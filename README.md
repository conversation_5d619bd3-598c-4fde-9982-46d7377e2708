<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-27 19:36:25
 * @FilePath: \new-wanda-park\parking-client-ui\README.md
 * @Description: {}
-->
# parking-client-ui

惠达万安岗亭值守系统

## 注意

1. 需要先全局安装 electron-builder

`npm install electron-builder -g`

1. 打包前先将 nsis.zip 解压，并放到目录，形如

C:\用户\A\AppData\Local\electron-builder\Cache\nsis\nsis-resources-3.4.1

## 浏览器开发模式

npm run dev

## 浏览器打包

npm run build

## Electron 开发模式

npm run electron:dev

## Electron 打包

npm run electron:build

npm run electron:publish
