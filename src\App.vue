<template>
  <el-config-provider :locale="locale">
    <div id="app">
      <RouterView />
    </div>
  </el-config-provider>
  <!-- 托管结束弹出框 -->

  <div class="dialbox">
    <!-- <el-dialog v-model="isok" title="温馨提示" width="500" @close="s_okendDialogVisible"> -->
    <el-dialog v-model="useOndutyStore.endDialogVisible" title="温馨提示" width="500" @close="s_okendDialogVisible">
      <div class="flex-col flex-center" style="color: #000">
        <div class="fw700">云端托管结束，请在本地管理车场！</div>
        <div v-if="useOndutyStore.cloudData.length" class="userShow">
          <div v-for="(i, index) in useOndutyStore.cloudData" :key="index">远程值守人员{{ index + 1 }}: {{ i.console_user_name }}</div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button type="primary" @click="s_okendDialogVisible"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { RouterView } from 'vue-router';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import ondutyStore from '@/stores/onduty.js'; //引入值班信息仓库
import { onMounted } from 'vue';
import { useIpcRenderer } from '@vueuse/electron';
import { useSetting } from '@/stores/setting';
import { ElMessage, ElMessageBox } from 'element-plus';
import { checkForUpdate } from '@/utils/appUpdate';
const isok = true;
const locale = zhCn;
const useOndutyStore = ondutyStore(); //使用仓库
const ipcRenderer = useIpcRenderer();
const setting = useSetting();

onMounted(() => {
  // 检查更新
  checkForUpdate(true);

  // 读取系统配置
  ipcRenderer.on('read-setting-reply', (event, res) => {
    if (res.success) {
      setting.$state = res.data;
    } else {
      ElMessage({
        message: res.message,
        type: 'error'
      });
    }
  });

  ipcRenderer.send('read-setting', '');

  // 关闭窗口
  ipcRenderer.on('close-window', (event, res) => {
    ElMessageBox.confirm('是否退出岗亭值守系统', '请确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
      .then(() => {
        ipcRenderer.send('close-window-reply', true);
      })
      .catch(() => {});
  });
});
//点击确定托管结束按钮
const s_okendDialogVisible = () => {
  useOndutyStore.endDialogVisible = false;
};
</script>
<style scoped lang="scss">
.flex-col {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.fw700 {
  font-weight: 700 !important;
  margin-bottom: 10px;
}
.userShow {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.dialbox {
  /* 新增弹框居中样式 */
  :deep(.el-overlay) {
    .el-overlay-dialog {
      display: flex;
      align-items: center;
      justify-content: center;
      .el-dialog {
        margin: 0 !important;
        position: relative;
        max-width: 95vw; /* 防止在小屏幕上溢出 */
      }
    }
  }
}
</style>
