<template>
  <el-card class="table" shadow="never">
    <div ref="tableRef">
      <el-table :data="table.data" border v-loading="table.loading">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" width="120">
          <template v-slot="scope">
            <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" />
        <el-table-column prop="collector_name" label="收费员" />
        <el-table-column prop="shift_name" label="班次名称" />
        <el-table-column prop="on_time" label="上班时间" />
        <el-table-column prop="off_time" label="下班时间" />
        <el-table-column prop="shift_time" label="交班时间" />
      </el-table>
      <el-pagination
        background
        :current-page="table.query.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="table.query.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="table.total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ShiftQueryTable" setup>
import { reactive, ref } from 'vue';
import { activeRouteTab } from '@/utils/tabKit';
import shiftQueryService from '@/service/watch/ShiftQueryService';
import parkService from '@/service/park/ParkService';
import { ElMessage } from 'element-plus';
import { useUser } from '@/stores/user';
const user = useUser();
const tableRef = ref();
const table = reactive({
  query: {
    page: 1,
    limit: 30
  },
  data: [],
  total: 0,
  loading: false
});

const list = (query) => {
  table.loading = true;

  table.query = Object.assign(table.query, query);
  shiftQueryService.pageShift(table.query).then((res) => {
    if (res.success) {
      table.data = res.data.rows;
      table.total = parseInt(res.data.total);
    } else {
      ElMessage({
        message: res.detail_message ? res.detail_message : res.message,
        type: 'error'
      });
    }
    table.loading = false;
  });
};

const handleSizeChange = (val) => {
  table.query.limit = val;
  list(table.query);
};

const handleCurrentChange = (val) => {
  table.query.page = val;
  list(table.query);
};

const handleDetail = (row) => {
  parkService.getParkInfo().then((response) => {
    const page_work_statistics = response.data.page_work_statistics;
    const list = page_work_statistics?.split(',').map((item) => parseInt(item, 10)) || [];
    if (!list.includes(user.role)) {
      ElMessage({
        message: '您无权限访问班次统计详情',
        type: 'error'
      });
    } else {
      activeRouteTab({
        path: '/watch/shiftDetail',
        query: {
          shift_record_id: row.id,
          collector_name: row.collector_name,
          on_time: row.on_time,
          off_time: row.off_time
        }
      });
    }
  });
};

defineExpose({
  list
});
</script>

<style lang="scss" scoped></style>
