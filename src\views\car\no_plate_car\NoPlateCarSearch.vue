<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.charge_id" placeholder="当班人" style="width: 100%" clearable>
        <el-option v-for="item in chargeIds" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="入场开始时间"
        end-placeholder="入场结束时间"
        :shortcuts="shortcuts"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.gateway_id" placeholder="进入道口" clearable>
        <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="NoPlateCarSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import parkService from '@/service/park/ParkService';
import homeService from '@/service/home/<USER>';
import { reactive, onMounted, ref } from 'vue';
import { useUser } from '@/stores/user';
import { getDefaultDateRange } from '@/utils/common';
import { dayjs } from 'element-plus';

const user = useUser();
const shortcuts = [
  {
    text: '当天',
    value: () => {
      return [dayjs().format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  },
  {
    text: '近3天',
    value: () => {
      return [dayjs().subtract(2, 'day').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  },
  {
    text: '近1周',
    value: () => {
      return [dayjs().subtract(6, 'day').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  },
  {
    text: '近1个月',
    value: () => {
      return [dayjs().subtract(1, 'months').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  }
];
const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    plate_no: undefined,
    gateway_id: undefined,
    charge_id: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});
const types = ref([]);
const chargeIds = ref([]);

onMounted(() => {
  form.dateRange = getDefaultDateRange(7);
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const gatewayParam = { region_id: user.park_region_id };
  parkService.listAllGateway(gatewayParam).then((response) => {
    types.value = response;
  });
  homeService.getShift({ page: 1, limit: 99 }).then((res) => {
    chargeIds.value = res.data.rows.map((item) => {
      return {
        value: item.id,
        label: item.name
      };
    });
  });
};

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    plate_no: undefined,
    gateway_id: undefined,
    charge_id: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  emits('form-reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
