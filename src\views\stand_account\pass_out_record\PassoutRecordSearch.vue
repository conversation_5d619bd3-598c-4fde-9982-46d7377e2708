<template>
    <FormSearch @search="handleDataSearch" @reset="handleAllReset">
        <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
        <form-search-item>
            <el-date-picker v-model="form.queryParams.start_time" type="datetime" style="width: 45%"
                placeholder="出场开始时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
            -
            <el-date-picker v-model="form.queryParams.end_time" type="datetime" style="width: 45%" placeholder="出场结束时间"
                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
        </form-search-item>
        <form-search-item>
            <el-select v-model="form.queryParams.out_type" style="width: 100%" placeholder="出场状态">
                <el-option v-for="item in out_typeList" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
        </form-search-item>
    </FormSearch>
</template>

<script name="PassoutRecordSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { getDefaultEndDateTime, getDefaultStartDateTime } from '@/utils/common';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

const emits = defineEmits(['form-search']);
const out_typeList = ref([
    { key: '正常离场', value: '1' },
    { key: '人工放行', value: '2' },
    { key: '特殊放行', value: '3' },
    { key: '匹配放行', value: '4' },
    { key: '冲卡离场', value: '1000' },
    { key: '折返事件', value: '5' },
    { key: '断网补录离场', value: '6' },
    { key: '跟车离场', value: '7' }
]);
const form = reactive({
    queryParams: {
        plate_no: undefined,
        out_type: undefined,
        start_time: '',
        end_time: '',
        page: 1,
        limit: 30
    }
});

onMounted(() => {
    form.queryParams.start_time = getDefaultStartDateTime(7);
    form.queryParams.end_time = getDefaultEndDateTime(7);
    handleDataSearch();
});

const handleDataSearch = () => {
    if (form.queryParams.start_time == null || form.queryParams.end_time == null) {
        ElMessage({
            message: '请选择出场时间',
            type: 'error'
        });
    } else {
        const param = {
            plate_no: form.queryParams.plate_no,
            start_time: form.queryParams.start_time,
            end_time: form.queryParams.end_time,
            out_type: form.queryParams.out_type,
            page: 1,
            limit: 30
        };
        const query = Object.assign(param, { page: 1, limit: 30 });
        emits('form-search', query);
    }
};
const handleAllReset = () => {
    form.queryParams = {
        plate_no: undefined,
        out_type: undefined,
        start_time: '',
        end_time: '',
        page: 1,
        limit: 30
    };
    const param = {
        plate_no: form.queryParams.plate_no,
        start_time: form.queryParams.start_time,
        end_time: form.queryParams.end_time,
        out_type: form.queryParams.out_type,
        page: 1,
        limit: 30
    };
    emits('form-reset', param);
};
</script>
<style lang="scss" scoped></style>
