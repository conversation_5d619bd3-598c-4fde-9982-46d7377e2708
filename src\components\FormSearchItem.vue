<template>
  <el-col :span="span" style="margin-top: 10px" v-show="visible">
    <slot></slot>
  </el-col>
</template>

<script>
export default {
  data() {
    return {
      span: 6,
      visible: !this.folded
    };
  },
  created() {
    const item = {
      show: this.show,
      hide: this.hide
    };
    this.emitter.$emit('addItem', item);
  },
  methods: {
    show() {
      this.visible = true;
    },
    hide() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-select) {
  width: 100%;
}
</style>
