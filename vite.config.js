import { fileURLToPath, URL } from 'node:url';

import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/sentry/park': {
        target: 'http://10.7.19.13:8083',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/sentry/, '/sentry')
      },
      '/sentry': {
        target: 'http://81.70.252.206',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/sentry/, '/sentry')
      }
    }
  }
});
