<template>
  <div class="container">
    <up-data-search @form-search="searchUpData" @form-reset="resetParamsAndData" />
    <up-data-table ref="table" />  
  </div>
</template>

<script setup name="UpData">
import UpDataSearch from './up_data/UpDataSearch.vue';
import UpDataTable from './up_data/UpDataTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchUpData = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
<style lang="scss" scoped></style>
