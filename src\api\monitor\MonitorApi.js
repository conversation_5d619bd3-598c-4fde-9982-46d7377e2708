import $ from '@/utils/axios';

/**
 * 可监控通道
 * @param {*} data
 * @returns
 */

//查询可监控通道
export const pageMonitorDevice = () => {
  return $({
    url: '/sentry/monitor/pageMonitorDevice',
    method: 'post'
  });
};

//手动开闸
export const liftRoll = (data) => {
  return $({
    url: '/sentry/liftRoll',
    method: 'post',
    data
  });
};

// 落杆测试
export const downRoll = (data) => {
  return $({
    url: '/sentry/operate/closeGate' + `?gatewayId=${data.gateway_id}`,
    method: 'get'
  });
};

// 音量调节
export const setVolume = (data) => {
  return $({
    url: `/sentry/operate/setVolume?volume=${data}`,
    method: 'get'
  });
};
