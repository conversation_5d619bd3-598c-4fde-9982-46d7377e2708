import $ from '@/utils/axios';

/**
 * 车辆信息
 * @param {*} data
 * @returns
 */

//查询计费模型
export const pageFeeModel = (data) => {
  return $({
    url: '/sentry/park/pageFeeModel',
    method: 'post',
    data
  });
};

//查询计费模型详情
export const getFeeModelById = (feeModelId) => {
  return $({
    url: '/sentry/park/getFeeModelById/' + feeModelId,
    method: 'get'
  });
};

//查询停车场基本信息
export const getParkInfo = () => {
  return $({
    url: '/sentry/park/getParkInfo',
    method: 'get'
  });
};

//查询黑名单
export const pageBlackList = (data) => {
  return $({
    url: '/sentry/park/pageBlackList',
    method: 'post',
    data
  });
};

//查询白名单
export const pageWhiteList = (data) => {
  return $({
    url: '/sentry/park/pageWhiteList',
    method: 'post',
    data
  });
};

//查询长租车信息
export const pageRentCar = (data) => {
  return $({
    url: '/sentry/park/pageRentCar',
    method: 'post',
    data
  });
};

//拦截规则
export const pageInterceptRules = (data) => {
  return $({
    url: '/sentry/park/pageInterceptRules',
    method: 'post',
    data
  });
};

//岗亭列表
export const listSentryByParkCode = (data) => {
  return $({
    url: '/sentry/auth/sentry/listSentryByParkCode',
    method: 'post',
    data
  });
};

//获取区域内所有通道
export const listAllGateway = (data) => {
  return $({
    url: '/sentry/park/listAllGateway',
    method: 'post',
    data
  });
};
