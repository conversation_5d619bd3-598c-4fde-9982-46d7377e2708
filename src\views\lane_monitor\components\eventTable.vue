<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-02-27 17:34:02
 * @LastEditTime: 2024-03-01 10:56:15
 * @LastEditors: 达万安 段世煜
 * @Description: 事件table组件
 * @FilePath: \parking-client-ui\src\views\lane_monitor\components\eventTable.vue
-->
<template>
  <el-card class="table" shadow="never">
    <div>
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange" ref="tableRef">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column type="index" label="序号" align="center" width="60" />
        <el-table-column prop="event_time" label="时间" align="center" min-width="160" />
        <el-table-column prop="event_level" label="事件等级" align="center">
          <template v-slot="scope">
            <el-tag effect="dark" :type="scope.row.event_level === '报警' ? 'danger' : 'warning'">{{ scope.row.event_level }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="event_type" label="事件类型" align="center" />
        <el-table-column prop="action" label="查看" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="primary" @click="handelViewPic(scope.row)"> 详情 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        v-model:current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        v-model:page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.queryParams.total"
        class="table-pagination"
        @size-change="handleSizeOrCurrentChange"
        @current-change="handleSizeOrCurrentChange"
      />
    </div>
  </el-card>
  <!-- 图片预览组件 -->
  <el-image-viewer v-if="showImagePreview" hide-on-click-modal @close="() => (showImagePreview = false)" :url-list="imgPreviewList" />
</template>

<script name="DeviceTable" setup>
import { reactive, ref, watch, onMounted, nextTick } from 'vue';
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';
import { getEventList, exportImageFile, exportListFile } from '@/api/lane_monitor/laneMonitorApi';
import { useSetting } from '@/stores/setting';
import { storeToRefs } from 'pinia';
import { useIpcRenderer } from '@vueuse/electron';
// 渲染进程回调 监听文件是否处理成功
const ipcRenderer = useIpcRenderer();
ipcRenderer.on('open-export-file-fail', (event, data) => {
  ElMessage.error(data);
});
// 查询数据
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    total: 0,
    camera_id: ''
  }
});
// 表格数据
const tableData = ref([]);
// 表格实体
const tableRef = ref();
// 表格加载状态
const loading = ref(false);
// camera_id
const setting = useSetting();
const { lane_monitor } = storeToRefs(setting);

onMounted(() => {
  // 数据初始化
  nextTick(() => {
    initData(lane_monitor.value);
  });
});

// 监听lane_monitor变化，数据初始化
watch(lane_monitor, (val) => {
  initData(val);
});

/**
 * @description: 初始化数据
 * @param {*} val camera_id
 */
const initData = (val) => {
  // 设置data.queryParams.camera_id的值为val
  data.queryParams.camera_id = val;
  // 调用getList函数，传入data.queryParams参数
  getList(data.queryParams);
};

/**
 * @description: 获取表格数据
 * @param {*} params 表格入参
 * @param {*} init 是否初始化
 */
const getList = async (params, init = false) => {
  // 初始化
  if (init) {
    // 设置页面和每页条数
    data.queryParams.page = 1;
    data.queryParams.limit = 30;
    // 清空已选择行
    selectedRows.value = [];
  }
  // 加载中
  loading.value = true;
  // 设置查询参数
  data.queryParams = { ...data.queryParams, ...params };
  try {
    // 获取事件列表
    const { data: resData, message, success } = await getEventList(data.queryParams);
    if (success) {
      // 设置表格数据
      tableData.value = resData.rows;
      // 设置总条数
      data.queryParams.total = parseInt(resData.total);
    } else {
      // 弹出错误信息
      ElMessage({
        message: message,
        type: 'error'
      });
    }
  } catch (error) {
    // 弹出错误信息
    ElMessage({
      message: error,
      type: 'error'
    });
  } finally {
    // 加载完成
    loading.value = false;
  }
};

// 图片预览展示标识
const showImagePreview = ref(false);
// 图片预览列表
const imgPreviewList = ref([]);
/**
 * @description: 图片预览
 * @param {*} val 源数据
 */
const handelViewPic = (val) => {
  if (!val.event_data) {
    ElMessage.warning(`当前${val.event_level}暂无图片`);
    return;
  }
  const base = 'data:image/jpg;base64,';
  let imageUrl = val.event_data;
  if (val.event_data.indexOf(base) === -1) {
    imageUrl = base + val.event_data;
  }
  imgPreviewList.value = [imageUrl];
  showImagePreview.value = true;
};
/**
 * @description: 分页
 */
const handleSizeOrCurrentChange = () => {
  getList(data.queryParams);
  selectedRows.value = [];
};
// 多选选择行
const selectedRows = ref([]);
/**
 * @description: 选择行
 * @param {*} val 当前选择值数组
 */
const handleSelectionChange = (val) => {
  selectedRows.value = val;
};
// 导出加载状态
const exportLoading = ref();
/**
 * @description: 打开loading
 */
const openLoading = () => {
  exportLoading.value = ElLoading.service({
    lock: true,
    text: '正在导出，请稍后',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
/**
 * @description: 导出图片
 */
const exportImage = async () => {
  if (selectedRows.value.length) {
    openLoading();
    const ids = selectedRows.value.map((item) => {
      return item.id;
    });
    try {
      const { data: resData, success, message } = await exportImageFile({ camera_id: data.queryParams.camera_id, ids: ids.join(',') });
      if (success) {
        // 清空已选择行
        selectedRows.value = [];
        tableRef.value.clearSelection();
        // 处理导出数据
        dealFile(resData);
      } else {
        ElMessage({
          message: message,
          type: 'error'
        });
      }
    } finally {
      exportLoading.value.close();
    }
  } else {
    ElMessage({
      message: '请选择需要下载的图片',
      type: 'warning'
    });
  }
};
/**
 * @description: 导出列表
 */
const exportList = async () => {
  if (data.queryParams.start_time && data.queryParams.end_time) {
    openLoading();
    const params = {
      camera_id: data.queryParams.camera_id,
      start_time: data.queryParams.start_time,
      end_time: data.queryParams.end_time,
      event_type: data.queryParams.event_type
    };
    try {
      const { data: resData, success, message } = await exportListFile(params);
      if (success) {
        // 处理导出数据
        dealFile(resData);
      } else {
        ElMessage({
          message: message,
          type: 'error'
        });
      }
    } finally {
      exportLoading.value.close();
    }
  } else {
    ElMessage({
      message: '请选择需要导出报表的时间段并执行查询操作',
      type: 'warning'
    });
  }
};
/**
 * @description: 处理导出数据 在文件系统中打开
 * @param {*} url 文件绝对路径
 */
const dealFile = (url) => {
  if (!url) {
    ElMessage.error('未获取到下载文件');
    return;
  }
  ElMessageBox.confirm('文件已导出至本地，是否需要在文件夹中打开文件？', '提示', {
    confirmButtonText: '在文件夹中显示',
    cancelButtonText: '取消',
    type: 'success'
  }).then(() => {
    const urlUse = url.replaceAll('/', '\\');
    ipcRenderer.send('open-export-file', urlUse);
  });
};

defineExpose({
  getList,
  exportImage,
  exportList
});
</script>
<style lang="scss" scoped></style>
