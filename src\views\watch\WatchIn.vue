<template>
  <el-row :gutter="10" class="watch-in">
    <el-col :span="5">
      <div class="title">入场图片</div>
      <el-image :src="state.capture.pic_path_url" fit="fill" @error="refresh" @click="showImage(state.capture.pic_path_url)">
        <template #error>
          <div class="image-slot">
            <el-icon>
              <Picture />
            </el-icon>
          </div>
        </template>
      </el-image>
    </el-col>
    <el-col :span="8">
      <el-row :gutter="16">
        <el-col :span="8" class="label">
          <div class="title">车辆信息</div>
        </el-col>
        <el-col :span="16" class="value">
          <div class="title" style="font-size: 24px; font-weight: 600; position: relative; top: -10%">
            {{ state.plate_no ? state.plate_no : '暂无车辆' }}
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="16" v-if="state.memo != null && state.memo != undefined">
        <el-col :span="24" style="color: #096dd9; height: 22px; line-height: 22px">
          <div class="title">
            <el-alert :title="state.memo" type="success" :closable="false" />
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="8" class="label">
          <div>入场时间</div>
        </el-col>
        <el-col :span="16" class="value">
          <span>{{ state.capture.event_time }}</span>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="8" class="label">
          <span>车型</span>
        </el-col>
        <el-col :span="16" class="value">
          <span>{{ state.capture.car_type_desc }}</span>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>进入道口</div>
            </el-col>
            <el-col :span="16" class="value">
              <span>{{ state.capture.gateway_name }}</span>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-button type="primary" @click="handleUpdatePlateNo(state.plate_no)" :disabled="state.plate_no === undefined"> 修改车牌号 </el-button>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="operate-button">
        <el-col :span="5">
          <el-button type="primary" style="height: 100px; width: 100%; font-size: 18px" @click="handleManualCapture">手动抓拍</el-button>
        </el-col>
        <el-col :span="19">
          <div>
            <el-button
              type="primary"
              @click="handleAllowPass"
              style="height: 46px; width: 100%; font-size: 15px"
              :disabled="state.plate_no === undefined"
            >
              允许入场
            </el-button>
          </div>
          <div style="margin-top: 8px">
            <el-button
              type="primary"
              @click="handleCancelPass"
              style="height: 46px; width: 100%; font-size: 15px"
              :disabled="state.plate_no === undefined"
            >
              取消入场
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-col>
    <el-col :span="11">
      <el-tabs type="border-card" class="table-card">
        <el-tab-pane label="入场记录">
          <el-table :data="state.inRecords" border style="height: calc(100vh - 308px)">
            <el-table-column prop="plate_no" label="车牌号" align="center">
              <template v-slot="scope">
                {{ scope.row.plate_no }}
              </template>
            </el-table-column>
            <el-table-column prop="in_time" label="入场时间" align="center" width="160" />
            <el-table-column prop="gateway_name" label="入口" align="center" />
            <el-table-column prop="out_state_desc" label="出场状态" align="center" />
            <el-table-column prop="park_region_name" label="区域" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="出场记录">
          <el-table :data="state.outRecords" border style="height: calc(100vh - 308px)">
            <el-table-column prop="plate_no" label="车牌号" align="center">
              <template v-slot="scope">
                {{ scope.row.plate_no }}
              </template>
            </el-table-column>
            <el-table-column prop="out_time" label="出场时间" align="center" width="160" />
            <el-table-column prop="gateway_name" label="出口" align="center" />
            <el-table-column prop="park_region_name" label="区域" align="center" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-col>
  </el-row>
  <el-dialog v-model="imageDialogVisible" title="查看图片">
    <img w-full :src="dialogImageUrl" style="max-width: 100%; max-height: 100%" alt="Preview Image" />
  </el-dialog>
  <!-- 修改车牌号 -->
  <el-dialog v-model="plateNoDialogVisible" title="修改车牌号" width="450px" :before-close="handlePlateNoClose" :close-on-click-modal="false">
    <el-form :model="plateNo.form" label-position="top">
      <el-form-item label="车牌号" class="required">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-select v-model="plateNo.form.plateNoFirst" filterable style="width: 100%" disabled v-if="plateNo.noPlateCarFlag == '1'">
              <el-option v-for="item in plateNoFirsts" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
            <el-select v-else v-model="plateNo.form.plateNoFirst" filterable style="width: 100%">
              <el-option v-for="item in plateNoFirsts" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-input
              type="text"
              v-model="plateNo.form.plateNoSecond"
              show-word-limit
              maxlength="10"
              style="width: 100%"
              disabled
              v-if="plateNo.noPlateCarFlag == '1'"
            />
            <el-input type="text" v-model="plateNo.form.plateNoSecond" show-word-limit maxlength="10" style="width: 100%" v-else />
          </el-col>
          <el-col :span="6">
            <el-select v-model="plateNo.form.plateNoThirdly" filterable style="width: 100%" disabled v-if="plateNo.noPlateCarFlag == '1'">
              <el-option v-for="item in plateNoThirdlies" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
            <el-select v-model="plateNo.form.plateNoThirdly" filterable style="width: 100%" v-else>
              <el-option v-for="item in plateNoThirdlies" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-checkbox
              v-model="plateNo.noPlateCarFlag"
              true-label="1"
              false-label="0"
              @change="changeToNoPlate"
              style="display: inline; margin-top: 10px"
            >
              无牌车
            </el-checkbox>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <div style="margin-top: 30px; text-align: center">
      <el-space>
        <el-button type="primary" @click="submitPlateNo()">确 认</el-button>
        <el-button @click="resetPlateNo()">重 置</el-button>
      </el-space>
    </div>
  </el-dialog>
</template>

<script name="WatchIn" setup>
import { reactive, ref, onMounted, onUnmounted, getCurrentInstance } from 'vue';
import watchService from '@/service/watch/WatchService';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useSetting } from '@/stores/setting';
import { useUser } from '@/stores/user';

const props = defineProps({
  gateway: {
    type: Object,
    required: true
  }
});

const setting = useSetting();
const user = useUser();
// 刷新图片的次数
const refreshTimes = ref(0);

const plateNoDialogVisible = ref(false);
const plateNoFirsts = ref([
  { label: '京' },
  { label: '冀' },
  { label: '晋' },
  { label: '蒙' },
  { label: '辽' },
  { label: '吉' },
  { label: '黑' },
  { label: '沪' },
  { label: '苏' },
  { label: '浙' },
  { label: '皖' },
  { label: '闽' },
  { label: '赣' },
  { label: '鲁' },
  { label: '豫' },
  { label: '鄂' },
  { label: '湘' },
  { label: '粤' },
  { label: '桂' },
  { label: '琼' },
  { label: '渝' },
  { label: '川' },
  { label: '贵' },
  { label: '云' },
  { label: '藏' },
  { label: '陕' },
  { label: '甘' },
  { label: '青' },
  { label: '宁' },
  { label: '新' },
  { label: '民航' },
  { label: '使' },
  { label: '无' }
]);
const plateNoThirdlies = ref([
  { label: '警' },
  { label: '学' },
  { label: '使' },
  { label: '领' },
  { label: '挂' },
  { label: '应急' },
  { label: '无' }
]);
const plateNo = reactive({
  form: {
    plateNoFirst: undefined,
    plateNoSecond: undefined,
    plateNoThirdly: undefined
  },
  noPlateCarFlag: '0'
});

const state = reactive({
  // 当前车牌号
  plate_no: undefined,
  // 抓拍记录
  capture: {
    car_color: undefined,
    car_direction: undefined,
    car_type: undefined,
    car_type_desc: undefined,
    device_direction: undefined,
    event_time: undefined,
    gateway_device_id: undefined,
    gateway_direction: undefined,
    gateway_id: undefined,
    gateway_name: undefined,
    ip: undefined,
    pic_path: undefined,
    pic_path2: undefined,
    pic_path_url: undefined,
    pic_path2_url: undefined,
    plate_no: undefined,
    take_direction: undefined,
    trigger_time: undefined,
    trigger_type: undefined
  },
  // 车辆最近一条入场记录
  lastParkInRecord: {
    id: undefined,
    car_in_biz_no: undefined,
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    park_region_name: undefined,
    gateway_id: undefined,
    gateway_name: undefined,
    in_time: undefined,
    plate_no: undefined,
    car_type: undefined,
    car_type_desc: undefined,
    car_color: undefined,
    car_photo: '',
    car_photo_cloud_file_id: undefined,
    car_photo_sync_to_cloud: undefined,
    out_state: undefined,
    in_type: undefined,
    car_photo_url: ''
  },
  // 业务流水号
  car_biz_no: undefined,
  // 备注
  memo: undefined,
  // 入场记录
  inRecords: [],
  // 出场记录
  outRecords: []
});

const imageDialogVisible = ref(false);
const dialogImageUrl = ref(undefined);
const showImage = (val) => {
  dialogImageUrl.value = val;
  imageDialogVisible.value = true;
};

onMounted(() => {
  // 添加WebSocket通知监听
  window.addEventListener('onmessage', handleWebSocketMessage);
});

onUnmounted(() => {
  window.removeEventListener('onmessage', handleWebSocketMessage);
});

// 处理WebSocket消息
const handleWebSocketMessage = (msg) => {
  if (msg.detail.command === 's2c.car-event' && parseInt(msg.detail.capture_event.gateway_id) === parseInt(props.gateway.id)) {
    clearData();
    autoCapture(msg.detail);
  } else if (msg.detail.command === 's2c.car-in-abnormal-event' && parseInt(msg.detail.gateway_id) === parseInt(props.gateway.id)) {
    handleAbnormalEvent(msg.detail);
  }
};

const parent = getCurrentInstance().parent;
const handleAbnormalEvent = (detail) => {
  ElMessageBox.alert('<div style="word-wrap: break-word;color: red;width: 390px;text-align: center;">' + detail.msg + '</div>', '提示', {
    confirmButtonText: '确定',
    dangerouslyUseHTMLString: true,
    appendTo: parent.proxy.$el
  });
};
// 处理图片加载错误再次请求
const refresh = (e) => {
  if (state.capture.pic_path_url === undefined) return;
  if (refreshTimes.value > 10) return;
  refreshTimes.value++;
  console.log('refreshTimes----', refreshTimes.value);
  setTimeout(() => {
    const url = state.capture.pic_path_url.split('?')[0];
    state.capture.pic_path_url = `${url}?version=${Date.now()}`;
  }, 1000);
};

// 自动抓拍(WebSocket)
const autoCapture = (msg) => {
  console.log('msg---------', msg);
  refreshTimes.value = 0;
  // 车辆进入通道
  state.capture = msg.capture_event;
  state.capture.pic_path_url = msg.pic_path_url;
  state.capture.pic_path2_url = msg.pic_path2_url;

  state.car_biz_no = msg.car_biz_no;
  state.memo = msg.memo;

  state.plate_no = msg.capture_event.plate_no;

  // 查询入场记录
  queryParkInRecord();

  // 查询出场记录
  queryParkOutRecord();

  // 查询车场最近入场记录
  queryLastParkInRecord();

  // 自动入场，刷新车场统计信息
  // statParkStatus();

  // 压测模式
  if (setting.pressure_test) {
    const param = {
      car_biz_no: msg.car_biz_no,
      lift_roll: true
    };
    watchService.manualPassIn(param).then((res) => {
      if (res.success) {
        ElMessage({
          message: '允许入场成功',
          type: 'success'
        });

        clearData();
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  }
};

// 修正入场信息
const handleUpdatePlateNo = (plate_no) => {
  // 查询最近一条入场记录
  queryLastParkInRecord();

  //普通
  var c_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNOPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
  //特种
  var ts_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{4}[学挂领试超练警]{1}$/u;
  //武警
  var wj_reg = /^WJ[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]?[0-9A-Z]{5}$/iu;
  //军牌
  var j_reg = /^[QVKHBSLJNGCEZ]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
  //新能源
  // 小型车
  var xs_reg =
    /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[DF]{1}[1-9ABCDEFGHJKLMNPQRSTUVWXYZ]{1}[0-9]{4}$/u;

  // 大型车
  var xb_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9]{5}[DF]{1}$/u;
  //民航
  var mh_reg = /^民航[0-9A-Z]{5}$/u;
  //使馆
  var s_reg = /^[1-3]{1}[0-9]{2}[0-9A-Z]{3}使$/u;
  var s1_reg = /^使[0-9]{6}$/u;
  //领馆
  var l_reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[1-3]{1}[0-9]{2}[0-9A-Z]{2}领$/u;
  //应急
  var yj_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[0-9A-Z]{5}应急$/u;
  //判断并进行拆分
  if (c_reg.test(plate_no) || xs_reg.test(plate_no) || xb_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1);
    plateNo.form.plateNoThirdly = '无';
  }
  if (ts_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1, plate_no.length - 1);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 1);
  }
  if (wj_reg.test(plate_no) || j_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = '无';
    plateNo.form.plateNoSecond = plate_no;
    plateNo.form.plateNoThirdly = '无';
  }
  if (mh_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 2);
    plateNo.form.plateNoSecond = plate_no.substring(2);
    plateNo.form.plateNoThirdly = '无';
  }
  if (s_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = '无';
    plateNo.form.plateNoSecond = plate_no.substring(0, plate_no.length - 1);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 1);
  }
  if (s1_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1);
    plateNo.form.plateNoThirdly = '无';
  }
  if (l_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1, plate_no.length - 1);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 1);
  }
  if (yj_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1, plate_no.length - 2);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 2);
  }
  plateNo.noPlateCarFlag = '0';
  plateNoDialogVisible.value = true;
};
//修改车牌号提交
const submitPlateNo = () => {
  if (plateNo.noPlateCarFlag == '0') {
    //有车牌车
    if (plateNo.form.plateNoFirst == undefined) {
      ElMessage({
        message: '请输入车牌号！',
        type: 'error'
      });
      return;
    }

    if (plateNo.form.plateNoSecond == undefined) {
      ElMessage({
        message: '请输入车牌号！',
        type: 'error'
      });
      return;
    }

    if (plateNo.form.plateNoThirdly == undefined) {
      ElMessage({
        message: '请输入车牌号！',
        type: 'error'
      });
      return;
    }
    var regex = /^[0-9A-Z]+$/;
    if (!regex.test(plateNo.form.plateNoSecond)) {
      ElMessage({
        message: '请输入正确的车牌号！',
        type: 'error'
      });
      return;
    }
    const param = {
      first_no: plateNo.form.plateNoFirst,
      mid_no: plateNo.form.plateNoSecond,
      last_no: plateNo.form.plateNoThirdly,
      car_in_biz_no: state.car_biz_no,
      id: state.lastParkInRecord.id,
      no_plate_car: 0,
      gateway_id: props.gateway.id
    };
    var plate_no = '';
    if (plateNo.form.plateNoFirst != '无' && plateNo.form.plateNoThirdly != '无') {
      plate_no = plateNo.form.plateNoFirst + plateNo.form.plateNoSecond + plateNo.form.plateNoThirdly;
    }
    if (plateNo.form.plateNoFirst == '无' && plateNo.form.plateNoThirdly != '无') {
      plate_no = plateNo.form.plateNoSecond + plateNo.form.plateNoThirdly;
    }
    if (plateNo.form.plateNoThirdly == '无' && plateNo.form.plateNoFirst != '无') {
      plate_no = plateNo.form.plateNoFirst + plateNo.form.plateNoSecond;
    }
    if (plateNo.form.plateNoFirst == '无' && plateNo.form.plateNoThirdly == '无') {
      plate_no = plateNo.form.plateNoSecond;
    }
    watchService.modifyPlateNo(param).then((res) => {
      if (res.success) {
        ElMessage({
          type: 'success',
          message: '修改车牌号成功'
        });
        state.plate_no = plate_no;
        plateNoDialogVisible.value = false;
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  } else {
    //无牌车
    const plateParam = {
      park_id: state.lastParkInRecord.park_id
    };
    watchService.generateCarNoPlateNo(plateParam).then((res) => {
      if (res.success) {
        updatePlateNo(res.data.plate_no);
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  }
};

const updatePlateNo = (plate_no) => {
  const param = {
    first_no: plateNo.form.plateNoFirst,
    mid_no: plate_no,
    last_no: plateNo.form.plateNoThirdly,
    id: state.lastParkInRecord.id,
    no_plate_car: 1,
    gateway_id: props.gateway.id
  };
  watchService.modifyPlateNo(param).then((res) => {
    if (res.success) {
      ElMessage({
        type: 'success',
        message: '修改车牌号成功'
      });
      state.plate_no = plate_no;
      plateNoDialogVisible.value = false;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
//重置
const resetPlateNo = () => {
  plateNo.form = {
    plateNoFirst: undefined,
    plateNoSecond: undefined,
    plateNoThirdly: undefined
  };
  plateNo.noPlateCarFlag = '0';
};
const changeToNoPlate = (val) => {
  if (val == '1') {
    plateNo.form = {
      plateNoFirst: undefined,
      plateNoSecond: undefined,
      plateNoThirdly: undefined
    };
  }
};
const handlePlateNoClose = () => {
  plateNoDialogVisible.value = false;
};

// 查询最近一条入场记录
const queryLastParkInRecord = () => {
  if (state.plate_no === undefined) {
    return;
  }

  const param = {
    park_region_id: props.gateway.park_region_id,
    plate_no: state.plate_no
  };
  watchService.queryLastParkInRecord(param).then((res) => {
    if (res.success) {
      if (res.data) {
        state.lastParkInRecord = res.data;
        state.car_type = res.data.car_type;
        state.pre_car_type = res.data.car_type;
      }
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 查询入场记录
const queryParkInRecord = () => {
  if (state.plate_no === undefined) {
    return;
  }

  const param = {
    park_region_id: props.gateway.park_region_id,
    plate_no: state.plate_no,
    topn: 10,
    has_plate_no: false
  };
  if (state.plate_no != undefined) {
    param.has_plate_no = true;
  }
  watchService.queryParkInRecord(param).then((res) => {
    if (res.success) {
      state.inRecords = res.data;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 查询出场记录
const queryParkOutRecord = () => {
  if (state.plate_no === undefined) {
    return;
  }

  const param = {
    park_region_id: props.gateway.park_region_id,
    plate_no: state.plate_no,
    topn: 10
  };
  watchService.queryParkOutRecord(param).then((res) => {
    if (res.success) {
      state.outRecords = res.data;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 手动抓拍
const handleManualCapture = () => {
  const param = {
    gateway_id: props.gateway.id,
    direction: props.gateway.type
  };
  watchService.manualCapture(param).then((res) => {
    if (res.success) {
      ElMessage({
        message: res.message,
        type: 'success'
      });
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 允许入场
const handleAllowPass = () => {
  ElMessageBox.confirm('是否允许入场？', '入场', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  })
    .then(() => {
      const param = {
        car_biz_no: state.car_biz_no,
        lift_roll: true
      };
      watchService.manualPassIn(param).then((res) => {
        if (res.success) {
          ElMessage({
            message: '允许入场成功',
            type: 'success'
          });

          clearData();
        } else {
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
            type: 'error'
          });
        }
      });

      // 允许入场，刷新车场统计信息
      // statParkStatus();
    })
    .catch(() => {});
};

// 取消入场
const handleCancelPass = () => {
  if (state.plate_no == null || state.plate_no == undefined) {
    ElMessage({
      message: '无车牌信息，不允许取消入场',
      type: 'warning'
    });
    return;
  }

  ElMessageBox.confirm('是否需要取消入场？', '取消入场', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  })
    .then(() => {
      const param = {
        gateway_id: props.gateway.id,
        direction: state.capture.gateway_direction,
        plate_no: state.plate_no
      };
      watchService.cancelPass(param).then((res) => {
        if (res.success) {
          ElMessage({
            message: '取消入场成功',
            type: 'success'
          });

          clearData();
        } else {
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
            type: 'error'
          });
        }
      });
    })
    .catch(() => {});
};

// 清空数据
const clearData = () => {
  // 当前车牌号
  state.plate_no = undefined;
  // 抓拍记录
  state.capture = {
    car_color: undefined,
    car_direction: undefined,
    car_type: undefined,
    car_type_desc: undefined,
    device_direction: undefined,
    event_time: undefined,
    gateway_device_id: undefined,
    gateway_direction: undefined,
    gateway_id: undefined,
    gateway_name: undefined,
    ip: undefined,
    pic_path: undefined,
    pic_path2: undefined,
    plate_no: undefined,
    take_direction: undefined,
    trigger_time: undefined,
    trigger_type: undefined
  };
  // 车辆最近一条入场记录
  state.lastParkInRecord = {
    id: undefined,
    car_in_biz_no: undefined,
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    park_region_name: undefined,
    gateway_id: undefined,
    gateway_name: undefined,
    in_time: undefined,
    plate_no: undefined,
    car_type: undefined,
    car_type_desc: undefined,
    car_color: undefined,
    car_photo: '',
    car_photo_cloud_file_id: undefined,
    car_photo_sync_to_cloud: undefined,
    out_state: undefined,
    in_type: undefined,
    car_photo_url: ''
  };
  // 业务流水号
  state.car_biz_no = undefined;
  // 备注
  state.memo = undefined;
  // 入场记录
  state.inRecords = [];
  // 出场记录
  state.outRecords = [];
};

// 刷新应收和实收、空闲车位
// const statParkStatus = () => {
//     const shift_record_id = user.shift_handover_record_id;
//     watchService.statParkStatus(shift_record_id).then((res) => {
//         if (res.success) {
//             user.total_money = res.data.total_money;
//             user.payed_money = res.data.payed_money;
//             user.free_space = res.data.free_space;
//             user.space_adjust = res.data.space_adjust;
//         } else {
//             ElMessage({
//                 dangerouslyUseHTMLString: true,
//                 message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
//                 type: 'error'
//             });
//         }
//     });
// };
</script>

<style lang="scss" scoped>
.watch-in {
  height: calc(100vh - 246px);
}
.title {
  color: #096dd9;
}
.el-image {
  margin: 12px 0px;
  width: 100%;
  height: calc((100vh - 350px) / 2 * 0.56);
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}

.image-slot .el-icon {
  font-size: 26px;
}

.label {
  display: inline-flex;
  justify-content: flex-end;
  align-items: flex-start;
  flex: 0 0 auto;
  font-size: var(--el-form-label-font-size);
  color: var(--el-text-color-regular);
  line-height: 30px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  width: 80px;
  text-align: right;
}
.value {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
  line-height: 30px;
  position: relative;
  font-size: var(--font-size);
  min-width: 0;
}

.el-input__wrapper {
  border: none;
}

.operate-button {
  margin-top: 10px;
  margin-left: 5px;
}

.table-card {
  height: calc(100vh - 248px);
}
</style>
