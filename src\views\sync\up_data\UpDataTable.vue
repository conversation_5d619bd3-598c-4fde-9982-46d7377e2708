<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <!-- <el-table-column prop="action" label="操作" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="primary"> 重新上传 </el-button>
          </template>
        </el-table-column> -->
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="park_name" label="车场名" align="center" />
        <el-table-column prop="object_id" label="待上传对象" align="center" />
        <el-table-column prop="object_type" label="待上传类型" align="center" />
        <el-table-column prop="object_version" label="版本号" align="center" />
        <el-table-column label="内容" align="center" min-width="200">
          <template v-slot="scope">
            <el-button link @click="handleDetail(scope.row)" type="info"> {{ scope.row.content.substring(0, 30) + '......' }} </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="memo" label="备注" align="center" />
        <el-table-column prop="upload_state_desc" label="状态" align="center" />
        <el-table-column prop="upload_time" label="上传时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

      <el-drawer v-model="drawerVisible" :with-header="false" size="40%">
        <el-card>
          <div class="desc">
            <span class="label">车场名:</span>
            <span class="value">{{ data.sysLog.park_name }}</span>
          </div>
          <div class="desc">
            <span class="label">待上传对象：</span>
            <span class="value">{{ data.sysLog.object_id }}</span>
          </div>
          <div class="desc">
            <span class="label">待上传类型：</span>
            <span class="value">{{ data.sysLog.object_type }}</span>
          </div>
          <div class="desc">
            <span class="label">版本号：</span>
            <span class="value">{{ data.sysLog.object_version }}</span>
          </div>
          <div class="desc">
            <span class="label">内容：</span>
            <span class="value">{{ data.sysLog.content }}</span>
          </div>
          <div class="desc">
            <span class="label">备注：</span>
            <span class="value">{{ data.sysLog.memo }}</span>
          </div>
          <div class="desc">
            <span class="label">状态：</span>
            <span class="value">{{ data.sysLog.upload_state_desc }}</span>
          </div>
          <div class="desc">
            <span class="label">上传时间：</span>
            <span class="value">{{ data.sysLog.upload_time }}</span>
          </div>
        </el-card>
        <template #footer>
          <div style="flex: auto">
            <el-button @click="closeDrawer">关 闭</el-button>
          </div>
        </template>
      </el-drawer>
    </div>
  </el-card>
</template>

<script name="UpDataTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import syncService from '@/service/sync/SyncService';
import { getDefaultDateRange } from '@/utils/common';

const tableData = ref([]);
const total = ref(0);
const loading = ref(false);
const drawerVisible = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    start_time: '',
    end_time: ''
  },
  sysLog: {}
});
onMounted(() => {
  data.dateRange = getDefaultDateRange(7);
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.start_time === '' ? (params.start_time = data.dateRange[0]) : (params.start_time = data.queryParams.start_time);
  params.end_time === '' ? (params.end_time = data.dateRange[1]) : (params.end_time = data.queryParams.end_time);
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  syncService.pageUploadData(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const handleDetail = (val) => {
  data.sysLog = val;
  drawerVisible.value = true;
};
const closeDrawer = () => {
  drawerVisible.value = false;
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.desc {
  line-height: 36px;
}
.label {
  color: rgba(0, 0, 0, 0.65);
  padding-left: 10px;
  padding-right: 10px;
}

.value {
  color: rgba(0, 0, 0, 0.8);
  width: auto;
}
</style>
