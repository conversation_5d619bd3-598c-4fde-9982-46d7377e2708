import { defineStore } from 'pinia';

export const useSetting = defineStore('setting', {
  id: 'setting',
  state: () => {
    return {
      terminal_code: undefined,
      terminal_auth_code: undefined,
      sentry_id: undefined,
      sentry_name: undefined,
      park_code: undefined,
      agent_ip: undefined,
      agent_port: undefined,
      pressure_test: false,
      lane_monitor: undefined
    };
  },
  actions: {
    setSelected(id) {
      this.lane_monitor = id;
    }
  },
  persist: {
    enable: true
  }
});
