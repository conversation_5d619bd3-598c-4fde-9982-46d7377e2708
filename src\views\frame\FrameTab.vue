<template>
  <div class="frame-tab">
    <div class="tab-container">
      <div v-for="(tag, index) in tabList" :key="index" class="tab" :class="index === activedTabIndex ? 'active' : ''" @click="clickTab(tag, index)">
        {{ tag.title }}
        <el-icon v-if="tabList.length > 1 && !(tag.title === '岗亭值守' && user.role === 1)" :size="14" @click.stop="closeTab(index)" class="close">
          <Close />
        </el-icon>
      </div>
    </div>
    <el-dropdown>
      <div class="more">
        <el-icon :size="13">
          <ArrowDown />
        </el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="closeCurrentTab">关闭当前标签</el-dropdown-item>
          <el-dropdown-item @click="closeOtherTab">关闭其他标签</el-dropdown-item>
          <el-dropdown-item @click="closeAllTab">关闭全部标签</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script name="FrameTab" setup>
import { computed, onMounted } from 'vue';
import { useTab } from '@/stores/tab';
import router from '@/router';
import { ElMessage } from 'element-plus';
import { activeRouteTab } from '@/utils/tabKit';
import { useMenu } from '@/stores/menu';
import { useUser } from '@/stores/user';

const tab = useTab();
const menu = useMenu();
const user = useUser();

const tabList = computed({
  get() {
    return tab.state.tabList;
  },
  set(val) {
    tab.state.tabList = val;
  }
});

const activedTabIndex = computed({
  get() {
    return tab.state.activedTabIndex;
  },
  set(val) {
    tab.state.activedTabIndex = val;
  }
});

const clickTab = (tag, index) => {
  tab.state.activedTabIndex = index;
  menu.state.activedMenuIndex = tag.id;

  router.push(tag.path);
};

const closeTab = (index) => {
  if (tab.state.tabList.length >= 1) {
    // 当前选中 tab 被删除
    tab.state.tabList.splice(index, 1);
    // 设置路由
    router.push({ name: tab.state.tabList[tab.state.tabList.length - 1].name });
    // 设置选中下标
    tab.state.activedTabIndex = tab.state.tabList.length - 1;
  } else {
    router.push('/');
  }
};

// 关闭当前标签
const closeCurrentTab = () => {
  if (tab.state.tabList.length === 1 || (tab.state.tabList[tab.state.activedTabIndex].title === '岗亭值守' && user.role === 1)) {
    ElMessage({
      message: '当前标签页不允许关闭',
      type: 'warning'
    });
    return;
  }
  closeTab(tab.state.activedTabIndex);
};

// 关闭其它标签
const closeOtherTab = () => {
  tab.state.tabList = [tab.state.tabList[tab.state.activedTabIndex]];
  tab.state.activedTabIndex = 0;
};

// 关闭全部标签
const closeAllTab = () => {
  tab.state.tabList = [];
  activeRouteTab({ path: '/' });
};
</script>

<style lang="scss" scoped>
.frame-tab {
  padding: 6px 6px;
  width: 100%;
  height: 43px;
  display: flex;
  justify-content: space-between;
  background-color: #fafafa;

  .tab-container {
    width: 100%;
    display: flex;
    overflow-x: scroll;
    height: 36px;
  }

  .tab-container::-webkit-scrollbar {
    height: 2px;
  }

  .tab {
    flex-shrink: 0;
    user-select: none;
    display: inline-block;
    height: 31px;
    line-height: 15px;
    padding: 6px 6px;
    margin: 0px 3px;
    background-color: #fff;
    border: 1px solid #fff;
    min-width: 90px;
    text-align: center;
    cursor: pointer;
    border-radius: 3px;
    box-shadow: 0 2px 8px #f0f1f2;
    color: rgba(0, 0, 0, 0.6);
    font-weight: 400;
  }

  .tab:hover {
    background-color: #fdfdfd;
  }
  .active {
    border: 1px solid #00474f;
    color: #00474f;
    font-weight: 400;
  }

  .active:hover {
    background-color: #fdfdfd;
  }

  .more {
    display: inline-block;
    padding: 8px;
    margin: 0px 3px;
    vertical-align: middle;
    background-color: #fff;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    box-shadow: 0 2px 8px #f0f1f2;
    height: 31px;

    .tab-oper:hover {
      font-weight: 600;
    }
  }
}

.el-icon {
  top: 2px;
}

.close {
  transition: all 0.5s ease 0s;
  transform: rotateZ(0turn);
  transform-origin: 50% 50%;
}

.close:hover {
  transition: all 0.3s ease 0s;
  transform: rotateZ(0.25turn);
  transform-origin: 50% 50%;
}
</style>
