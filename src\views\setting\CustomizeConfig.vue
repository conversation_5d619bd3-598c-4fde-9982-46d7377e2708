<template>
  <div style="text-align: center">
    <el-form :model="data.form" label-width="120px" style="width: 400px; margin: 0 auto">
      <el-form-item label="语音播报音量" prop="volume">
        <el-select v-model="data.form.volume" placeholder="请选择音量" clearable>
          <el-option v-for="item in volumes" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="save">保存</el-button>
  </div>
</template>

<script setup>
import { reactive } from 'vue';
import { ElMessage } from 'element-plus';
import monitorService from '@/service/monitor/MonitorService';

const volumes = [
  { value: 0, name: '静音' },
  { value: 1, name: '很小' },
  { value: 2, name: '偏小' },
  { value: 3, name: '较小' },
  { value: 4, name: '中低' },
  { value: 5, name: '中等' },
  { value: 6, name: '中高' },
  { value: 7, name: '偏大' },
  { value: 8, name: '较大' },
  { value: 9, name: '很大' }
];
const data = reactive({
  form: {
    volume: undefined
  }
});
const save = () => {
  monitorService.setVolume(data.form.volume).then((response) => {
    if (response.success === true) {
      ElMessage({
        message: '保存成功',
        type: 'success'
      });
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
</script>

<style lang="scss" scoped></style>
