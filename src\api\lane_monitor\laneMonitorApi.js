import $ from '@/utils/axios';

/**
 * @description 获取车道识别相机基础信息
 * @returns 相机信息
 */
export const getCameraInfo = (id) => {
  return $({
    url: `/sentry/park/monitor/getBaseDeviceData?cameraId=${id}`,
    method: 'post'
  });
};

/**
 * @description 获取车道识别相机列表
 * @returns 相机列表
 */
export const getCameraOptions = () => {
  return $({
    url: '/sentry/park/monitor/getVideoList',
    method: 'post'
  });
};

/**
 * @description 获取车道识别事件列表
 * @returns 事件列表
 */
export const getEventList = (data) => {
  return $({
    url: '/sentry/park/monitor/pagingEvents',
    method: 'post',
    data
  });
};

/**
 * @description 批量导出图片
 * @returns 图片压缩包
 */
export const exportImageFile = (data) => {
  return $({
    url: '/sentry/park/monitor/downLoad',
    method: 'post',
    data
  });
};

/**
 * @description 导出报表
 * @returns 报表文件
 */
export const exportListFile = (data) => {
  return $({
    url: '/sentry/park/monitor/downLoadList',
    method: 'post',
    data
  });
};
