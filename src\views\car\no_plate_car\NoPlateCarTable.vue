<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="charge_name" label="当班人" align="center" />
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="car_in_biz_no" label="入场流水号" align="center" />
        <el-table-column prop="park_name" label="车场名" align="center" />
        <el-table-column prop="park_region_name" label="区域" align="center" />
        <el-table-column prop="gateway_name" label="通道名" align="center" />
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="plate_no" label="车牌" align="center" />
        <el-table-column prop="car_type_desc" label="车型" align="center" />
        <el-table-column prop="car_color" label="颜色" align="center" />
        <el-table-column label="入场图片" align="center" min-width="100">
          <template v-slot="scope">
            <el-image
              v-if="scope.row.car_photo_url !== '' && scope.row.car_photo_url !== null && scope.row.car_photo_url !== undefined"
              style="width: 100px; height: 100px"
              :src="scope.row.car_photo_url"
              :fit="fit"
              @click="showImage(scope.row.car_photo_url)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="out_state_desc" label="出场状态" align="center" />
        <el-table-column prop="in_type_desc" label="入场类型" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog v-model="imageDialogVisible" title="入场图片">
      <img w-full :src="data.dialogImageUrl" style="max-width: 100%; max-height: 100%" alt="Preview Image" />
    </el-dialog>
  </el-card>
</template>

<script name="NoPlateCarTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import carService from '@/service/car/CarService';
import { getDefaultDateRange } from '@/utils/common';

const tableData = ref([]);
const imageDialogVisible = ref(false);
const total = ref(0);
const loading = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    start_time: '',
    end_time: ''
  },
  dateRange: [],
  dialogImageUrl: ''
});
onMounted(() => {
  data.dateRange = getDefaultDateRange(7);
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.start_time === '' ? (params.start_time = data.dateRange[0]) : (params.start_time = data.queryParams.start_time);
  params.end_time === '' ? (params.end_time = data.dateRange[1]) : (params.end_time = data.queryParams.end_time);
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  carService.pageCarInRecordNoPlate(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const showImage = (val) => {
  data.dialogImageUrl = val;
  imageDialogVisible.value = true;
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
