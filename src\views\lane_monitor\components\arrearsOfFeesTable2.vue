<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-02-27 17:34:02
 * @LastEditTime: 2024-03-01 10:56:15
 * @LastEditors: 达万安 段世煜
 * @Description: 事件table组件
 * @FilePath: \parking-client-ui\src\views\lane_monitor\components\eventTable.vue
-->
<template>
  <el-card class="table" shadow="never">
    <div>
      <el-space>
        <div class="search-btn-group">
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ tableTop.all }}</p>
            <span class="search-btn-group-total-label">累计上报用户数</span>
          </div>
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ tableTop.today }}</p>
            <span class="search-btn-group-total-label">今日上报用户数</span>
          </div>
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ tableTop.yesterday }}</p>
            <span class="search-btn-group-total-label">昨日上报用户数</span>
          </div>
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ tableTop.last7days }}</p>
            <span class="search-btn-group-total-label">最近7天上报用户数</span>
          </div>
        </div>
      </el-space>
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange" ref="tableRef">
        <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
        <!-- <el-table-column type="index" label="序号" align="center" width="60" /> -->
        <el-table-column prop="event_level" label="操作" align="center">
          <template v-slot="scope">
            <el-button link @click="recordList(scope.row)" type="primary"> 违规记录 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="plate_no" label="车牌号" align="center" min-width="160" />
        <el-table-column prop="event_no" label="事件编号" align="center" />
        <el-table-column prop="event_time" label="事件时间" align="center" width="160" />
        <!-- <el-table-column prop="event_time" label="跟车类型" align="center" width="160" /> -->
        <el-table-column prop="gateway_name" label="通道名称" align="center" />
        <el-table-column prop="gateway_type" label="通道类型" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.gateway_type == 1">出口</span>
            <span v-if="scope.row.gateway_type == 2">入口</span>
            <span v-if="scope.row.gateway_type == 3">出入混合</span>
          </template>
        </el-table-column>
        <el-table-column label="事件抓拍图" align="center" min-width="100">
          <template v-slot="scope">
            <el-button link type="primary" @click="handelViewPic(scope.row)">查看图片</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="event_video" label="事件短视频" align="center">
          <template v-slot="scope">
            <el-button link type="primary" @click="handelViewPic(scope.row, 'video')">查看视频</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="thirty_days_times" label="近30天违规次数" align="center" />
        <el-table-column prop="total_times" label="累计违规总次数" align="center" />
      </el-table>
      <el-pagination background v-model:current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        v-model:page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper"
        :total="data.queryParams.total" class="table-pagination" @size-change="handleSizeOrCurrentChange"
        @current-change="handleSizeOrCurrentChange" />
    </div>
  </el-card>
  <!-- 图片预览组件 -->
  <el-image-viewer v-if="showImagePreview" hide-on-click-modal @close="() => (showImagePreview = false)"
    :url-list="imgPreviewList" />

  <!-- 违规记录 -->
  <el-dialog v-model="recorddialogVisible" title="违规记录" width="900" :before-close="handleClose">
    <div style="padding: 20px 0">
      <el-table :data="tableData2" v-loading="loading" border @selection-change="handleSelectionChange" ref="tableRef">
        <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
        <el-table-column type="index" label="序号" align="center" width="60" />
        <!-- <el-table-column prop="event_level" label="操作" align="center">
          <template v-slot="scope">
            <el-button link @click="recordList(scope.row)" type="primary"> 违规记录 </el-button>
          </template>
        </el-table-column> -->
        <el-table-column prop="event_no" label="事件编号" align="center" />
        <el-table-column prop="event_time" label="事件时间" align="center" width="160" />
        <el-table-column prop="gateway_name" label="通道名称" align="center" />

        <el-table-column prop="" label="事件抓拍图" align="center" />
        <el-table-column prop="" label="事件短视频" align="center" />
      </el-table>
      <el-pagination background v-model:current-page="recordParams.page" :page-sizes="[10, 30, 50, 100]"
        v-model:page-size="recordParams.limit" layout="total, sizes, prev, pager, next, jumper"
        :total="recordParams.total" class="table-pagination" @size-change="handleSizeOrCurrentChange2"
        @current-change="handleSizeOrCurrentChange2" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="dialogVisible = false">
          Confirm
        </el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script name="DeviceTable" setup>
import ArrearsOfFeesApi from '@/api/arrears-of-fees/arrearsOfFees.js';
import { exportImageFile } from '@/api/lane_monitor/laneMonitorApi';
import { useSetting } from '@/stores/setting';
import { useIpcRenderer } from '@vueuse/electron';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import { storeToRefs } from 'pinia';
import { nextTick, onMounted, reactive, ref } from 'vue';
console.log(ArrearsOfFeesApi);
// 渲染进程回调 监听文件是否处理成功
const ipcRenderer = useIpcRenderer();
ipcRenderer.on('open-export-file-fail', (event, data) => {
  ElMessage.error(data);
});
// 查询数据
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    total: 0,
    camera_id: ''
  }
});
const recordParams = reactive({
  event_type_id: 85,
  page: 1,
  limit: 30,
  total: 0
});
const tableTop = ref({});
// 表格数据
const tableData = ref([]);
const tableData2 = ref([]);
// 表格实体
const tableRef = ref();
// 表格加载状态
const loading = ref(false);
// camera_id
const setting = useSetting();
const { lane_monitor } = storeToRefs(setting);

onMounted(() => {
  // 数据初始化
  nextTick(() => {
    initData(lane_monitor.value);
  });
});

// 监听lane_monitor变化，数据初始化
// watch(lane_monitor, (val) => {
//   console.log(val,'sssssssssssss')
//   initData(val);
// });

/**
 * @description: 初始化数据
 * @param {*} val camera_id
 */
const initData = (val) => {
  // 设置data.queryParams.camera_id的值为val
  data.queryParams.camera_id = val;
  // 调用getList函数，传入data.queryParams参数
  getList(data.queryParams);
};

/**
 * @description: 获取表格数据
 * @param {*} params 表格入参
 * @param {*} init 是否初始化
 */
const getList = async (params, init = false) => {
  // 初始化
  if (init) {
    // 设置页面和每页条数
    data.queryParams.page = 1;
    data.queryParams.limit = 30;
    // 清空已选择行
    selectedRows.value = [];
  }
  // 加载中
  loading.value = true;
  // 设置查询参数
  data.queryParams = { ...data.queryParams, ...params };
  try {
    // 获取事件列表
    data.queryParams.event_type_id = 85;
    const { data: resData, message, success } = await ArrearsOfFeesApi.getpagingEvents(data.queryParams);
    if (success) {
      // 设置表格数据
      tableData.value = resData.rows;
      // 设置总条数
      data.queryParams.total = parseInt(resData.total);
    } else {
      // 弹出错误信息
      ElMessage({
        message: message,
        type: 'error'
      });
    }
  } catch (error) {
    // 弹出错误信息
    ElMessage({
      message: error,
      type: 'error'
    });
  } finally {
    // 加载完成
    loading.value = false;
  }
};
const getList2 = async () => {
  // 设置页面和每页条数
  recordParams.page = 1;
  recordParams.limit = 30;
  recordParams.plate_no = chooseItem.value.plate_no;
  // 加载中
  loading.value = true;
  // 设置查询参数
  try {
    // 获取事件列表
    const { data: resData, message, success } = await ArrearsOfFeesApi.getpagingEvents(recordParams);
    if (success) {
      // 设置表格数据
      tableData2.value = resData.rows;
      // 设置总条数
      recordParams.total = parseInt(resData.total);
    } else {
      // 弹出错误信息
      ElMessage({
        message: message,
        type: 'error'
      });
    }
  } catch (error) {
    // 弹出错误信息
    ElMessage({
      message: error,
      type: 'error'
    });
  } finally {
    // 加载完成
    loading.value = false;
  }
};
// 图片预览展示标识
const showImagePreview = ref(false);
// 图片预览列表
const imgPreviewList = ref([]);
/**
 * @description: 图片预览
 * @param {*} val 源数据
 */
const handelViewPic = (val, type) => {
  if (type) {
    ElMessage.warning(`设备暂不支持视频`);
    return;
  }
  // const base = 'data:image/jpg;base64,';
  let imageUrl = val.event_data;
  // if (val.event_data.indexOf(base) === -1) {
  //   imageUrl = base + val.event_data;
  // }
  imgPreviewList.value = [imageUrl];
  showImagePreview.value = true;
};

/**
 * @description: 分页
 */
const handleSizeOrCurrentChange = () => {
  getList(data.queryParams);
};
const handleSizeOrCurrentChange2 = () => {
  getList2(recordParams);
};
// 多选选择行
const selectedRows = ref([]);
/**
 * @description: 选择行
 * @param {*} val 当前选择值数组
 */
const handleSelectionChange = (val) => {
  selectedRows.value = val;
};
// 导出加载状态
const exportLoading = ref();
/**
 * @description: 打开loading
 */
const openLoading = () => {
  exportLoading.value = ElLoading.service({
    lock: true,
    text: '正在导出，请稍后',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
/**
 * @description: 导出图片
 */
const exportImage = async () => {
  if (selectedRows.value.length) {
    openLoading();
    const ids = selectedRows.value.map((item) => {
      return item.id;
    });
    try {
      const { data: resData, success, message } = await exportImageFile({ camera_id: data.queryParams.camera_id, ids: ids.join(',') });
      if (success) {
        // 清空已选择行
        selectedRows.value = [];
        tableRef.value.clearSelection();
        // 处理导出数据
        dealFile(resData);
      } else {
        ElMessage({
          message: message,
          type: 'error'
        });
      }
    } finally {
      exportLoading.value.close();
    }
  } else {
    ElMessage({
      message: '请选择需要下载的图片',
      type: 'warning'
    });
  }
};
/**
 * @description: 导出列表
 */
const exportList = async () => {
  ArrearsOfFeesApi.exportEvents({
    // "page": 1,
    // "limit": 30,
    // "total": 1,
    event_type_id: 85,
    start_time: data.queryParams.start_time,
    end_time: data.queryParams.end_time,
    plate_no: data.queryParams.plate_no,
    gateway_id: data.queryParams.gateway_id
  });
};
/**
 * @description: 处理导出数据 在文件系统中打开
 * @param {*} url 文件绝对路径
 */
const dealFile = (url) => {
  if (!url) {
    ElMessage.error('未获取到下载文件');
    return;
  }
  ElMessageBox.confirm('文件已导出至本地，是否需要在文件夹中打开文件？', '提示', {
    confirmButtonText: '在文件夹中显示',
    cancelButtonText: '取消',
    type: 'success'
  }).then(() => {
    const urlUse = url.replaceAll('/', '\\');
    ipcRenderer.send('open-export-file', urlUse);
  });
};

// 违规记录
const recorddialogVisible = ref(false);
const chooseItem = ref({});
const recordList = (item) => {
  chooseItem.value = item;
  getList2();
  recorddialogVisible.value = true;
};
defineExpose({
  getList,
  exportImage,
  exportList,
  tableTop
});
</script>
<style lang="scss" scoped>
.search-btn-group {
  gap: 4px;
  display: flex;
  margin-bottom: 10px;
}

.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
  padding: 0 6px;
}

.search-btn-group-total-num {
  color: #409eff;
  margin: 1px;
  font-weight: 700;
  font-size: 12px;
  text-align: center;
}

.search-btn-group-total-label {
  color: #161616;
  font-size: 12px;
}
</style>
