<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="数据标识" /></form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.down_state" placeholder="下行数据类型" clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="DownDataSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive, ref, onMounted } from 'vue';
import commonService from '@/service/common/CommonService';
const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    down_state: '',
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  },
  dateRange: []
});

const states = ref([]);
onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumDownState' }];
  commonService.findEnums('park', param).then((response) => {
    states.value = response.data.states;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    down_state: '',
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
