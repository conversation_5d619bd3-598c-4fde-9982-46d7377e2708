import * as device from '@/api/device/DeviceApi';

/**
 * @description 设备信息
 * <AUTHOR>
 * @date 2022/11/2
 */
export default {
  /**
   * 查询停车场相关设备
   */
  queryDeviceStates(data) {
    return new Promise((resolve, reject) => {
      try {
        device.queryDeviceStates(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询终端（岗亭端）信息
   */
  pagingSentryTerminals(data) {
    return new Promise((resolve, reject) => {
      try {
        device.pagingSentryTerminals(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询设备明细
   */
  getDeviceStateDetail(data) {
    return new Promise((resolve, reject) => {
      try {
        device.getDeviceStateDetail(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询设备状态
   */
  listDeviceStates() {
    return new Promise((resolve, reject) => {
      try {
        device.listDeviceStates().then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
