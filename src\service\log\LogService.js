import * as log from '@/api/log/LogApi';

/**
 * @description 日志
 * <AUTHOR>
 * @date 2022/10/26
 */
export default {
  /**
   * 查询系统日志
   */
  pageSysLog(data) {
    return new Promise((resolve, reject) => {
      try {
        log.pageSysLog(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询操作日志
   */
  pageAgentAuditLog(data) {
    return new Promise((resolve, reject) => {
      try {
        log.pageAgentAuditLog(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
