<template>
  <el-row :gutter="10">
    <el-col :span="5">
      <div class="title">入场图片</div>
      <el-image :src="state.lastParkInRecord.car_photo_url" fit="fill" @click="showImage(state.lastParkInRecord.car_photo_url)">
        <template #error>
          <div class="image-slot">
            <el-icon>
              <Picture />
            </el-icon>
          </div>
        </template>
      </el-image>
      <div class="title">出场图片</div>
      <el-image :src="state.capture.pic_path_url" fit="fill" @error="refresh" @click="showImage(state.capture.pic_path_url)">
        <template #error>
          <div class="image-slot">
            <el-icon>
              <Picture />
            </el-icon>
          </div>
        </template>
      </el-image>
    </el-col>
    <el-col :span="9" style="min-width: 430px">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div class="title">车辆信息</div>
            </el-col>
            <el-col :span="16" class="value">
              <div class="title" style="font-size: 24px; font-weight: 600; position: relative; top: -10%">
                {{ state.plate_no ? state.plate_no : '暂无车辆' }}
              </div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12" style="color: #096dd9; height: 22px; line-height: 22px">
          <div v-if="state.memo != null && state.memo != undefined" class="title">
            <el-alert :title="state.memo" type="success" :closable="false" />
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>车型</div>
            </el-col>
            <el-col :span="16" class="value">
              <span>{{ state.lastParkInRecord.car_type_desc }}</span>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>入场时间</div>
            </el-col>
            <el-col :span="16" class="value">
              <span>{{ state.lastParkInRecord.in_time }}</span>
              <span v-if="state.lastParkInRecord.in_type == 7" style="color: #f5222d">（入场记录为跟车事件生成）</span>
              <span v-if="state.lastParkInRecord.in_type == 9" style="color: #f5222d">（折返事件）</span>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>进入道口</div>
            </el-col>
            <el-col :span="16" class="value">
              <span>{{ state.lastParkInRecord.gateway_name }}</span>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>出场时间</div>
            </el-col>
            <el-col :span="16" class="value">
              <!-- <span>{{ state.capture.event_time }}</span> -->
              <span>{{ state.parkOrder.to_time }}</span>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>停车时长</div>
            </el-col>
            <el-col :span="16" class="value">
              <span>{{ state.parkOrder.duration_text }}</span>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-button
            type="primary"
            @click="handleUpdatePlateNo(state.plate_no)"
            :disabled="
              state.plate_no === undefined || state.completePayed.display || state.parkOrder.should_pay_money === 0 || state.has_car_in_record
            "
          >
            修改车牌号
          </el-button>
        </el-col>
      </el-row>
      <el-row :gutter="16" style="margin-top: 10px">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div class="title">费用信息</div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>应收费用</div>
            </el-col>
            <el-col :span="16" class="value">
              <span class="money">{{ state.parkOrder.total_money }} </span>
              <div class="money-unit">&ensp;元</div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>优免</div>
            </el-col>
            <el-col :span="16" class="value">
              <span class="money">{{ state.parkOrder.current_coupon_money }}</span>
              <div class="money-unit">&ensp;元&ensp;</div>
              <el-button type="primary" @click="handleCancelCoupon" :disabled="!state.usedCouponFlag">取消优免</el-button>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>电子支付</div>
            </el-col>
            <el-col :span="16" class="value">
              <span class="money">{{ state.parkOrder.history_payed_money }}</span>
              <div class="money-unit">&ensp;元</div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="20" class="label">
              <div>已抵扣金额</div>
            </el-col>
            <el-col :span="8" class="value">
              <span class="money">{{ state.parkOrder.history_derate_coupon_money }}</span>
              <div class="money-unit">&ensp;元</div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>现金支付</div>
            </el-col>
            <el-col :span="16" class="value">
              <span class="money">{{ state.parkOrder.pay_type === 1 ? state.parkOrder.payed_money : 0 }}</span>
              <div class="money-unit">&ensp;元</div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>已支付</div>
            </el-col>
            <el-col :span="16" class="value">
              <span class="money">{{ state.parkOrder.payed_money }}</span>
              <div class="money-unit">&ensp;元</div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="20" class="label">
              <div>找零金额</div>
            </el-col>
            <el-col :span="8" class="value">
              <span class="money">{{ state.parkOrder.cash_zero }}</span>
              <div class="money-unit">&ensp;元</div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div>还需支付</div>
            </el-col>
            <el-col :span="16" class="value">
              <span class="money" style="color: #52c41a">{{ state.parkOrder.should_pay_money }}</span>
              <div class="money-unit">&ensp;元&ensp;</div>
              <el-button
                type="primary"
                @click="handleRefreshOrderClick"
                :disabled="
                  state.plate_no === undefined ||
                  state.completePayed.display ||
                  state.parkOrder.order_state === 2 ||
                  (state.memo !== undefined && state.memo.includes('白名单'))
                "
              >
                订单刷新
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row v-if="tableData.length > 0" :gutter="16">
        <el-col :span="24">
          <el-row :gutter="16">
            <el-col :span="9" class="label">
              <div class="title">历史欠费</div>
            </el-col>
            <el-col :span="9" class="label" style="width: 300px; justify-content: flex-start">
              <div style="margin-left: 12px; color: rgb(245, 34, 45)">(欠费合计：{{ debt_moneyAll }}元 / {{ tableData.length }}笔)</div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row v-if="tableData.length > 0" :gutter="16">
        <el-col :span="24" style="">
          <el-table
            :data="tableData"
            style="width: 100%; max-height: 280px; overflow-y: auto"
            v-loading="loading"
            border
            @selection-change="handleSelectionChange"
            ref="tableRef"
          >
            <el-table-column prop="event_type" label="欠费原因" align="center" />
            <el-table-column prop="should_pay_money" label="欠缴金额" align="center" />
            <el-table-column prop="time" label="停车时长" align="center" />
            <!-- <el-table-column prop="event_data" label="事件抓拍图" align="center" /> -->
            <el-table-column label="事件抓拍图" align="center" min-width="100">
              <template v-slot="scope">
                <el-button link type="primary" @click="showImage(scope.row.event_data)">查看图片</el-button>
              </template>
            </el-table-column>
            <el-table-column label="事件短视频" align="center" min-width="100">
              <template v-slot="scope">
                <el-button link type="primary" @click="showImage(scope.row.event_data, 'video')">查看视频</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="event_time" label="事件时间" align="center" />
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-row :gutter="16">
            <el-col :span="8" class="label">
              <div class="title">计费方式</div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="24" style="margin-left: 10px">
          <el-radio-group
            v-model="state.car_type"
            @change="handleChangeCarType"
            :disabled="
              state.capture.plate_no === undefined ||
              state.completePayed.display ||
              state.stop_car_type !== 1 ||
              (state.parkOrder.should_pay_money === '0' && state.parkOrder.order_state === 2)
            "
          >
            <el-radio-button v-for="item in car_type_options" :key="item.value" :label="item.value">{{ item.key }}</el-radio-button>
          </el-radio-group>
        </el-col>
      </el-row>
      <el-row :gutter="16" style="margin-top: 20px; height: 82px; margin-left: 5px">
        <el-col :span="5">
          <el-button
            type="primary"
            style="height: 82px; width: 100%; font-size: 18px"
            @click="handleManualCapture"
            :disabled="state.parkOrder.order_state === 2"
            >手动抓拍</el-button
          >
        </el-col>
        <el-col :span="19">
          <el-button
            id="chargeAndPass"
            type="primary"
            v-if="state.has_car_in_record"
            @click="handleChargeAndPass"
            size="large"
            style="width: 100%; font-size: 18px"
            :disabled="
              state.plate_no === undefined ||
              state.completePayed.display ||
              (state.memo !== undefined && state.memo.includes('白名单')) ||
              (state.stop_car_type !== 1 && !!state.stop_car_type) ||
              (state.parkOrder.should_pay_money === '0' && state.parkOrder.order_state === 2) ||
              (state.out_type == 4 && state.car_type == 0)
            "
          >
            收费放行
          </el-button>
          <el-button
            id="manualLift"
            type="primary"
            v-if="!state.has_car_in_record"
            @click="handleManualLift"
            size="large"
            style="width: 100%; font-size: 18px"
            :disabled="
              state.plate_no === undefined ||
              state.parkOrder.order_state === 2 ||
              (state.stop_car_type !== 1 && !!state.stop_car_type) ||
              (state.out_type == 4 && state.car_type == 0)
            "
          >
            手动抬杆
          </el-button>
          <el-row :gutter="10" style="margin-top: 10px">
            <el-col :span="6">
              <el-button style="width: 100%" @click="handleManualPass" :disabled="state.has_car_in_record || state.plate_no === undefined">
                手动匹配
              </el-button>
            </el-col>
            <el-col :span="6">
              <el-button
                style="width: 100%"
                @click="handleManualRecord"
                :disabled="state.has_car_in_record || state.plate_no === undefined || state.plate_no === '无牌车'"
              >
                手动补录
              </el-button>
            </el-col>
            <el-col :span="6">
              <el-button
                style="width: 100%"
                @click="handleSpecialPass"
                :disabled="
                  !state.has_car_in_record ||
                  state.plate_no === undefined ||
                  (state.memo !== undefined && state.memo.includes('白名单')) ||
                  (state.stop_car_type !== 1 && !!state.stop_car_type) ||
                  (state.parkOrder.should_pay_money === '0' && state.parkOrder.order_state === 2) ||
                  (state.out_type == 4 && state.car_type == 0)
                "
              >
                特殊放行
              </el-button>
            </el-col>
            <el-col :span="6">
              <el-button
                style="width: 100%"
                @click="handleCancelPass"
                :disabled="
                  state.plate_no === undefined ||
                  (state.memo !== undefined && state.memo.includes('白名单')) ||
                  (state.stop_car_type !== 1 && !!state.stop_car_type) ||
                  (state.parkOrder.should_pay_money === '0' && state.parkOrder.order_state === 2) ||
                  (state.out_type == 4 && state.car_type == 0)
                "
                >取消放行</el-button
              >
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <div class="alert warning" v-show="state.carInRecord.display">
        <span>{{ state.carInRecord.tip }}</span>
      </div>
      <div class="alert success" v-show="state.completePayed.display">
        <span>{{ state.completePayed.tip }}</span>
      </div>
    </el-col>
    <el-col :span="10">
      <el-tabs type="border-card" class="table-card">
        <el-tab-pane label="排队车辆">
          <el-table :data="rankTableData" border style="height: calc((100vh - 380px) / 2)">
            <el-table-column type="index" label="序号" align="center" width="60" />
            <el-table-column prop="plate_no" label="车牌号" align="center" />
            <el-table-column prop="gateway_name" label="出道口名称" align="center" width="160" />
            <el-table-column prop="queue_time" label="排队时间" align="center" />
            <el-table-column prop="out_state_desc" label="操作" align="center">
              <template v-slot="scope">
                <el-button link @click="firstHandler(scope.row)" type="primary"> 优先处理 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="入场记录">
          <el-table :data="state.inRecords" border style="height: calc((100vh - 380px) / 2)">
            <el-table-column prop="plate_no" label="车牌号" align="center">
              <template v-slot="scope">
                {{ scope.row.plate_no }}
              </template>
            </el-table-column>
            <el-table-column prop="in_time" label="入场时间" align="center" width="160" />
            <el-table-column prop="gateway_name" label="入口" align="center" />
            <el-table-column prop="in_type" label="入场类型" align="center">
              <template v-slot="scope">
                {{ inTypeList[scope.row.in_type] }}
              </template>
            </el-table-column>
            <el-table-column prop="out_state_desc" label="出场状态" align="center" />
            <el-table-column prop="park_region_name" label="区域" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="出场记录">
          <el-table :data="state.outRecords" border style="height: calc((100vh - 380px) / 2)">
            <el-table-column prop="plate_no" label="车牌号" align="center">
              <template v-slot="scope">
                {{ scope.row.plate_no }}
              </template>
            </el-table-column>
            <el-table-column prop="out_time" label="出场时间" align="center" width="160" />
            <el-table-column prop="gateway_name" label="出口" align="center" />
            <el-table-column prop="park_region_name" label="区域" align="center" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <el-tabs type="border-card" class="table-card" style="margin-top: 10px">
        <el-tab-pane label="费用详情">
          <el-table :data="state.parkOrder.details" border style="height: calc((100vh - 380px) / 2)">
            <el-table-column label="时段" align="center" width="350">
              <template v-slot="scope">
                {{ scope.row.start_time + ' - ' + scope.row.end_time }}
              </template>
            </el-table-column>
            <el-table-column prop="money" label="计费金额" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="优免券">
          <el-table :data="state.availableCoupons" border style="height: calc((100vh - 380px) / 2)">
            <el-table-column prop="action" label="操作" align="center" width="110">
              <template v-slot="scope">
                <el-button link type="primary" @click="handleUseCoupon(scope.row)" v-if="!state.usedCouponFlag && state.parkOrder.order_state !== 2"
                  >使用</el-button
                >
              </template>
            </el-table-column>
            <el-table-column prop="coupon_no" label="卡券编号" align="center" show-overflow-tooltip />
            <el-table-column prop="merchant_name" label="发放商家" align="center" />
            <el-table-column prop="type_desc" label="券类型" align="center" />
            <el-table-column prop="expire_time" label="过期时间" align="center" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-col>
  </el-row>

  <!-- 手动抬杆 -->
  <el-dialog v-model="manualLift.dialogVisible" title="手动抬杆" width="400px" :before-close="handleManualLiftClose" :close-on-click-modal="false">
    <el-form ref="manualLiftFormRef" :model="manualLift.form" :rules="manualLift.rules" label-position="top">
      <el-form-item prop="money" label="放行金额">
        <el-input-number v-model="manualLift.form.money" placeholder="请输入" style="width: 100%" :min="0" :precision="2" />
      </el-form-item>
      <el-form-item prop="out_reason" label="放行原因">
        <el-select v-model="manualLift.form.out_reason" placeholder="请选择" style="width: 100%">
          <el-option v-for="item in out_reason_options" :key="item.value" :label="item.key" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="memo" label="备注">
        <el-input type="textarea" :rows="4" v-model="manualLift.form.memo" placeholder="请输入" style="width: 100%" />
      </el-form-item>
    </el-form>
    <div style="margin-top: 30px; text-align: center">
      <el-space>
        <el-button type="primary" @click="calcManualLiftCarFee(manualLiftFormRef)">车辆计费</el-button>
        <el-button @click="resetManualLiftCarFee(manualLiftFormRef)">重 置</el-button>
      </el-space>
    </div>
  </el-dialog>

  <!-- 手动匹配 -->
  <el-dialog v-model="manualPass.dialogVisible" title="手动匹配" width="700px" :before-close="handleManualPassClose" :close-on-click-modal="false">
    <div style="display: flex; align-items: center; margin-bottom: 10px">
      <div v-if="!manualPass.flag" style="height: 32px; line-height: 32px"><span class="required">*</span>车牌号</div>
      <div v-if="manualPass.flag" style="height: 32px; line-height: 32px"><span class="required">*</span>入场时间</div>
      <div>
        <el-input
          v-if="!manualPass.flag"
          v-model="manualPass.query.plate_no"
          placeholder="请输入车牌号"
          style="width: 100px; margin: 0 10px"
        ></el-input>
        <el-date-picker
          v-if="!manualPass.flag"
          v-model="manualPass.daterange"
          type="daterange"
          range-separator="至"
          start-placeholder="入场开始时间"
          end-placeholder="入场结束时间"
          style="width: 225px; margin: 0 10px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />

        <el-date-picker
          v-if="manualPass.flag"
          v-model="manualPass.daterange0"
          type="daterange"
          range-separator="至"
          start-placeholder="入场开始时间"
          end-placeholder="入场结束时间"
          style="width: 265px; margin: 0 10px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />

        <el-button type="primary" @click="handleQueryParkInRecord">查 询</el-button>
        <el-button v-if="manualPass.flag" @click="handlePlate">返回</el-button>
        <el-button v-else @click="handleNoPlate">查询无牌车</el-button>
      </div>
    </div>
    <div v-show="!manualPass.flag" style="padding-bottom: 30px">
      <el-table :data="manualPass.table.data" border v-loading="manualPass.loading">
        <el-table-column prop="plate_no" label="车牌号" align="center">
          <template v-slot="scope">
            {{ scope.row.plate_no }}
          </template>
        </el-table-column>
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="gateway_name" label="入口" align="center" width="160" />
        <el-table-column label="入场图片" align="center" min-width="100">
          <template v-slot="scope">
            <el-image
              v-if="scope.row.car_photo_url"
              style="width: 100px; height: 100px"
              :src="scope.row.car_photo_url"
              :fit="fit"
              @click="showImage(scope.row.car_photo_url)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="action" label="操作" align="center" width="90">
          <template v-slot="scope">
            <el-button v-if="scope.row.out_state === 0" link type="primary" @click="calcManualPassCarFee(scope.row)">车辆计费</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="manualPass.query.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="manualPass.query.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="manualPass.total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div v-show="manualPass.flag" style="padding-bottom: 30px">
      <el-table :data="manualPass.table0.data" border v-loading="manualPass.loading">
        <el-table-column prop="plate_no" label="车牌号" align="center">
          <template v-slot="scope">
            {{ scope.row.plate_no }}
          </template>
        </el-table-column>
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="gateway_name" label="入口" align="center" width="160" />
        <el-table-column label="入场图片" align="center" min-width="100" v-if="manualPass.flag">
          <template v-slot="scope">
            <el-image
              v-if="scope.row.car_photo_url"
              style="width: 100px; height: 100px"
              :src="scope.row.car_photo_url"
              :fit="fit"
              @click="showImage(scope.row.car_photo_url)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="action" label="操作" align="center" width="90">
          <template v-slot="scope">
            <el-button v-if="scope.row.out_state === 0" link type="primary" @click="calcManualPassCarFee(scope.row)">车辆计费</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="manualPass.query0.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="manualPass.query0.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="manualPass.total0"
        class="table-pagination"
        @size-change="handleSize0Change"
        @current-change="handleCurrent0Change"
      />
    </div>
  </el-dialog>

  <el-dialog v-model="imageDialogVisible" title="查看图片">
    <img w-full :src="dialogImageUrl" style="max-width: 100%; max-height: 100%" alt="Preview Image" />
  </el-dialog>

  <!-- 特殊放行 -->
  <el-dialog v-model="specialPass.dialogVisible" title="特殊放行" width="400px" :before-close="handleSpecialPassClose" :close-on-click-modal="false">
    <el-form ref="specialPassFormRef" :model="specialPass.form" :rules="specialPass.rules" label-position="top">
      <el-form-item prop="money" label="减免金额">
        <el-input v-model="specialPass.form.money" readonly placeholder="请输入" style="width: 100%" :min="0" :precision="2" />
      </el-form-item>
      <el-form-item prop="out_reason" label="放行原因">
        <el-select v-model="specialPass.form.out_reason" placeholder="请选择" style="width: 100%">
          <el-option v-for="item in out_reason_options" :key="item.value" :label="item.key" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="memo" label="备注">
        <el-input type="textarea" :rows="4" v-model="specialPass.form.memo" placeholder="请输入" style="width: 100%" />
      </el-form-item>
    </el-form>
    <div style="margin-top: 30px; text-align: center">
      <el-space>
        <el-button type="primary" @click="calcSpecialPassCarFee(specialPassFormRef)">车辆计费</el-button>
        <el-button @click="resetSpecialPassCarFee(specialPassFormRef)">重 置</el-button>
      </el-space>
    </div>
  </el-dialog>

  <!-- 手动补录 -->
  <el-dialog
    v-model="manualRecord.dialogVisible"
    title="手动补录"
    width="400px"
    :before-close="handleManualRecordClose"
    :close-on-click-modal="false"
  >
    <el-form ref="manualRecordFormRef" :model="manualRecord.form" label-position="top">
      <el-form-item label="车牌号" class="required">
        <el-input v-model="state.plate_no" readonly="true" style="width: 100%" :min="0" :precision="2" />
      </el-form-item>
      <el-form-item prop="gateway_id" label="入口通道">
        <el-select v-model="manualRecord.form.park_in_gateway_id" placeholder="请选择" style="width: 100%">
          <el-option v-for="item in manualRecord.in_gateways" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="入场时间" class="required">
        <el-date-picker
          v-model="manualRecord.form.in_time"
          type="datetime"
          style="width: 100%"
          placeholder="入场时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
    </el-form>
    <div style="margin-top: 30px; text-align: center">
      <el-space>
        <el-button type="primary" @click="submitManualRecord(manualRecordFormRef)">确 定</el-button>
        <el-button @click="handleManualRecordClose()">取 消</el-button>
      </el-space>
    </div>
  </el-dialog>

  <!-- 修改车牌号 -->
  <el-dialog v-model="plateNoDialogVisible" title="修改车牌号" width="450px" :before-close="handlePlateNoClose" :close-on-click-modal="false">
    <el-form :model="plateNo.form" label-position="top">
      <el-form-item label="车牌号" class="required">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-select v-model="plateNo.form.plateNoFirst" filterable style="width: 100%" disabled v-if="plateNo.noPlateCarFlag == '1'">
              <el-option v-for="item in plateNoFirsts" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
            <el-select v-else v-model="plateNo.form.plateNoFirst" filterable style="width: 100%">
              <el-option v-for="item in plateNoFirsts" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-input
              type="text"
              v-model="plateNo.form.plateNoSecond"
              show-word-limit
              maxlength="10"
              style="width: 100%"
              disabled
              v-if="plateNo.noPlateCarFlag == '1'"
            />
            <el-input type="text" v-model="plateNo.form.plateNoSecond" show-word-limit maxlength="10" style="width: 100%" v-else />
          </el-col>
          <el-col :span="6">
            <el-select v-model="plateNo.form.plateNoThirdly" filterable style="width: 100%" disabled v-if="plateNo.noPlateCarFlag == '1'">
              <el-option v-for="item in plateNoThirdlies" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
            <el-select v-model="plateNo.form.plateNoThirdly" filterable style="width: 100%" v-else>
              <el-option v-for="item in plateNoThirdlies" :key="item.label" :label="item.label" :value="item.label" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-checkbox
              v-model="plateNo.noPlateCarFlag"
              true-label="1"
              false-label="0"
              @change="changeToNoPlate"
              style="display: inline; margin-top: 10px"
            >
              无牌车
            </el-checkbox>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <div style="margin-top: 30px; text-align: center">
      <el-space>
        <el-button type="primary" @click="submitPlateNo()">确 认</el-button>
        <el-button @click="resetPlateNo()">重 置</el-button>
      </el-space>
    </div>
  </el-dialog>
</template>

<script name="WatchOut" setup>
import ArrearsOfFeesApi from '@/api/arrears-of-fees/arrearsOfFees.js';
import commonService from '@/service/common/CommonService';
import stantAccountService from '@/service/stand_account/StantAccountService';
import mockCarService from '@/service/watch/MockCarService';
import watchService from '@/service/watch/WatchService';
import { useSetting } from '@/stores/setting';
import { useUser } from '@/stores/user';
import Big from 'big.js';
import { dayjs, ElMessage, ElMessageBox } from 'element-plus';
import { throttle } from 'lodash';
import { onActivated, onDeactivated, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute(); // 当前路由对象，包含路由信息
const props = defineProps({
  gateway: {
    type: Object,
    required: true
  },
  isActived: {
    type: Boolean,
    default: false
  }
});

const setting = useSetting();
const user = useUser();
const car_type_options = ref([]);
const out_reason_options = ref([]);

const plateNoDialogVisible = ref(false);
const plateNoFirsts = ref([
  { label: '京' },
  { label: '冀' },
  { label: '晋' },
  { label: '蒙' },
  { label: '辽' },
  { label: '吉' },
  { label: '黑' },
  { label: '沪' },
  { label: '苏' },
  { label: '浙' },
  { label: '皖' },
  { label: '闽' },
  { label: '赣' },
  { label: '鲁' },
  { label: '豫' },
  { label: '鄂' },
  { label: '湘' },
  { label: '粤' },
  { label: '桂' },
  { label: '琼' },
  { label: '渝' },
  { label: '川' },
  { label: '贵' },
  { label: '云' },
  { label: '藏' },
  { label: '陕' },
  { label: '甘' },
  { label: '青' },
  { label: '宁' },
  { label: '新' },
  { label: '民航' },
  { label: '使' },
  { label: '无' }
]);
const plateNoThirdlies = ref([
  { label: '警' },
  { label: '学' },
  { label: '使' },
  { label: '领' },
  { label: '挂' },
  { label: '应急' },
  { label: '无' }
]);
const plateNo = reactive({
  form: {
    plateNoFirst: undefined,
    plateNoSecond: undefined,
    plateNoThirdly: undefined
  },
  noPlateCarFlag: '0'
});
const state = reactive({
  // 当前车牌号
  plate_no: undefined,
  // 是否有入场记录
  has_car_in_record: false,
  // 停车类型
  stop_car_type: undefined,
  // 备注
  memo: undefined,
  // 抓拍记录
  capture: {
    gateway_id: undefined,
    direction: undefined,
    ip: undefined,
    plate_no: undefined,
    plate_color: undefined,
    car_color: undefined,
    trigger_type: undefined,
    pic_path: '',
    pic_path2: '',
    take_direction: undefined,
    pic_path_url: '',
    pic_path2_url: '',
    event_time: undefined
  },
  // 离场类型，默认-人工放行
  out_type: 2,
  // 业务流水号
  car_biz_no: undefined,
  // 车辆最近一条入场记录
  lastParkInRecord: {
    id: undefined,
    car_in_biz_no: undefined,
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    park_region_name: undefined,
    gateway_id: undefined,
    gateway_name: undefined,
    in_time: undefined,
    plate_no: undefined,
    car_type: undefined,
    car_type_desc: undefined,
    car_color: undefined,
    car_photo: '',
    car_photo_cloud_file_id: undefined,
    car_photo_sync_to_cloud: undefined,
    out_state: undefined,
    in_type: undefined,
    car_photo_url: ''
  },
  // 停车订单
  parkOrder: {
    id: undefined,
    park_id: undefined,
    park_region_id: undefined,
    park_in_record_id: undefined,
    total_money: undefined,
    duration: undefined,
    duration_text: undefined,
    payed_money: undefined,
    current_coupon_money: undefined,
    history_payed_money: undefined,
    cash_zero: undefined,
    details: [],
    to_time: undefined,
    order_no: undefined,
    should_pay_money: undefined,
    order_state: undefined
  },
  // 计费方式
  pre_car_type: 0,
  car_type: null,
  // 入场记录
  inRecords: [],
  // 出场记录
  outRecords: [],
  // 优免券
  usedCouponFlag: false,
  availableCoupons: [],
  // 无入场记录展示
  carInRecord: {
    display: false,
    tip: undefined
  },
  // 支付成功展示
  completePayed: {
    display: false,
    tip: undefined
  }
});
const rankTableData = ref([]);
const inTypeList = ref({
  1: '正常入场',
  2: '人工放行',
  3: '手动登记',
  4: '重复入场',
  5: '手动补录',
  6: '批量导入',
  7: '跟车入场',
  8: '断网补录入场',
  9: '折返事件'
});
const rankTimer = ref(null);

onMounted(() => {
  // 添加WebSocket通知监听
  window.addEventListener('onmessage', handleWebSocketMessage);

  // 初始化字典值
  initDict();

  // 刷新应收和实收、空闲车位
  // statParkStatus();

  querySentryGateway();
});

onUnmounted(() => {
  window.removeEventListener('onmessage', handleWebSocketMessage);
  stopAutoRefresh();
});
const emits = defineEmits(['first-handler']);
const firstHandler = (row) => {
  emits('first-handler', row.gateway_id);
};
const tableData = ref([]);
// tableData.value = tableData.value.concat(tableData.value)
const debt_moneyAll = ref(0);
const getTableData = async () => {
  // 加载中
  const params = {
    plate_no: state.plate_no,
    park_id: state.parkOrder.park_id || '',
    order_states: [4]
  };
  try {
    // 获取事件列表
    const { data: resData, message, success } = await ArrearsOfFeesApi.selectByPage(params);
    if (success) {
      // 设置表格数据
      debt_moneyAll.value = 0;
      resData.rows.forEach((item) => {
        if (item.should_pay_money >= 0) {
          debt_moneyAll.value += item.should_pay_money;
        }
      });
      debt_moneyAll.value = debt_moneyAll.value.toFixed(2);
      tableData.value = resData.rows;
      if (resData.rows.length == 0) {
        stopAutoRefresh();
      }
      // 设置总条数
    } else {
      // 弹出错误信息
      ElMessage({
        message: message,
        type: 'error'
      });
      stopAutoRefresh();
    }
  } catch (error) {
    // 弹出错误信息
    ElMessage({
      message: error,
      type: 'error'
    });
    stopAutoRefresh();
  } finally {
    // 加载完成
  }
};
// 查询岗亭值守的通道信息
const querySentryGateway = () => {
  const param = {
    sentry_id: setting.sentry_id
  };
  mockCarService.querySentryGateway(param).then((res) => {
    if (res.success) {
      manualRecord.in_gateways = res.data.filter((item) => item.type === 2);
    } else {
      ElMessage({
        message: res.detail_message ? res.detail_message : res.message,
        type: 'error'
      });
    }
  });
};

// 处理WebSocket消息
const handleWebSocketMessage = (msg) => {
  if (msg.detail.command === 's2c.car-event' && parseInt(msg.detail.capture_event.gateway_id) === parseInt(props.gateway.id)) {
    clearData();
    autoCapture(msg.detail);
  } else if (msg.detail.command === 's2c.car-pay-over-event' && parseInt(msg.detail.gateway_id) === parseInt(props.gateway.id)) {
    completePay(msg.detail);
  }
};

// 自动抓拍(WebSocket)
const autoCapture = (msg) => {
  console.log(msg);
  // 车辆进入通道
  state.capture = msg.capture_event;
  state.capture.pic_path_url = msg.pic_path_url;
  state.capture.pic_path2_url = msg.pic_path2_url;
  refreshTimes.value = 0;
  state.plate_no = msg.capture_event.plate_no;
  state.car_type = msg.capture_event.plate_type;
  // 出场业务流水号
  state.car_biz_no = msg.car_biz_no;
  // 是否存在入场记录
  state.has_car_in_record = msg.has_car_in_record;
  // 停车类型
  state.stop_car_type = msg.stop_car_type;
  // 备注
  state.memo = msg.memo;

  // 生成缴费订单(无需刷新订单)
  if (msg.park_order) {
    state.parkOrder = msg.park_order;
    //判断是否是0元订单
    if (msg.park_order.order_money == '0') {
      //已经放行，取消收费放行按钮可点击
    }
  }

  // 查询最近一条入场记录
  if (msg.has_car_in_record) {
    // 有入场记录，为收费放行，属于正常离场
    state.out_type = 1;

    if (msg.car_in_record) {
      state.carInRecord = msg.car_in_record;
    }

    queryLastParkInRecord();

    // 设置无入场记录的消息提示
    state.carInRecord = {
      display: false,
      tip: undefined
    };

    // 压测模式
    if (setting.pressure_test) {
      handleChargeAndPass();
    }
  } else {
    // 无入场记录，默认为人工放行
    state.out_type = 2;

    // 设置无入场记录的消息提示
    state.carInRecord = {
      display: true,
      tip: '注意：此车辆无入场记录'
    };

    // 压测模式
    if (setting.pressure_test) {
      manualLift.form = {
        money: 0,
        out_reason: 0,
        memo: ''
      };
      calcManualLiftCarFee(manualLiftFormRef);
    }
  }

  // 查询入场记录
  queryParkInRecord();

  // 查询出场记录
  queryParkOutRecord();

  // 查询可用优免券
  queryAvailableCoupons();

  // 查询历史欠费
  // getTableData()
  handleRefreshOrder();
};

// 完成支付
const completePay = (msg) => {
  stopAutoRefresh();
  state.parkOrder.pay_type = msg.pay_type;
  if (msg.pay_type === 1) {
    console.log('1');
    //缴费现金支付
    state.parkOrder.payed_money = msg.payed_money;
    state.parkOrder.should_pay_money = 0;
    state.parkOrder.cash_zero = new Big(msg.payed_money).minus(msg.order_money);
    state.completePayed = {
      display: true,
      tip: '车辆：' + msg.plate_no + ' 已完成支付，支付金额：￥' + msg.payed_money
    };

    // 收费成功，刷新车场统计信息
    // statParkStatus();
  } else if (msg.pay_type === 3) {
    console.log('2');
    //etc支付
    state.parkOrder.should_pay_money = 0;
    state.parkOrder.history_payed_money = msg.payed_money;
    state.completePayed = {
      display: true,
      tip: '车辆：' + msg.plate_no + ' 已完成支付，支付金额：￥' + msg.payed_money
    };

    // 收费成功，刷新车场统计信息
    // statParkStatus();
  } else if (msg.pay_type === 0 && msg.gateway_id == props.gateway.id && state.car_type == 0) {
    console.log('3');
    // 无入场记录展示
    state.carInRecord = {
      display: false,
      tip: undefined
    };
    // 无牌车H5支付后刷新
    state.parkOrder.id = msg.order_id;
    state.plate_no = msg.plate_no;
    refreshOrderFn(0);
    // 查询最近一条入场记录
    queryLastParkInRecord();
    state.completePayed = {
      display: true,
      tip: '车辆：' + msg.plate_no + ' 已完成支付，支付金额：￥' + msg.payed_money
    };
    state.memo = undefined;
  } else if (msg.car_out_biz_no === state.car_biz_no) {
    console.log('4');
    refreshOrderFn(0);

    state.completePayed = {
      display: true,
      tip: '车辆：' + msg.plate_no + ' 已完成支付，支付金额：￥' + msg.payed_money
    };

    state.memo = undefined;

    // 支付完成，刷新车场统计信息
    // statParkStatus();
  }
};

// 初始化字典值
const initDict = () => {
  const param = [
    {
      enum_key: 'car_type_options',
      enum_value: 'EnumCarType'
    },
    {
      enum_key: 'out_reason_options',
      enum_value: 'EnumOutReason'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    car_type_options.value = response.data.car_type_options;
    out_reason_options.value = response.data.out_reason_options;
  });
};

// 查询最近一条入场记录
const queryLastParkInRecord = () => {
  if (state.plate_no === undefined) {
    return;
  }

  const param = {
    park_region_id: props.gateway.park_region_id,
    plate_no: state.plate_no
  };
  watchService.queryLastParkInRecord(param).then((res) => {
    if (res.success) {
      if (res.data) {
        state.lastParkInRecord = res.data;
        state.car_type = res.data.car_type;
        state.pre_car_type = res.data.car_type;
      }
      console.log('state===', state);
      // 白名单不调用生成订单的方法
      if ((state.parkOrder.id === undefined && state.stop_car_type === 1) || state.out_type == 4) {
        getParkOrder(0);
      }
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 按入场时间生成当前需缴费订单
const getParkOrder = (require_refresh_order) => {
  if (state.lastParkInRecord === null) {
    ElMessage({
      message: '无入场记录，不能生成订单',
      type: 'warning'
    });
    return;
  }

  const param = {
    car_in_record_id: state.lastParkInRecord.id,
    require_refresh_order: require_refresh_order,
    car_type: state.car_type
  };
  // 无牌车手动匹配时候的处理
  if (state.out_type == 4) {
    param.prk_gateway_id = props.gateway.id;
  }

  watchService.getParkOrder(param).then((res) => {
    if (res.success) {
      state.parkOrder = res.data;

      // 手动匹配处理
      if (state.out_type === 4) {
        if (state.parkOrder.order_state === 2 || state.parkOrder.should_pay_money === '0.00') {
          state.memo = '临停车，已支付完成或待支付金额为0，自动开闸离场';
          if (state.car_type != 0) {
            testLift(props.gateway);
          }
        } else {
          state.memo = '临停车，正常计费，等待支付';
        }
      }
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 自动抬杆
const testLift = (gateway) => {
  const param = {
    gateway_id: gateway.id,
    direction: gateway.type
  };
  watchService.liftRoll(param).then((res) => {
    if (res.success) {
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 重新选择计费方式
const refreshOrderFn = (timeNum) => {
  setTimeout(() => {
    if (state.parkOrder === null) {
      ElMessage({
        message: '无订单信息，不允许刷新订单',
        type: 'warning'
      });
      return;
    }
    const param = {
      park_order_id: state.parkOrder.id,
      car_type: state.car_type
    };
    console.log('paramparamparamparamparamparam', param);
    watchService.refreshOrder(param).then((res) => {
      if (res.success) {
        ElMessage({
          message: '订单刷新成功',
          type: 'success'
        });

        state.parkOrder = res.data;
        // state.parkOrder.payed_money = parseInt(state.parkOrder.payed_money);
        // state.parkOrder.total_money = parseInt(state.parkOrder.total_money);
        // state.parkOrder.current_coupon_money = parseInt(state.parkOrder.current_coupon_money);
        // state.parkOrder.history_payed_money = parseInt(state.parkOrder.history_payed_money);
        // state.parkOrder.history_derate_coupon_money = parseInt(state.parkOrder.history_derate_coupon_money);
        state.parkOrder.cash_zero = 0;
        // state.parkOrder.should_pay_money = parseInt(state.parkOrder.should_pay_money);
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  }, timeNum);
};

// 修正入场信息
const handleUpdatePlateNo = (plate_no) => {
  //普通
  var c_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNOPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
  //特种
  var ts_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{4}[学挂领试超练警]{1}$/u;
  //武警
  var wj_reg = /^WJ[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]?[0-9A-Z]{5}$/iu;
  //军牌
  var j_reg = /^[QVKHBSLJNGCEZ]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
  //新能源
  // 小型车
  var xs_reg =
    /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[DF]{1}[1-9ABCDEFGHJKLMNPQRSTUVWXYZ]{1}[0-9]{4}$/u;

  // 大型车
  var xb_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9]{5}[DF]{1}$/u;
  //民航
  var mh_reg = /^民航[0-9A-Z]{5}$/u;
  //使馆
  var s_reg = /^[1-3]{1}[0-9]{2}[0-9A-Z]{3}使$/u;
  var s1_reg = /^使[0-9]{6}$/u;
  //领馆
  var l_reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[1-3]{1}[0-9]{2}[0-9A-Z]{2}领$/u;
  //应急
  var yj_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[0-9A-Z]{5}应急$/u;
  //判断并进行拆分
  if (c_reg.test(plate_no) || xs_reg.test(plate_no) || xb_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1);
    plateNo.form.plateNoThirdly = '无';
  }
  if (ts_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1, plate_no.length - 1);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 1);
  }
  if (wj_reg.test(plate_no) || j_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = '无';
    plateNo.form.plateNoSecond = plate_no;
    plateNo.form.plateNoThirdly = '无';
  }
  if (mh_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 2);
    plateNo.form.plateNoSecond = plate_no.substring(2);
    plateNo.form.plateNoThirdly = '无';
  }
  if (s_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = '无';
    plateNo.form.plateNoSecond = plate_no.substring(0, plate_no.length - 1);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 1);
  }
  if (s1_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1);
    plateNo.form.plateNoThirdly = '无';
  }
  if (l_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1, plate_no.length - 1);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 1);
  }
  if (yj_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1, plate_no.length - 2);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 2);
  }
  plateNo.noPlateCarFlag = '0';
  plateNoDialogVisible.value = true;
};
//修改车牌号提交
const submitPlateNo = () => {
  if (plateNo.noPlateCarFlag == '0') {
    //有车牌车
    if (plateNo.form.plateNoFirst == undefined) {
      ElMessage({
        message: '请输入车牌号！',
        type: 'error'
      });
      return;
    }

    if (plateNo.form.plateNoSecond == undefined) {
      ElMessage({
        message: '请输入车牌号！',
        type: 'error'
      });
      return;
    }

    if (plateNo.form.plateNoThirdly == undefined) {
      ElMessage({
        message: '请输入车牌号！',
        type: 'error'
      });
      return;
    }
    var regex = /^[0-9A-Z]+$/;
    if (!regex.test(plateNo.form.plateNoSecond)) {
      ElMessage({
        message: '请输入正确的车牌号！',
        type: 'error'
      });
      return;
    }
    const param = {
      first_no: plateNo.form.plateNoFirst,
      mid_no: plateNo.form.plateNoSecond,
      last_no: plateNo.form.plateNoThirdly,
      id: state.lastParkInRecord.id,
      no_plate_car: 0,
      gateway_id: props.gateway.id
    };
    var plate_no = '';
    if (plateNo.form.plateNoFirst != '无' && plateNo.form.plateNoThirdly != '无') {
      plate_no = plateNo.form.plateNoFirst + plateNo.form.plateNoSecond + plateNo.form.plateNoThirdly;
    }
    if (plateNo.form.plateNoFirst == '无' && plateNo.form.plateNoThirdly != '无') {
      plate_no = plateNo.form.plateNoSecond + plateNo.form.plateNoThirdly;
    }
    if (plateNo.form.plateNoThirdly == '无' && plateNo.form.plateNoFirst != '无') {
      plate_no = plateNo.form.plateNoFirst + plateNo.form.plateNoSecond;
    }
    if (plateNo.form.plateNoFirst == '无' && plateNo.form.plateNoThirdly == '无') {
      plate_no = plateNo.form.plateNoSecond;
    }
    watchService.modifyPlateNo(param).then((res) => {
      if (res.success) {
        ElMessage({
          type: 'success',
          message: '修改车牌号成功'
        });
        state.plate_no = plate_no;
        plateNoDialogVisible.value = false;
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  } else {
    //无牌车
    const plateParam = {
      park_id: state.lastParkInRecord.park_id
    };
    watchService.generateCarNoPlateNo(plateParam).then((res) => {
      if (res.success) {
        updatePlateNo(res.data.plate_no);
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  }
};
const handlePlateNoClose = () => {
  plateNoDialogVisible.value = false;
};

const updatePlateNo = (plate_no) => {
  const param = {
    first_no: plateNo.form.plateNoFirst,
    mid_no: plate_no,
    last_no: plateNo.form.plateNoThirdly,
    id: state.lastParkInRecord.id,
    no_plate_car: 1,
    gateway_id: props.gateway.id
  };
  watchService.modifyPlateNo(param).then((res) => {
    if (res.success) {
      ElMessage({
        type: 'success',
        message: '修改车牌号成功'
      });
      state.plate_no = plate_no;
      plateNoDialogVisible.value = false;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
//重置
const resetPlateNo = () => {
  plateNo.form = {
    plateNoFirst: undefined,
    plateNoSecond: undefined,
    plateNoThirdly: undefined
  };
  plateNo.noPlateCarFlag = '0';
};
const changeToNoPlate = (val) => {
  if (val == '1') {
    plateNo.form = {
      plateNoFirst: undefined,
      plateNoSecond: undefined,
      plateNoThirdly: undefined
    };
  }
};

// 取消优免
const handleCancelCoupon = () => {
  if (state.parkOrder === null) {
    ElMessage({
      message: '无订单信息，不允许取消优免操作',
      type: 'warning'
    });
    return;
  }

  if (state.parkOrder.order_state === 2) {
    ElMessage({
      message: '订单已完成，不允许取消优免操作',
      type: 'warning'
    });
    return;
  }

  const param = {
    park_order_id: state.parkOrder.id
  };
  watchService.cancelFreeCoupon(param).then((res) => {
    if (res.success) {
      state.parkOrder = res.data;

      state.usedCouponFlag = false;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 定义变量
const refreshInterval = ref(null); // 定时器
// 启动定时刷新
const startAutoRefresh = () => {
  // 先清除已有的定时器
  stopAutoRefresh();
  if (!state.has_car_in_record || !state.parkOrder) {
    return;
  }

  // 设置每10秒执行一次的定时器
  refreshInterval.value = setInterval(() => {
    if (tableData.value.length > 0 && props.isActived) {
      getTableData();
    }

    if (
      route.name != 'Watch' ||
      state.plate_no === undefined ||
      state.completePayed.display ||
      state.parkOrder.order_state === 2 ||
      (state.memo !== undefined && state.memo.includes('白名单'))
    ) {
      stopAutoRefresh();
    }
  }, 30000);
};

// 停止定时刷新
const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
  }
};

const getRankData = () => {
  watchService.queryCarOutQueueRecords({}).then((res) => {
    rankTableData.value = res.data;
  });
};

const rank15 = () => {
  getRankData();
  rankTimer.value = setInterval(() => {
    // 每隔10秒查询一次排队车辆
    console.log('排队车辆15s');
    getRankData();
  }, 15000);
};

onActivated(() => {
  console.log('进入页面');
  if (rankTimer.value) {
    clearInterval(rankTimer.value);
  }
  rank15();
  if (!state.parkOrder.id) {
    return;
  }
  startAutoRefresh();
});

onDeactivated(() => {
  console.log('离开页面');
  if (rankTimer.value) {
    clearInterval(rankTimer.value);
  }
  stopAutoRefresh();
});
const handleRefreshOrderClick = throttle(
  () => {
    handleRefreshOrder();
  },
  10000,
  { leading: true, trailing: false } // leading: true 表示立即执行
);
const handleRefreshOrder = () => {
  if (!state.has_car_in_record || !state.parkOrder) {
    ElMessage({
      message: '没有入场记录和订单信息，无法刷新订单',
      type: 'warning'
    });
    stopAutoRefresh();
    return;
  }

  // 刷新订单
  // ElMessage({
  //   message: '订单刷新操作已提交',
  //   type: 'info'
  // });
  if (state.parkOrder == null || state.parkOrder.id == null) {
    // 不存在订单，生成订单，且为临停车
    if (state.stop_car_type === 1) {
      getParkOrder(0);
    }
  } else {
    // 存在订单，刷新订单
    refreshOrderFn(800);
  }
  getTableData();
};

// 切换费率
const switchCarType = () => {
  if (state.parkOrder === null) {
    ElMessage({
      message: '无订单信息，不允许刷新订单',
      type: 'warning'
    });
    return;
  }

  const param = {
    park_order_id: state.parkOrder.id,
    park_order_no: state.order_no,
    pre_order_money: state.parkOrder.total_money,
    pre_car_type: state.pre_car_type,
    car_type: state.car_type
  };
  watchService.switchCarType(param).then((res) => {
    if (res.success) {
      ElMessage({
        message: '切换计费方式成功',
        type: 'success'
      });

      state.parkOrder = res.data;
      state.pre_car_type = state.car_type;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 计费方式改变
const handleChangeCarType = (e) => {
  if (!state.has_car_in_record || !state.parkOrder) {
    ElMessage({
      message: '没有入场记录和订单信息，无法刷新订单',
      type: 'warning'
    });
    // 恢复到之前的车辆类型
    state.car_type = state.pre_car_type;
    return;
  }

  // 显示确认弹窗
  ElMessageBox.confirm('是否确认切换费率？', '切换费率', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    closeOnClickModal: false,
    closeOnPressEscape: false
  })
    .then(() => {
      // 用户点击"是"，执行切换
      ElMessage({
        message: '切换计费方式操作已提交',
        type: 'info'
      });
      switchCarType();
    })
    .catch(() => {
      // 用户点击"否"或关闭弹窗，恢复到之前的车辆类型
      state.car_type = state.pre_car_type;
      ElMessage({
        message: '已取消切换费率',
        type: 'info'
      });
    });
};

// 手动抓拍
const handleManualCapture = () => {
  const param = {
    gateway_id: props.gateway.id,
    direction: 1 // 出口
  };
  watchService.manualCapture(param).then((res) => {
    if (res.success) {
      ElMessage({
        message: res.message,
        type: 'success'
      });
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 收费放行
const handleChargeAndPass = () => {
  if (state.parkOrder === null) {
    ElMessage({
      message: '无订单信息，不允许收费放行',
      type: 'warning'
    });
    return;
  }
  if (state.lastParkInRecord === null) {
    ElMessage({
      message: '无最近入场记录，不允许收费放行',
      type: 'warning'
    });
    return;
  }
  if (state.car_biz_no === null || state.car_biz_no === undefined) {
    ElMessage({
      message: '无出场流水号，不允许收费放行',
      type: 'warning'
    });
    return;
  }

  const param = {
    car_in_biz_no: state.lastParkInRecord.car_in_biz_no,
    car_out_biz_no: state.car_biz_no,
    money: state.parkOrder.should_pay_money,
    park_order_no: state.parkOrder.order_no,
    out_type: state.out_type,
    out_state: 1 // 正常离场
  };
  watchService.chargeAndPass(param).then((res) => {
    if (res.success) {
      ElMessage({
        message: '收费放行成功',
        type: 'success'
      });

      // 收费成功，清空数据
      clearData();

      // 收费成功，刷新车场统计信息
      // statParkStatus();
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 手动抬杆
const manualLiftFormRef = ref();
const manualLift = reactive({
  dialogVisible: false,
  form: {
    money: undefined,
    out_reason: undefined,
    memo: undefined
  },
  rules: {
    money: [{ required: true, message: '请输入放行金额', trigger: 'blur' }],
    out_reason: [{ required: true, message: '请选择放行原因', trigger: 'change' }]
  }
});
// 打开手动抬杆对话框
const handleManualLift = () => {
  manualLift.dialogVisible = true;
};
// 关闭手动抬杆对话框
const handleManualLiftClose = () => {
  manualLiftFormRef.value.resetFields();
  manualLift.dialogVisible = false;
};

// 手动抬杆 - 人工放行
const calcManualLiftCarFee = (formRef) => {
  // 重置出场类型，人工放行
  state.out_type = 2;

  if (state.parkOrder === null) {
    ElMessage({
      message: '无订单信息，不允许手动抬杆',
      type: 'warning'
    });
    return;
  }
  if (state.lastParkInRecord === null) {
    ElMessage({
      message: '无最近入场记录，不允许手动抬杆',
      type: 'warning'
    });
    return;
  }
  if (state.car_biz_no === null || state.car_biz_no === undefined) {
    ElMessage({
      message: '无出场流水号，不允许手动抬杆',
      type: 'warning'
    });
    return;
  }

  formRef.validate().then(() => {
    const param = {
      car_in_biz_no: state.lastParkInRecord.car_in_biz_no,
      car_out_biz_no: state.car_biz_no,
      park_order_no: state.parkOrder.order_no,
      money: manualLift.form.money,
      out_reason: manualLift.form.out_reason,
      memo: manualLift.form.memo,
      out_type: state.out_type,
      out_state: 2 // 异常离场
    };
    watchService.chargeAndPass(param).then((res) => {
      if (res.success) {
        ElMessage({
          message: '手动抬杆放行成功',
          type: 'success'
        });

        formRef.resetFields();
        manualLift.dialogVisible = false;

        // 清空数据
        clearData();
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
};
// 手动抬杆 - 重置
const resetManualLiftCarFee = (formRef) => {
  if (!formRef) return;
  formRef.resetFields();
};

// 手动匹配
const manualPass = reactive({
  dialogVisible: false,
  // 有牌车查询条件和列表数据
  table: {
    data: []
  },
  query: {
    page: 1,
    limit: 10,
    start_time: undefined,
    end_time: undefined,
    plate_no: undefined
  },
  total: 0,
  // 无牌车查询条件和列表数据
  table0: {
    data: []
  },
  query0: {
    page: 1,
    limit: 10,
    start_time: undefined,
    end_time: undefined
  },
  total0: 0,
  daterange: [],
  daterange0: [],
  loading: false,
  flag: false
});
const imageDialogVisible = ref(false);
const dialogImageUrl = ref(undefined);
// 打开手动离场对话框
const handleManualPass = () => {
  manualPass.dialogVisible = true;
};
// 关闭手动匹配对话框
const handleManualPassClose = () => {
  manualPass.table.data = [];
  manualPass.table0.data = [];
  manualPass.query = {
    page: 1,
    limit: 10,
    start_time: undefined,
    end_time: undefined,
    plate_no: undefined
  };
  manualPass.query0 = {
    page: 1,
    limit: 10,
    start_time: undefined,
    end_time: undefined
  };
  manualPass.dialogVisible = false;
};

const handlePlate = () => {
  manualPass.flag = false;
};

const handleNoPlate = () => {
  manualPass.flag = true;
};

const showImage = (val, type) => {
  if (type) {
    ElMessage({
      message: '设备暂不支持视频',
      type: 'warning'
    });
    return;
  }
  dialogImageUrl.value = val;
  imageDialogVisible.value = true;
};

// 手动补录
const manualRecordFormRef = ref();
const manualRecord = reactive({
  dialogVisible: false,
  form: {
    park_in_gateway_id: undefined,
    park_out_gateway_id: props.gateway.id,
    in_time: undefined,
    plate_no: state.plate_no
  },
  in_gateways: []
});

//打开手动补录
const handleManualRecord = () => {
  manualRecord.form = {
    park_in_gateway_id: undefined,
    in_time: undefined,
    plate_no: undefined
  };
  manualRecord.dialogVisible = true;
};

//关闭手动补录
const handleManualRecordClose = () => {
  manualRecord.form = {
    in_time: undefined,
    plate_no: undefined,
    park_in_gateway_id: undefined
  };
  manualRecord.dialogVisible = false;
};

// 防抖工具函数
function debounce(fn, delay = 800) {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

//手动补录提交
const submitManualRecord = debounce((manualRecordFormRef) => {
  if (manualRecord.form.in_time === null || manualRecord.form.in_time === undefined) {
    ElMessage({
      message: '请选择入场时间，无入场时间无法补录！',
      type: 'warning'
    });
    return;
  }
  if (dayjs(manualRecord.form.in_time).isAfter(dayjs(state.capture.event_time))) {
    ElMessage({
      message: '入场时间不能大于进场时间！',
      type: 'warning'
    });
    return;
  }
  if (manualRecord.form.park_in_gateway_id === null || manualRecord.form.park_in_gateway_id === undefined) {
    ElMessage({
      message: '请选择入场通道，无入场时间无法补录！',
      type: 'warning'
    });
    return;
  }
  manualRecordFormRef.validate().then(() => {
    console.log(manualRecord.form);
    const param = {
      in_time: manualRecord.form.in_time,
      plate_no: state.plate_no,
      park_in_gateway_id: manualRecord.form.park_in_gateway_id,
      park_out_gateway_id: props.gateway.id
    };
    watchService.manualRecordCarInRecord(param).then((res) => {
      if (res.success) {
        ElMessage({
          message: '手动补录成功',
          type: 'success'
        });
        queryLastParkInRecord();
        // 查询入场记录
        queryParkInRecord();
        // 查询出场记录
        queryParkOutRecord();
        // 查询可用优免券
        queryAvailableCoupons();

        state.carInRecord = {
          display: false,
          tip: undefined
        };
        state.has_car_in_record = true;
        state.parkOrder = res.data;

        if (res.data.park_in_record_id != null) {
          state.memo = '临停车，正常计费, 等待支付';
        }
        // 订单刷新
        handleRefreshOrder();
        manualRecordFormRef.resetFields();
        manualRecord.dialogVisible = false;
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
}, 800);

// 查询手动匹配车辆最近入场记录
const handleQueryParkInRecord = () => {
  let param;
  if (!manualPass.flag) {
    if (manualPass.query.plate_no === undefined) {
      ElMessage({
        message: '请输入车牌号',
        type: 'info'
      });
      return;
    }
    if (manualPass.daterange.length > 0) {
      manualPass.query.start_time = manualPass.daterange[0] + ' 00:00:00';
      manualPass.query.end_time = manualPass.daterange[1] + ' 23:59:59';
    } else {
      ElMessage({
        message: '请选择入场时间',
        type: 'info'
      });
      return;
    }
    param = { ...manualPass.query, has_plate_no: !manualPass.flag, out_state: 0 };
  } else {
    if (manualPass.daterange0.length > 0) {
      manualPass.query0.start_time = manualPass.daterange0[0] + ' 00:00:00';
      manualPass.query0.end_time = manualPass.daterange0[1] + ' 23:59:59';
    } else {
      ElMessage({
        message: '请选择入场时间',
        type: 'info'
      });
      return;
    }
    param = { ...manualPass.query0, has_plate_no: !manualPass.flag, out_state: 0 };
  }
  manualPass.loading = true;
  stantAccountService.pageParkInRecord(param).then((res) => {
    if (res.success) {
      if (!manualPass.flag) {
        manualPass.table.data = res.data.rows;
        manualPass.total = parseInt(res.data.total);
      } else {
        manualPass.table0.data = res.data.rows;
        manualPass.total0 = parseInt(res.data.total);
      }
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
    manualPass.loading = false;
  });
};
const handleSizeChange = (val) => {
  manualPass.query.limit = val;
  handleQueryParkInRecord();
};
const handleCurrentChange = (val) => {
  manualPass.query.page = val;
  handleQueryParkInRecord();
};
const handleSize0Change = (val) => {
  manualPass.query0.limit = val;
  handleQueryParkInRecord();
};
const handleCurrent0Change = (val) => {
  manualPass.query0.page = val;
  handleQueryParkInRecord();
};
// 手动匹配 - 车辆计费
const calcManualPassCarFee = (row) => {
  // 重置出场类型，4-匹配放行
  state.out_type = 4;

  // 设置参数
  state.plate_no = row.plate_no;
  state.has_car_in_record = true;

  // 查询最近一条入场记录
  queryLastParkInRecord();

  // 查询入场记录
  queryParkInRecord();

  // 查询出场记录
  queryParkOutRecord();

  // 查询可用优免券
  queryAvailableCoupons();

  manualPass.dialogVisible = false;

  // 去掉下方提示
  state.carInRecord = {
    display: false,
    tip: undefined
  };
  // 去掉消息memo
  state.memo = undefined;
};

// 特殊放行
const specialPassFormRef = ref();
const specialPass = reactive({
  dialogVisible: false,
  form: {
    money: undefined,
    out_reason: undefined,
    memo: undefined
  },
  rules: {
    // money: [{ required: true, message: '请输入放行金额', trigger: 'blur' }],
    out_reason: [{ required: true, message: '请选择放行原因', trigger: 'change' }]
  }
});
// 打开特殊放行对话框
const handleSpecialPass = () => {
  specialPass.form = {
    money: state.parkOrder.should_pay_money,
    out_reason: undefined,
    memo: undefined
  };

  specialPass.dialogVisible = true;
};
// 关闭特殊放行对话框
const handleSpecialPassClose = () => {
  specialPassFormRef.value.resetFields();
  specialPass.dialogVisible = false;
};
// 特殊放行 - 车辆计费
const calcSpecialPassCarFee = (formRef) => {
  // 重置出场类型，特殊放行
  state.out_type = 3;

  if (state.parkOrder === null) {
    ElMessage({
      message: '无订单信息，不允许特殊放行',
      type: 'warning'
    });
    return;
  }
  if (state.lastParkInRecord === null) {
    ElMessage({
      message: '无最近入场记录，不允许特殊放行',
      type: 'warning'
    });
    return;
  }
  if (state.car_biz_no === null || state.car_biz_no === undefined) {
    ElMessage({
      message: '无出场流水号，不允许特殊放行',
      type: 'warning'
    });
    return;
  }

  formRef.validate().then(() => {
    const param = {
      park_order_no: state.parkOrder.order_no,
      car_in_biz_no: state.lastParkInRecord.car_in_biz_no,
      car_out_biz_no: state.car_biz_no,
      money: specialPass.form.money,
      out_reason: specialPass.form.out_reason,
      memo: specialPass.form.memo
    };

    watchService.specialPass(param).then((res) => {
      if (res.success) {
        ElMessage({
          message: '特殊放行成功',
          type: 'success'
        });

        formRef.resetFields();
        specialPass.dialogVisible = false;

        clearData();
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
};
// 特殊放行 - 重置
const resetSpecialPassCarFee = (formRef) => {
  if (!formRef) return;
  formRef.resetFields();
};

// 取消放行
const handleCancelPass = () => {
  ElMessageBox.confirm('是否需要取消放行？', '取消放行', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  })
    .then(() => {
      const param = {
        gateway_id: props.gateway.id,
        direction: 1, // 出口
        plate_no: state.plate_no
      };
      watchService.cancelPass(param).then((res) => {
        if (res.success) {
          ElMessage({
            message: '取消放行成功',
            type: 'success'
          });

          clearData();
        } else {
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
            type: 'error'
          });
        }
      });
    })
    .catch(() => {});
};

// 查询入场记录
const queryParkInRecord = () => {
  if (state.plate_no === undefined) {
    return;
  }

  const param = {
    park_region_id: props.gateway.park_region_id,
    plate_no: state.plate_no,
    topn: 10,
    has_plate_no: false
  };
  if (state.plate_no != undefined) {
    param.has_plate_no = true;
  }
  watchService.queryParkInRecord(param).then((res) => {
    if (res.success) {
      state.inRecords = res.data;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 查询出场记录
const queryParkOutRecord = () => {
  if (state.plate_no === undefined) {
    return;
  }

  const param = {
    park_region_id: props.gateway.park_region_id,
    plate_no: state.plate_no,
    topn: 10
  };
  watchService.queryParkOutRecord(param).then((res) => {
    if (res.success) {
      state.outRecords = res.data;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 查询可用优免券
const queryAvailableCoupons = () => {
  if (state.plate_no === undefined) {
    return;
  }

  const param = {
    plate_no: state.plate_no
  };
  watchService.queryAvailableCoupons(param).then((res) => {
    if (res.success) {
      state.availableCoupons = res.data;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 使用优免券
const handleUseCoupon = (row) => {
  if (state.parkOrder == null) {
    ElMessage({
      message: '无订单信息，不可使用优免券',
      type: 'warning'
    });
    return;
  }

  const param = {
    park_order_id: state.parkOrder.id,
    coupon_no: row.coupon_no
  };
  watchService.useCoupon(param).then((res) => {
    if (res.success) {
      state.parkOrder = res.data;

      if (state.parkOrder.should_pay_money === '0') {
        handleChargeAndPass();
      } else {
        // 已使用优免券，不可再点使用
        state.usedCouponFlag = true;
        // 查询可用的优免券
        queryAvailableCoupons();
      }

      if (res.data.order_state === 2) {
        state.memo = '订单已完成，自动放行';
      }
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 清空数据
const clearData = () => {
  // 当前车牌号
  state.plate_no = undefined;
  // 是否有入场记录
  state.has_car_in_record = true;
  // 停车类型
  state.stop_car_type = undefined;
  // 备注
  state.memo = undefined;
  // 抓拍记录
  state.capture = {
    gateway_id: undefined,
    direction: undefined,
    ip: undefined,
    plate_no: undefined,
    plate_color: undefined,
    car_color: undefined,
    trigger_type: undefined,
    pic_path: '',
    pic_path2: '',
    take_direction: undefined,
    pic_path_url: '',
    pic_path2_url: '',
    event_time: undefined
  };
  // 出场类型
  state.out_type = 2;
  // 出场业务流水号
  state.car_biz_no = undefined;
  // 车辆最近一条入场记录
  state.lastParkInRecord = {
    id: undefined,
    car_in_biz_no: undefined,
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    park_region_name: undefined,
    gateway_id: undefined,
    gateway_name: undefined,
    in_time: undefined,
    plate_no: undefined,
    car_type: undefined,
    car_type_desc: undefined,
    car_color: undefined,
    car_photo: '',
    car_photo_cloud_file_id: undefined,
    car_photo_sync_to_cloud: undefined,
    out_state: undefined,
    in_type: undefined,
    car_photo_url: ''
  };
  // 停车订单
  state.parkOrder = {
    id: undefined,
    park_id: undefined,
    park_region_id: undefined,
    park_in_record_id: undefined,
    total_money: undefined,
    duration: undefined,
    duration_text: undefined,
    payed_money: undefined,
    current_coupon_money: undefined,
    history_payed_money: undefined,
    details: [],
    to_time: undefined,
    order_no: undefined,
    should_pay_money: undefined,
    order_state: undefined
  };
  // 计费方式
  state.pre_car_type = 0;
  state.car_type = null;
  // 入场记录
  state.inRecords = [];
  // 出场记录
  state.outRecords = [];
  // 优免券
  state.usedCouponFlag = false;
  state.availableCoupons = [];
  // 无入场记录展示
  state.carInRecord = {
    display: false,
    tip: undefined
  };
  // 支付成功展示
  state.completePayed = {
    display: false,
    tip: undefined
  };
  tableData.value = [];
  stopAutoRefresh();
};

// 刷新应收和实收、空闲车位
// const statParkStatus = () => {
//   const shift_record_id = user.shift_handover_record_id;
//   watchService.statParkStatus(shift_record_id).then((res) => {
//     if (res.success) {
//       user.total_money = res.data.total_money;
//       user.payed_money = res.data.payed_money;
//       user.free_space = res.data.free_space;
//       user.space_adjust = res.data.space_adjust;
//     } else {
//       ElMessage({
//         dangerouslyUseHTMLString: true,
//         message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
//         type: 'error'
//       });
//     }
//   });
// };
// 刷新图片的次数
const refreshTimes = ref(0);
// 处理图片加载错误再次请求
const refresh = (e) => {
  if (state.capture.pic_path_url === undefined) return;
  if (refreshTimes.value > 10) return;
  refreshTimes.value++;
  setTimeout(() => {
    const url = state.capture.pic_path_url.split('?')[0];
    state.capture.pic_path_url = `${url}?version=${Date.now()}`;
  }, 1000);
};
watch(
  () => props.isActived,
  (newId) => {
    console.log(newId, 'props.isActived,watch监听tab 出口执行排队车辆查询');
    if (rankTimer.value) {
      clearInterval(rankTimer.value);
    }
    if (!newId) {
      stopAutoRefresh();
      setTimeout(() => {
        stopAutoRefresh();
      }, 5000);
      return;
    }

    rank15();
    startAutoRefresh();
  },
  { immediate: true } // 立即执行一次
);
</script>

<style lang="scss" scoped>
.title {
  color: #096dd9;
}

.el-image {
  margin: 12px 0px;
  width: 100%;
  height: calc((100vh - 340px) / 2 * 0.56);
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}

.image-slot .el-icon {
  font-size: 26px;
}

.label {
  display: inline-flex;
  justify-content: flex-end;
  align-items: flex-start;
  flex: 0 0 auto;
  font-size: var(--el-form-label-font-size);
  color: rgba(0, 0, 0, 0.65);
  line-height: 30px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  width: 90px;
  text-align: right;
}

.value {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
  line-height: 30px;
  position: relative;
  font-size: var(--font-size);
  min-width: 0;
  color: rgba(0, 0, 0, 0.8);
}

.money {
  display: inline-block;
  color: #f5222d;
  font-size: 22px;
  position: relative;
  margin-top: -5px;
}

.money-unit {
  display: inline-block;
  position: relative;
  top: 0px;
}

.el-input__wrapper {
  border: none;
}

.table-card {
  height: calc((100vh - 258px) / 2);
}

.required {
  padding-right: 5px;
  color: #f5222d;
}

.alert {
  margin-top: 20px;
  margin-left: 5px;
  font-size: 20px;
  padding: 6px 10px;
  border-radius: 2px;
  user-select: none;
}

.warning {
  background-color: #fef0f0;
  color: #c45656;
}

.success {
  background-color: #f0f9eb;
  color: #67c23a;
}

:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*';
  color: #f5222d;
}
</style>
