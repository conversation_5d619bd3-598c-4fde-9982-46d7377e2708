<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-27 19:36:25
 * @FilePath: \new-wanda-park\parking-client-ui\src\views\log\OperateLog.vue
 * @Description: {}
-->
<template>
  <div class="container">
    <operate-log-search @form-search="searchOperateLog" @reset="resetParamsAndData" />
    <operate-log-table ref="table" />
  </div>
</template>

<script name="OperateLog" setup>
import OperateLogSearch from './operate_log/OperateLogSearch.vue';
import OperateLogTable from './operate_log/OperateLogTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchOperateLog = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>

<style lang="scss" scoped></style>
