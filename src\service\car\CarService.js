import * as car from '@/api/car/CarApi';

/**
 * @description 车辆查询
 * <AUTHOR>
 * @date 2022/11/1
 */
export default {
  /**
   * 查询在场（长期滞留）车辆
   */
  pageCarInRecordNotOut(data) {
    return new Promise((resolve, reject) => {
      try {
        car.pageCarInRecordNotOut(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询无牌车辆
   */
  pageCarInRecordNoPlate(data) {
    return new Promise((resolve, reject) => {
      try {
        car.pageCarInRecordNoPlate(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询一户多车关联情况
   */
  getCarBindInfo(data) {
    return new Promise((resolve, reject) => {
      try {
        car.getCarBindInfo(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
