<template>
  <div class="container">
    <shift-query-search @form-search="search" />
    <shift-query-table ref="table" />  
  </div>
</template>

<script name="ShiftQuery" setup>
import ShiftQuerySearch from './shift_query/ShiftQuerySearch.vue';
import ShiftQueryTable from './shift_query/ShiftQueryTable.vue';
import { ref } from 'vue';

const table = ref();

const search = (query) => {
  table.value.list(query);
};
</script>

<style lang="scss" scoped></style>
