<template>
  <div class="container">
    <fee-model-search @form-search="searchFeeModel" @form-reset="resetParamsAndData" />
    <fee-model-table ref="table" />  
  </div>
</template>

<script name="FeeModel" setup>
import FeeModelSearch from './fee_model/FeeModelSearch.vue';
import FeeModelTable from './fee_model/FeeModelTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchFeeModel = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>

<style lang="scss" scoped></style>
