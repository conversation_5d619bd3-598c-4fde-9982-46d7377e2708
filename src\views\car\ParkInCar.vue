<template>
  <div class="container">
    <park-in-car-search @form-search="searchParkInCar" @form-reset="resetParamsAndData" />
    <park-in-car-table ref="table" />  
  </div>
</template>

<script name="ParkInCar" setup>
import ParkInCarSearch from './park_in_car/ParkInCarSearch.vue';
import ParkInCarTable from './park_in_car/ParkInCarTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchParkInCar = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>

<style lang="scss" scoped></style>
