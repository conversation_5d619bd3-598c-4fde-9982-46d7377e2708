import $ from '@/utils/axios';
import { ElMessage } from 'element-plus';
import { getSetting, getToken } from '@/utils/common';
const ArrearsOfFeesApi = {
  /**
   * @description 获取车道识别相机基础信息
   * @returns 相机信息
   */
  getpagingEvents(data) {
    return $({
      url: `/sentry/park/monitor/pagingEvents`,
      method: 'post',
      data
    });
  },
  async exportEvents(data) {
    const setting = getSetting();
    const baseURL = setting.agent_port && setting.agent_ip ? `http://${setting.agent_ip}:${setting.agent_port}` : 'http://127.0.0.1:3000';
    fetch(baseURL + '/sentry/park/monitor/exportEvents', {
      method: 'POST',
      headers: {
        Authorization: getToken(),
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob',
      body: JSON.stringify(data)
    })
      .then(function (response) {
        return response.blob();
      })
      .then(function (blob) {
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        // 释放的 URL 对象以及移除 a 标签
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      });
  },
  selectByPage(data) {
    return $({
      url: '/sentry/park/selectByPage',
      method: 'post',
      data
    });
  },
  monitorcount(data) {
    return $({
      url: `/sentry/park/monitor/count?startTime=${data.startTime}&endTime=${data.endTime}&eventTypeId=${data.eventTypeId}`,
      method: 'get'
    });
  },
  // 预警事件列表
  pagingEventsRelation(data) {
    return $({
      url: `/sentry/park/monitor/pagingEventsRelation`,
      method: 'post',
      data
    });
  },
  // 预警事件统计
  countEventsRelation(data) {
    return $({
      url: `/sentry/park/monitor/countEventsRelation?startTime=${data.startTime}&endTime=${data.endTime}&eventTypeId=${data.eventTypeId}`,
      method: 'get'
    });
  },
  exportEventsRelation(data) {
    const setting = getSetting();
    const baseURL = setting.agent_port && setting.agent_ip ? `http://${setting.agent_ip}:${setting.agent_port}` : 'http://127.0.0.1:3000';
    fetch(baseURL + '/sentry/park/monitor/exportEventsRelation', {
      method: 'POST',
      headers: {
        Authorization: getToken(),
        'Content-Type': 'application/json;charset=UTF-8'
      },
      responseType: 'blob',
      body: JSON.stringify(data)
    })
      .then(function (response) {
        return response.blob();
      })
      .then(function (blob) {
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        // 释放的 URL 对象以及移除 a 标签
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      });
  }
};
export default ArrearsOfFeesApi;
