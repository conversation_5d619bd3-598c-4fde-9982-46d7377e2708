import { useMenu } from '@/stores/menu';
import { routePush } from './router';
import { useTab } from '@/stores/tab';

/**
 * 激活路由页签
 * @param {*} path
 */
export const activeRouteTab = (obj) => {
  const menu = useMenu();

  let page = undefined;
  if (obj.path) {
    page = menu.state.pageList.filter((item) => item.path === obj.path)[0];
  } else {
    page = menu.state.pageList.filter((item) => item.name === obj.name)[0];
  }

  if (page && routePush(obj)) {
    // 添加 frame-tab
    const tab = useTab();

    if (!tab.state.tabList.some((item) => item.path === page.path)) {
      tab.state.tabList.push(page);
      tab.state.activedTabIndex = tab.state.tabList.length - 1;
      menu.state.activedMenuIndex = page.id;
    } else {
      const index = tab.state.tabList.findIndex((item) => item.path === page.path);
      tab.state.activedTabIndex = index;
      menu.state.activedMenuIndex = page.id;
    }
  }
};
