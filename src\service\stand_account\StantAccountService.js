import * as account from '@/api/stand_account/StantAccountApi';

/**
 * @description 台账查询
 * <AUTHOR>
 * @date 2022/11/1
 */
export default {
  /**
   * 分页查询入场记录
   */
  pageParkInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        account.pageParkInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询出场记录
   */
  pageParkOutRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        account.pageParkOutRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询停车订单
   */
  pageParkOrder(data) {
    return new Promise((resolve, reject) => {
      try {
        account.pageParkOrder(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修正入场记录
   */
  updateCarInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        account.updateCarInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
