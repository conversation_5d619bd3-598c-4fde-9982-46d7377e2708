<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <!-- <el-table-column prop="action" label="操作" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="warning"> 请求重发 </el-button>
          </template>
        </el-table-column> -->
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="park_name" label="车场名" align="center" />
        <el-table-column prop="title" label="数据标题" align="center" />
        <el-table-column prop="object_type" label="对象类型" align="center" />
        <el-table-column prop="data_version" label="数据" align="center" />
        <el-table-column prop="content" label="版本内容" align="center" />
        <el-table-column prop="memo" label="备注" align="center" />
        <el-table-column prop="down_time" label="下载时间" min-width="160" align="center" />
        <el-table-column prop="down_state_desc" label="数据状态" align="center" />
        <el-table-column prop="created_at" label="创建时间" min-width="160" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="DownDataTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import syncService from '@/service/sync/SyncService';

const tableData = ref([]);

const total = ref(0);

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
onMounted(() => {
  getList(data.queryParams);
});

const getList = (params) => {
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  syncService.pageDataDownload(params).then((response) => {
    tableData.value = response.rows;
    total.value = parseInt(response.total);
  });
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
