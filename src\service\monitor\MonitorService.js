import * as monitor from '@/api/monitor/MonitorApi';

/**
 * @description 可监控通道
 * <AUTHOR>
 * @date 2022/12/23
 */
export default {
  /**
   * 查询可监控通道
   */
  async pageMonitorDevice() {
    return await new Promise((resolve, reject) => {
      try {
        monitor.pageMonitorDevice().then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   *
   * @returns 手动开闸
   */
  liftRoll(data) {
    return new Promise((resolve, reject) => {
      try {
        monitor.liftRoll(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   *
   * @returns 手动关闸
   */
  downRoll(data) {
    return new Promise((resolve, reject) => {
      try {
        monitor.downRoll(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   *
   * @returns 调节音量
   */
  setVolume(data) {
    return new Promise((resolve, reject) => {
      try {
        monitor.setVolume(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
