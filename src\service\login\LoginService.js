import * as login from '@/api/login/LoginApi';

/**
 * 登录/退出登录
 */
export default {
  /**
   * 收费员登录
   */
  collectorLogin(param) {
    return new Promise((resolve, reject) => {
      try {
        login.collectorLogin(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 管理员登录
   */
  adminLogin(param) {
    return new Promise((resolve, reject) => {
      try {
        login.adminLogin(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 退出登录
   */
  logout() {
    return new Promise((resolve, reject) => {
      try {
        login.logout().then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 检查班次信息
   */
  checkShift(param) {
    return new Promise((resolve, reject) => {
      try {
        login.checkShift(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 创建新的班次
   */
  createShiftRecord(param) {
    return new Promise((resolve, reject) => {
      try {
        login.createShiftRecord(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改密码
   */
  changePasswd(param) {
    return new Promise((resolve, reject) => {
      try {
        login.changePasswd(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
