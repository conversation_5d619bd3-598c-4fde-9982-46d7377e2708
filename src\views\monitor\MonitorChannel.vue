<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-27 19:36:25
 * @FilePath: \new-wanda-park\parking-client-ui\src\views\monitor\MonitorChannel.vue
 * @Description: {}
-->
<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="6" v-for="(item, index) in tableData" :key="index">
        <el-card class="card">
          <div class="header">{{ item.gateway_name }}</div>
          <!-- <div class="button-top">
            <el-button type="primary" link @click="getVideos(item.ip, index)">连接</el-button>
            <el-button type="primary" link @click="close()">关闭</el-button> 
          </div> -->
          <div class="video">
            <img :id="'img' + index" style="width: 100%; height: 100%" />
          </div>
          <div class="button-bottom">
            <el-button type="primary" @click="liftRoll(item.gateway_id, item.in_out)">手动开闸</el-button>
            <el-button class="button-down" type="primary" @click="downRoll(item.gateway_id)">手动放闸</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script name="MonitorChannel" setup>
import { ref, onMounted, onBeforeMount } from 'vue';
import { ElMessage } from 'element-plus';
import monitorService from '@/service/monitor/MonitorService';

const tableData = ref([]);
const loading = ref(false);

const lockReconnect = ref(false);

onBeforeMount(() => {
  getList();
});

onMounted(() => {
  monitorService.pageMonitorDevice().then((response) => {
    if (response.success === true) {
      for (let i = 0; i < tableData.value.length; i++) {
        console.log(tableData.value[i], i);
        getVideos(tableData.value[i].ip, i);
      }
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
});

const getList = () => {
  loading.value = true;
  monitorService.pageMonitorDevice().then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const getVideos = (ip, index) => {
  console.log(ip + index);
  let ws = null;
  let img_1 = document.getElementById('img' + index);
  console.log(img_1);

  if (ws) {
    ws.close();
  }

  ws = new WebSocket('ws://' + ip + ':9999');
  ws.onopen = function (event) {
    heartCheck.reset().start();
    console.log('open...');
  };

  var img = new Image();
  img.onload = function () {
    img_1.src = img.src;
  };

  var oldtime = new Date().valueOf();
  ws.onmessage = function (event) {
    var curtime = new Date().valueOf();
    //logtime = curtime - oldtime;
    //console.log("当前时间:"+curtime+'-------'+"差值:"+logtime)
    oldtime = curtime;
    heartCheck.reset().start();
    if (typeof event.data == 'string') {
      //字符串数据
      //var result = JSON.parse(event.data);
      //console.log(event.data)
    } else {
      //视频图片流数据
      var reader = new FileReader();
      reader.readAsDataURL(event.data);
      reader.onload = function (eve) {
        img.src = this.result;
      };
    }
  };
  ws.onerror = function (event) {
    console.log('error');
    reconnect();
  };
  ws.onclose = function (event) {
    console.log('close');
    reconnect();
  };
};
let heartCheck = {
  timeout: 3000, //3秒发一次心跳
  timeoutObj: null,
  serverTimeoutObj: null,
  reset: function () {
    clearTimeout(this.timeoutObj);
    clearTimeout(this.serverTimeoutObj);
    return this;
  },
  start: function () {
    var self = this;
    this.timeoutObj = setTimeout(function () {
      //这里发送一个心跳，后端收到后，返回一个心跳消息，
      //onmessage拿到返回的心跳就说明连接正常
      //ws.send("ping");
      console.log('ping9999');
      self.serverTimeoutObj = setTimeout(function () {
        //如果超过一定时间还没重置，说明后端主动断开了
        //如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
        hangClick();
      }, self.timeout);
    }, this.timeout);
  }
};
const reconnect = () => {
  if (lockReconnect.value) return;
  lockReconnect.value = true;
  setTimeout(function () {
    //没连接上会一直重连，设置延迟避免请求过多
    console.log('重连9999');
    hangClick();
    lockReconnect.value = false;
  }, 2000);
};
const liftRoll = (id, inOut) => {
  const parms = {
    gateway_id: id,
    direction: inOut,
    scene: 2
  };
  monitorService.liftRoll(parms).then((res) => {
    if (res.success) {
      ElMessage({
        type: 'success',
        message: res.message
      });
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
const downRoll = (val) => {
  const parms = {
    gateway_id: val
  };
  monitorService.downRoll(parms).then((res) => {
    if (res.success) {
      const data = res;
      if (data.success == true) {
        ElMessage({
          message: data.data,
          type: 'success'
        });
      } else {
        ElMessage({
          message: data.data,
          type: 'error'
        });
      }
    } else {
      ElMessage({
        message: res.data ? res.data : res.message,
        type: 'error'
      });
    }
  });
};
const close = () => {
  if (ws) {
    ws.close();
  }
};
</script>

<style lang="scss" scoped>
.header {
  padding-left: 5px;
  color: #096dd9;
}
.video {
  margin: 10px 0px;
  width: 100%;
  height: 215px;
}
.button-bottom {
  margin-bottom: 5px;
  .button-down {
    margin-left: 5px;
  }
}
.button-top {
  float: right;
  margin-top: -21px;
}
.card {
  height: 300px;
}
</style>
