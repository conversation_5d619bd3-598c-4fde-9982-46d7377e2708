<template>
  <div class="container">
    <long-stop-car-search @form-search="searchLongStopCar" @form-reset="resetParamsAndData" />
    <long-stop-car-table ref="table" />  
  </div>
</template>

<script name="LongStopCar" setup>
import LongStopCarSearch from './long_stop_car/LongStopCarSearch.vue';
import LongStopCarTable from './long_stop_car/LongStopCarTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchLongStopCar = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>

<style lang="scss" scoped></style>
