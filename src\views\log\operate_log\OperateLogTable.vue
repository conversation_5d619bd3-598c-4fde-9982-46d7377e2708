<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="primary"> 详情 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="park_name" label="车场名称" align="center" />
        <el-table-column prop="name" label="审计主题" align="center" />
        <el-table-column prop="description" label="描述" align="center" />
        <el-table-column prop="context" label="上下文" align="center" />
        <el-table-column prop="audit_time" label="审计时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="OperateLogTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import logService from '@/service/log/LogService';

const tableData = ref([]);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
onMounted(() => {
  getList(data.queryParams);
});

const getList = (params) => {
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  logService.pageAgentAuditLog(params).then((response) => {
    tableData.value = response.rows;
    total.value = parseInt(response.total);
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
