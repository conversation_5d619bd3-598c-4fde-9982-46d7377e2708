<template>
  <div class="container">
    <passout-record-search @form-search="searchPassoutRecord" @form-reset="resetParamsAndData" />
    <passout-record-table ref="table" />  
  </div>
</template>

<script setup name="PassoutRecord">
import PassoutRecordSearch from './pass_out_record/PassoutRecordSearch.vue';
import PassoutRecordTable from './pass_out_record/PassoutRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchPassoutRecord = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
