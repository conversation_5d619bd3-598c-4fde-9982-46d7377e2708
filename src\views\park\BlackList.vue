<template>
  <div class="container">
    <black-list-search @form-search="searchBlackList" @form-reset="resetParamsAndData" />
    <black-list-table ref="table" />  
  </div>
</template>

<script name="BlackList" setup>
import BlackListSearch from './black_list/BlackListSearch.vue';
import BlackListTable from './black_list/BlackListTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchBlackList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>

<style lang="scss" scoped></style>
