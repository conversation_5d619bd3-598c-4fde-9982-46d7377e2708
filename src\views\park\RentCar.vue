<template>
  <div class="container">
    <rent-car-search @form-search="searchRentCar" @form-reset="resetParamsAndData" />
    <rent-car-table ref="table" />  
  </div>
</template>

<script name="RentCar" setup>
import RentCarSearch from './rent_car/RentCarSearch.vue';
import RentCarTable from './rent_car/RentCarTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchRentCar = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
<style lang="scss" scoped></style>
