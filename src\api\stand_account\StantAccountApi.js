import $ from '@/utils/axios';

/**
 * 台账查询
 * @param {*} data
 * @returns
 */

//分页查询入场记录
export const pageParkInRecord = (data) => {
  return $({
    url: '/sentry/watch/pageParkInRecord',
    method: 'post',
    data
  });
};

//分页查询出场记录
export const pageParkOutRecord = (data) => {
  return $({
    url: '/sentry/watch/pageParkOutRecord',
    method: 'post',
    data
  });
};

//分页查询停车订单
export const pageParkOrder = (data) => {
  return $({
    url: '/sentry/watch/pageParkOrder',
    method: 'post',
    data
  });
};

// 修正入场记录
export const updateCarInRecord = (data) => {
  return $({
    url: '/sentry/watch/updateCarInRecord',
    method: 'post',
    data
  });
};
