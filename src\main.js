import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import App from './App.vue';
import router from './router';
import pinia from '@/stores';
import '@/styles/index.scss';
import emitter from '@/utils/mitt';
import permission from '@/utils/permissionKit';

// eslint-disable-next-line no-unused-vars
import mock from '@/mock';
const app = createApp(App);
app.use(ElementPlus);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(router);
app.use(pinia);

app.config.globalProperties.emitter = emitter;
app.config.globalProperties.$_has = permission;

app.mount('#app');
