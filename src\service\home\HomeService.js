import * as home from '@/api/home/<USER>';

/**
 * @description 主页
 * <AUTHOR>
 * @date 2022/10/25
 */
export default {
  /**
   * 执行交接班
   */
  executeShift(data) {
    return new Promise((resolve, reject) => {
      try {
        home.executeShift(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 获取交接班列表
   */
  getShift(data) {
    return new Promise((resolve, reject) => {
      try {
        home.getShift(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
