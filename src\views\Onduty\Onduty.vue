<template>
  <div class="conbox">
    <div class="content">
      <div class="con-status fw700">
        托管状态: <span v-if="useOndutyStore.isEscrow == 2 || useOndutyStore.isEscrow == 3" style="color: #95d475">云端托管中</span>
        <span v-else>--</span>
      </div>
      <el-form v-if="useOndutyStore.isEscrow == 0 || useOndutyStore.isEscrow == 1" :rules="formRlue" ref="fromRef" :model="sendDate">
        <el-form-item label="设置托管生效时间:">
          <div>
            <el-radio-group v-model="sendDate.hosting_type" @change="s_changeHostingType">
              <div class="flex-col">
                <el-radio value="1">
                  <div class="flex-center">
                    长期托管
                    <el-tooltip>
                      <template #content>
                        该模式为申请托管后立即生效，有效期为长期，如需结束远程值<br />守，则需岗亭端手动点击"解除托管"后才可结束
                      </template>
                      <el-icon><QuestionFilled style="cursor: pointer; color: #777" /></el-icon>
                    </el-tooltip>
                  </div>
                </el-radio>
                <el-radio value="2">
                  <div class="flex-center">
                    短期托管
                    <el-tooltip>
                      <template #content>
                        该模式为申请托管后，当到达开始时间时远程值守开始生效，到 <br />
                        达"结束时间"时，需由岗亭端手动点击”解除托管”后可结束值守。
                      </template>
                      <el-icon><QuestionFilled style="cursor: pointer; color: #777" /></el-icon>
                    </el-tooltip>
                    <div class="time-fw" v-if="sendDate.hosting_type == 2">
                      <el-form-item prop="apply_begin_time">
                        <el-date-picker
                          v-model="sendDate.apply_begin_time"
                          type="datetime"
                          placeholder="开始时间"
                          format="YYYY-MM-DD HH:mm"
                          date-format="MMM DD, YYYY"
                          value-format="YYYY-MM-DD HH:mm"
                          time-format="HH:mm"
                          :disabled-date="disabledDate"
                        />
                      </el-form-item>
                      <span style="color: #777">-</span>
                      <el-form-item prop="apply_end_time">
                        <el-date-picker
                          v-model="sendDate.apply_end_time"
                          type="datetime"
                          placeholder="结束时间"
                          value-format="YYYY-MM-DD HH:mm"
                          format="YYYY-MM-DD HH:mm"
                          date-format="MMM DD, YYYY"
                          time-format="HH:mm"
                          :disabled-date="disabledDate"
                        />
                      </el-form-item>
                    </div>
                  </div>
                </el-radio>
              </div>
            </el-radio-group>
          </div>
        </el-form-item>
        <div class="fw700" style="margin-top: 20px">车场应急通讯录</div>
        <el-form-item label="车场值班室电话:" prop="duty_phone">
          <el-input placeholder="请输入电话号码" style="width: 200px" v-model.trim="sendDate.duty_phone"></el-input>
        </el-form-item>
        <div class="con-line">
          <div>应急联系人1:</div>
          <el-form-item label="姓名:">
            <el-input placeholder="请输入姓名" v-model.trim="phoneDataOne.contact_name1"></el-input>
          </el-form-item>
          <!-- <el-form-item label="万信号:">
                  <el-input placeholder="请输入万信号" v-model="sendDate.contactWx1"></el-input>
                </el-form-item> -->
          <el-form-item label="手机号码:">
            <el-input placeholder="请输入手机号码" v-model.trim="phoneDataOne.contact_mobile1"></el-input>
          </el-form-item>
        </div>
        <div class="con-line">
          <div>应急联系人2:</div>
          <el-form-item label="姓名:">
            <el-input placeholder="请输入姓名" v-model.trim="phoneDataTwo.contact_name2"></el-input>
          </el-form-item>
          <!-- <el-form-item label="万信号:">
                  <el-input placeholder="请输入万信号" v-model="sendDate.contactWx2"></el-input>
                </el-form-item> -->
          <el-form-item label="手机号码:">
            <el-input placeholder="请输入手机号码" v-model.trim="phoneDataTwo.contact_mobile2"></el-input>
          </el-form-item>
        </div>
        <div class="con-line">
          <div>应急联系人3:</div>
          <el-form-item label="姓名:">
            <el-input placeholder="请输入姓名" v-model.trim="phoneDataThree.contact_name3"></el-input>
          </el-form-item>
          <!-- <el-form-item label="万信号:">
                  <el-input placeholder="请输入万信号" v-model="sendDate.contactWx3"></el-input>
                </el-form-item> -->
          <el-form-item label="手机号码:">
            <el-input placeholder="请输入手机号码" v-model.trim="phoneDataThree.contact_mobile3"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div v-else>
        <div v-if="cloudDataStustus">
          <div>
            托管生效时间：
            <span style="color: #1a73e8" v-if="cloudDataStustus.hosting_type == 1">长期托管</span>
            <span style="color: #1a73e8" v-else>{{ cloudDataStustus.apply_begin_time }} 至 {{ cloudDataStustus.apply_end_time }}</span>
          </div>
          <div>
            远程值守人员:
            <span v-for="(i, index) in useOndutyStore.cloudData" style="color: #1a73e8; margin-left: 10px">
              <span v-if="index != 0">、</span>{{ i.console_user_name }}({{ i.console_user_mobile }})</span
            >
          </div>
          <div class="fw700" style="margin-top: 20px">车场应急通讯录</div>
          <div style="margin-top: 5px">值班室电话:{{ cloudDataStustus.duty_phone }}</div>
          <div v-if="cloudDataStustus.contact_name1" style="margin-top: 5px">
            应急联系人1:{{ cloudDataStustus.contact_name1 }}({{ cloudDataStustus.contact_mobile1 }})
          </div>
          <div v-if="cloudDataStustus.contact_name2" style="margin-top: 5px">
            应急联系人2:{{ cloudDataStustus.contact_name2 }}({{ cloudDataStustus.contact_mobile2 }})
          </div>
          <div v-if="cloudDataStustus.contact_name3" style="margin-top: 5px">
            应急联系人3:{{ cloudDataStustus.contact_name3 }}({{ cloudDataStustus.contact_mobile3 }})
          </div>
        </div>
      </div>
      <div class="warning">
        <div class="fw700" style="margin-top: 20px">托管注意事项</div>
        <div class="warning-con">
          1、请确保和云端坐席人员已提前沟通好切换托管事宜，否则会导致托管失败；<br />
          2、托管时，请确保车场外网畅通;<br />
          3、托管时，请确保已接通的呼叫都处理完毕，否则会导致托管失败;<br />
          4、请在通道无车辆通行时进行托管操作，否则会导致记录丢失。<br />
          <br />
          解除托管注意事项<br />
          1、请确保和云端坐席人员已提前沟通好解除托管事宜，否则会出现出入口异常情况；<br />
          2、取消托管，只需局域网畅通即可，如遇车场外网断开，可及时切换到本地管理模式;<br />
          3、取消托管后，请确保话机连接正常，及时处理出入口异常。<br />
        </div>
      </div>
      <div class="buttonbox flex-center">
        <el-button v-if="useOndutyStore.isEscrow == 0" type="primary" @click="s_applyForEscrow">申请托管</el-button>
        <el-button v-if="useOndutyStore.isEscrow == 1" type="primary" disabled>申请托管中</el-button>
        <el-button v-if="useOndutyStore.isEscrow == 2" type="success" @click="s_exitForEscrow">解除托管</el-button>
        <el-button v-if="useOndutyStore.isEscrow == 3" type="success" disabled>解除托管中</el-button>
      </div>
    </div>
  </div>
  <!-- 申请托管弹出框 -->
  <el-dialog v-model="dialogVisible" title="申请托管" width="500" :before-close="handleClose">
    <div class="flex-col flex-center" style="color: #000">
      <div class="fw700">请确认以下信息是否托管？</div>
      <div>岗亭当班人员: {{ useUserStore.name }}</div>
      <div>广场值班室电话: {{ sendDate.duty_phone }}</div>
      <div v-if="phoneDataOne.contact_name1">应急联系人1: {{ phoneDataOne.contact_name1 }}({{ phoneDataOne.contact_mobile1 }})</div>
      <div v-if="phoneDataTwo.contact_name2">应急联系人2: {{ phoneDataTwo.contact_name2 }}({{ phoneDataTwo.contact_mobile2 }})</div>
      <div v-if="phoneDataThree.contact_name3">应急联系人3: {{ phoneDataThree.contact_name3 }}({{ phoneDataThree.contact_mobile3 }})</div>
      <div style="color: red; margin-top: 10px">提示：托管到云端值守之后，本地继续具备值守能力</div>
    </div>
    <template #footer>
      <div class="dialog-footer flex-center">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="s_okDialogVisible"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 申请取消托管弹出框 -->
  <el-dialog v-model="canceldDialogVisible" title="解除托管" width="500">
    <div class="flex-col flex-center" style="color: #000">
      <div class="fw700">请确认以下信息，是否继续解除托管？</div>
      <div>岗亭当班人员: {{ useUserStore.name }}</div>
      <div v-if="cloudData.length" class="userShow">
        <div v-for="(i, index) in cloudData" :key="index">远程值守人员{{ index + 1 }}: {{ i.console_user_name }}</div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer flex-center">
        <el-button @click="canceldDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="s_okcanceldDialogVisible"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
//#region import区
import { ref, onMounted } from 'vue'; //引入vue
import { ElMessage } from 'element-plus'; //引入element-plus
import { useUser } from '@/stores/user.js'; //引入用户信息仓库
import { sentry, getSentryStatus, getSentryStatusTimer } from '@/api/onduty/index.js'; //引入申请托管接口
import ondutyStore from '@/stores/onduty.js'; //引入值班信息仓库
//#endregion

//#region start 变量/响应式区
const requestTime = ref(); //请求托管定时器
const cloudData = ref([]); //云端值守信息
const useOndutyStore = ondutyStore(); //使用仓库
const cloudDataStustus = ref(); //云端值守信息
const useUserStore = useUser(); //使用用户信息仓库
const dialogVisible = ref(false); //控制申请托管弹出框1
// const endDialogVisible = ref(false)//控制托管结束弹出框
const canceldDialogVisible = ref(false); //控制申请取消托管弹出框
// const isEscrow = ref(2) //临时数据  0没申请托管  1 申请中托管  2申请托管成功  3解除托管中1
const timer = ref(); //定时器
const fromRef = ref(); //表单ref

// const value1 = ref([now, nextDay8AM]);
const sendDate = ref({
  park_id: '', //车场ID
  hosting_type: '1', //托管类型
  apply_begin_time: '', //托管开始时间
  apply_end_time: '', //托管结束时间
  duty_phone: '' //值班室电话
});
// 应急联系人1
const phoneDataOne = ref({
  contact_name1: '', //应急联系人1姓名
  contact_mobile1: '' //应急联系人1手机号
});
// 应急联系人2
const phoneDataTwo = ref({
  contact_name2: '', //应急联系人2姓名
  contact_mobile2: '' //应急联系人2手机号
});
// 应急联系人3
const phoneDataThree = ref({
  contact_name3: '', //应急联系人3姓名
  contact_mobile3: '' //应急联系人3手机号
});
// 自定义开始时间规则
const validatePassStrart = (_, value, callback) => {
  if (sendDate.value.hosting_type == 1) return;
  if (!value) {
    callback(new Error('请输入开始时间'));
  } else {
    callback();
  }
};
// 自定义结束时间规则
const validatePassEnd = (_, value, callback) => {
  if (sendDate.value.hosting_type == 1) return;
  if (!value) {
    callback(new Error('请输入结束时间'));
  } else {
    callback();
  }
};
// from规则
const formRlue = {
  apply_begin_time: [{ validator: validatePassStrart, trigger: 'blur' }],
  apply_end_time: [{ validator: validatePassEnd, trigger: 'blur' }],
  duty_phone: [{ required: true, message: '请输入值班室电话', trigger: 'blur' }]
};
//#endregion

//#region vue3生命周期钩子区
onMounted(() => {
  s_getCloudData();
  setNowData();
  isHaspeople();
});
//#endregion

//#region  方法区
//格式化日期
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份补零
  const day = String(date.getDate()).padStart(2, '0'); // 日期补零
  const hours = String(date.getHours()).padStart(2, '0'); // 小时补零
  const minutes = String(date.getMinutes()).padStart(2, '0'); // 分钟补零
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};
//日期的默认时间
//是否书如果联系人
const isHaspeople = () => {
  if (useOndutyStore.phoneDataOne) {
    phoneDataOne.value = useOndutyStore.phoneDataOne;
  }
  if (useOndutyStore.phoneDataTwo) {
    phoneDataTwo.value = useOndutyStore.phoneDataTwo;
  }
  if (useOndutyStore.phoneDataThree) {
    phoneDataThree.value = useOndutyStore.phoneDataThree;
  }
  if (useOndutyStore.duty_phone) {
    sendDate.value.duty_phone = useOndutyStore.duty_phone;
  }
};
const setNowData = () => {
  // 获取当前时间
  const now = new Date();
  // 计算T+1的上午8点
  const nextDay8AM = new Date(now);
  nextDay8AM.setDate(nextDay8AM.getDate() + 1); // 加1天
  nextDay8AM.setHours(8, 0, 0, 0); // 设置为上午8点
  sendDate.value.apply_begin_time = formatDate(now);
  sendDate.value.apply_end_time = formatDate(nextDay8AM);
};
//点击解除托管“确认”按钮
const s_okcanceldDialogVisible = async () => {
  const requestData = {
    ...sendDate.value,
    ...phoneDataOne.value,
    ...phoneDataTwo.value,
    ...phoneDataThree.value,
    park_id: useUserStore.park_id,
    apply_type: 2
  };
  console.log('发送给后端的数据', requestData);
  try {
    const rudata = await sentry(requestData);
    console.log('rudata', rudata);
    if (rudata.code == 200) {
      ElMessage({
        type: 'success',
        message: '解除申请中！'
      });
      canceldDialogVisible.value = false;
      useOndutyStore.isEscrow = 3;
      //解除申请中 定时发送查看审核状态
      s_setTime();
    }
  } catch (error) {
    console.error('申请托管失败', error);
  }
};
//用户定时器获取云端数据
const getSentryStatusTimerData = async () => {
  try {
    const rudata = await getSentryStatusTimer(useUserStore.park_id);
    console.log('云端数据结束', rudata);
    if (rudata.code == 200) {
      useOndutyStore.cloudData = rudata.data;
      cloudData.value = rudata.data;
      console.log('云端数据结束', rudata);
      if (rudata.data[0].hosting_status == 2) {
        useOndutyStore.endDialogVisible = true;
        useOndutyStore.isEscrow = 0;
        if (timer.value) {
          clearInterval(timer.value);
        }
      }
    }
  } catch (error) {
    console.error(error);
  }
};
//开启
const s_setTime = () => {
  timer.value = setInterval(() => {
    getSentryStatusTimerData();
  }, 1000 * 20);
};
// 提示解除托管弹出框 关闭触发
const closeDialog = () => {
  // isEscrow.value=0
};
//点击了"解除托管"按钮
const s_exitForEscrow = async () => {
  canceldDialogVisible.value = true;
};

//获取云端详情
const s_getCloudData = async () => {
  try {
    const rudata = await getSentryStatus(useUserStore.park_id);
    console.log('获取云端详情', rudata);
    if (rudata.code == 200) {
      cloudDataStustus.value = rudata.data;
      if (!rudata.data || !Object.keys(rudata.data).includes('hosting_status') || rudata.data.hosting_status == 2) {
        useOndutyStore.isEscrow = 0;
        return;
      }
      if (rudata.data.hosting_status == 0) {
        useOndutyStore.isEscrow = 1;
      }
      if (rudata.data.hosting_status == 1) {
        getSentryStatusTimerData();
        useOndutyStore.isEscrow = 2;
        //有定时器关闭
        if (requestTime.value) {
          clearInterval(requestTime.value);
          requestTime.value = null;
        }
      }
      if (rudata.data.hosting_status == 3) {
        useOndutyStore.isEscrow = 3;
        getSentryStatusTimerData();
      }
    }
  } catch (error) {
    console.error('获取云端详情失败', error);
  }
};
//点击托管审弹窗的“确定”按钮
const s_okDialogVisible = async () => {
  //没有问题走这
  const requestData = {
    ...sendDate.value,
    ...phoneDataOne.value,
    ...phoneDataTwo.value,
    ...phoneDataThree.value,
    park_id: useUserStore.park_id,
    apply_type: 1
  };
  try {
    const rudata = await sentry(requestData);
    console.log('rudata', rudata);
    if (rudata.code == 200) {
      ElMessage({
        type: 'success',
        message: '申请托管成功！'
      });
      dialogVisible.value = false;
      useOndutyStore.isEscrow = 1;
      useOndutyStore.phoneDataOne = phoneDataOne.value;
      useOndutyStore.phoneDataTwo = phoneDataTwo.value;
      useOndutyStore.phoneDataThree = phoneDataThree.value;
      useOndutyStore.duty_phone = sendDate.value.duty_phone;
      requestTime.value = setInterval(() => {
        s_getCloudData();
      }, 1000 * 20);
    }
    if (rudata.code == 'WATCH-00010') {
      ElMessage({
        type: 'warning',
        message: '该广场已是云端值守广场，不可申请托管'
      });
    }
  } catch (error) {
    console.error('申请托管失败', error);
  }
  // dialogVisible.value=false//关闭当前弹出框
  // isEscrow.value=1
  // const rudata = await sentry(requestData)
  //       console.log("申请托管返回的数据",rudata)
};
const s_applyForEscrow = async () => {
  // 判断验证
  await fromRef.value.validate();
  // 获取每个联系人不完整的信息
  const oneIsHasValue = Object.entries(phoneDataOne.value).filter((item) => Object.values(item).includes(''));
  const twoIsHasValue = Object.entries(phoneDataTwo.value).filter((item) => Object.values(item).includes(''));
  const threeIsHasValue = Object.entries(phoneDataThree.value).filter((item) => Object.values(item).includes(''));
  // 有一个联系人为完整的
  if (!oneIsHasValue.length || !twoIsHasValue.length || !threeIsHasValue.length) {
    //判断是否填写了信息 不完整进行提示
    if (oneIsHasValue.length == 1) {
      return ElMessage({
        type: 'warning',
        message: '紧急联系人1信息不完整！'
      });
    }
    if (twoIsHasValue.length == 1) {
      return ElMessage({
        type: 'warning',
        message: '紧急联系人2信息不完整！'
      });
    }
    if (threeIsHasValue.length == 1) {
      return ElMessage({
        type: 'warning',
        message: '紧急联系人3信息不完整！'
      });
    }
    if (
      phoneDataOne.value.contact_mobile1 &&
      /^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/.test(phoneDataOne.value.contact_mobile1) === false
    ) {
      return ElMessage({
        type: 'warning',
        message: '紧急联系人1手机号格式不对！'
      });
    }
    if (
      phoneDataTwo.value.contact_mobile2 &&
      /^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/.test(phoneDataTwo.value.contact_mobile2) === false
    ) {
      return ElMessage({
        type: 'warning',
        message: '紧急联系人2手机号格式不对！'
      });
    }
    if (
      phoneDataThree.value.contact_mobile3 &&
      /^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/.test(phoneDataThree.value.contact_mobile3) === false
    ) {
      return ElMessage({
        type: 'warning',
        message: '紧急联系人3手机号格式不对！'
      });
    }
    dialogVisible.value = true;
    return;
  }
  ElMessage({
    type: 'warning',
    message: '至少填写一名完整的应急人联系信息！'
  });
};
//禁用时间
const disabledDate = (time) => {
  // 获取当前时间
  const now = new Date();
  // 今天00:00:00
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  // 禁用今天之前的所有日期
  return time < today;
};
//托管类型切换的时候
const s_changeHostingType = () => {
  sendDate.value.begin_time = '';
  sendDate.value.end_time = '';
};
//#endregion
</script>

<style scoped lang="scss">
.flex-col {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.fw700 {
  font-weight: 700 !important;
  margin-bottom: 10px;
}
.conbox {
  height: 100%;
  width: 100%;
  .content {
    height: 100%;
    width: 100%;
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
    overflow: auto;
    .el-form-item {
      margin-bottom: 0 !important;
    }
    :deep(.el-form-item__label) {
      color: #000;
    }
    .con-line {
      display: flex;
      align-items: center;
      margin-top: 20px;
      gap: 15px;
      margin-left: 20px;
    }
    .warning {
      .warning-con {
        // font-size: 13px;
        margin-left: 20px;
      }
    }
  }
}
.time-fw {
  display: flex;
}
.el-overlay {
  z-index: 9999 !important; /* 确保足够高 */
}
.userShow {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
</style>
