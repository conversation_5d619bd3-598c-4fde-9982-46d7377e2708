<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="plate_no" label="车牌规则" align="center" />
        <el-table-column prop="state_desc" label="生效状态" align="center" />
        <el-table-column prop="address" label="有效期" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.effective_start_time + '-' + scope.row.effective_end_time }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script name="InterceptRuleTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import parkService from '@/service/park/ParkService';

const loading = ref(false);
const tableData = ref([]);
const data = reactive({
  queryParams: {}
});
onMounted(() => {
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  parkService.pageInterceptRules(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
