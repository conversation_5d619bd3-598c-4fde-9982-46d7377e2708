<template>
  <el-card class="table" shadow="never">
    <div ref="tableRef">
      <el-table :data="table.data" v-loading="table.loading" border>
        <el-table-column prop="action" label="操作" width="120">
          <template v-slot="scope">
            <el-button type="primary" link @click="handleAllowPassIn(scope.row)">允许入场</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" />
        <el-table-column prop="park_name" label="车场名称" />
        <el-table-column prop="park_region_name" label="子场名称" />
        <el-table-column prop="park_gateway_name" label="通道名称" />
        <el-table-column prop="plate_no" label="车牌号" />
        <el-table-column prop="car_type_desc" label="车型" />
        <el-table-column prop="intercept_type_desc" label="拦截类型" />
        <el-table-column prop="intercept_reason" label="拦截原因" />
        <el-table-column prop="in_state_desc" label="是否入场" />
        <el-table-column prop="in_time" label="入场时间" width="160" />
        <el-table-column prop="out_state_desc" label="是否出场" />
        <el-table-column prop="out_time" label="出场时间" width="160" />
        <el-table-column prop="memo" label="备注" />
      </el-table>
      <el-pagination
        background
        :current-page="table.query.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="table.query.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="table.total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="InterceptRecordTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import interceptRecordService from '@/service/watch/InterceptRecordService';
import watchService from '@/service/watch/WatchService';

const tableRef = ref();
const table = reactive({
  query: {
    page: 1,
    limit: 30
  },
  data: [],
  total: 0,
  loading: false
});

onActivated(() => {
  list(table.query);
});

const list = (query) => {
  table.loading = true;

  table.query = Object.assign(table.query, query);
  interceptRecordService.pageCarInterceptRecord(table.query).then((res) => {
    if (res.success) {
      table.data = res.data.rows;
      table.total = parseInt(res.data.total);
    } else {
      ElMessage({
        message: res.detail_message != '' ? res.detail_message : res.message,
        type: 'error'
      });
    }
    table.loading = false;
  });
};

const handleSizeChange = (val) => {
  table.query.limit = val;
  list(table.query);
};

const handleCurrentChange = (val) => {
  table.query.page = val;
  list(table.query);
};

// 允许入场
const handleAllowPassIn = (row) => {
  ElMessageBox.confirm('是否允许入场？', '入场', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  })
    .then(() => {
      const param = {
        car_biz_no: row.intercept_biz_no,
        lift_roll: false
      };
      watchService.manualPassIn(param).then((res) => {
        if (res.success) {
          ElMessage({
            message: '允许入场成功',
            type: 'success'
          });

          clearData();
        } else {
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
            type: 'error'
          });
        }
      });
    })
    .catch(() => {});
};

defineExpose({
  list
});
</script>

<style lang="scss" scoped></style>
