<template>
  <div class="frame-menu">
    <el-menu :default-active="activedMenuIndex" :collapse="true" background-color="#00474f" text-color="#fff">
      <template v-for="(item, index) in menuList" :key="item.id">
        <template v-if="!item.children">
          <el-menu-item :key="index" @click="activeRouteTab({ path: item.path })">
            <template #title>
              <el-icon size="18">
                <component :is="item.icon"></component>
              </el-icon>
              <div>{{ item.title }}</div>
            </template>
          </el-menu-item>
        </template>
        <template v-else>
          <FrameMenuItem :menu="item" :key="item.id"></FrameMenuItem>
        </template>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import FrameMenuItem from './FrameMenuItem.vue';
import { useMenu } from '@/stores/menu';
import { computed } from 'vue';

const menu = useMenu();

const menuList = computed({
  get() {
    return menu.state.menuList;
  },
  set(val) {
    menu.state.menuList = val;
  }
});

const activedMenuIndex = computed({
  get() {
    return menu.state.activedMenuIndex;
  },
  set(val) {
    menu.state.activedMenuIndex = val;
  }
});
</script>

<style lang="scss" scoped>
.frame-menu {
  height: calc(100vh - 84px);
  width: 100%;
  overflow: hidden;
  background-color: #00474f;
  box-shadow: 6px 2px 8px #f0f1f2;
}

:deep(.el-menu--collapse) {
  width: 87px;
}

:deep(.el-sub-menu__title) {
  line-height: 26px !important;
  border: 5px solid #00474f;
  border-radius: 8px;
  margin: 0 auto;
  height: 65px;
}

:deep(.el-sub-menu__title span) {
  visibility: visible !important;
  color: rgba(255, 255, 255, 0.7) !important;
  display: block !important;
}

:deep(.el-sub-menu__title:hover) {
  background: #08979c !important;
}

:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  border: 5px solid #00474f;
  background: #08979c !important;
}

:deep(.el-menu--collapse > .el-sub-menu > .el-sub-menu__title [class^='el-icon']) {
  margin-left: 0px;
  width: 20px;
  text-align: center;
}

.el-menu-item {
  height: 42px !important;
  line-height: 42px !important;
}

.el-menu-item.is-active {
  color: #08979c;
}
</style>
