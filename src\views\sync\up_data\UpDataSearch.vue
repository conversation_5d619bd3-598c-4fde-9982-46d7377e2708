<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.upload_state" placeholder="上行数据类型" clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="UpDataSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { reactive, onMounted, ref } from 'vue';
import { getDefaultDateRange } from '@/utils/common';

const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    upload_state: 1,
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  },
  dateRange: []
});
const states = ref([]);

onMounted(() => {
  //   // 数据初始化
  initSelects();
  form.dateRange = getDefaultDateRange(7);
});
const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumUploadState' }];
  commonService.findEnums('sentry', param).then((response) => {
    states.value = response.data.states;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    upload_state: undefined,
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  };
  emits('form-reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
