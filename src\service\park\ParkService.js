import * as park from '@/api/park/ParkApi';

/**
 * @description 车场信息
 * <AUTHOR>
 * @date 2022/10/25
 */
export default {
  /**
   * 查询停车场基本信息
   */
  getParkInfo() {
    return new Promise((resolve, reject) => {
      try {
        park.getParkInfo().then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询计费模型
   */
  pageFeeModel(data) {
    return new Promise((resolve, reject) => {
      try {
        park.pageFeeModel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询计费模型详情
   */
  getFeeModelById(data) {
    return new Promise((resolve, reject) => {
      try {
        park.getFeeModelById(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询停车场黑名单
   */
  pageBlackList(data) {
    return new Promise((resolve, reject) => {
      try {
        park.pageBlackList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询停车场白名单
   */
  pageWhiteList(data) {
    return new Promise((resolve, reject) => {
      try {
        park.pageWhiteList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询长租车信息
   */
  pageRentCar(data) {
    return new Promise((resolve, reject) => {
      try {
        park.pageRentCar(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 拦截规则
   */
  pageInterceptRules(data) {
    return new Promise((resolve, reject) => {
      try {
        park.pageInterceptRules(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 岗亭列表
   */
  listSentryByParkCode(data) {
    return new Promise((resolve, reject) => {
      try {
        park
          .listSentryByParkCode(data)
          .then(function (res) {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 区域内通道列表
   */
  listAllGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        park.listAllGateway(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
