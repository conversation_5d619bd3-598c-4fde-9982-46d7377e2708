import { useUser } from '@/stores/user';
import { useSetting } from '@/stores/setting';

// 获得 token
export function getToken() {
  const user = useUser();
  return user.token;
}

// 移除 token
export function removeToken() {
  const user = useUser();
  user.removeToken();
}

// 获得设置
export function getSetting() {
  const setting = useSetting();
  return setting;
}

// 获取角色
export function getRole() {
  const user = useUser;
  return user.role;
}

// 更新网络状态
export function updateNetState(net_state) {
  const user = useUser();
  user.net_state = net_state;
}

// 计算日期
export function getDefaultDateRange(day) {
  // 结束时间
  const end = new Date();
  const endYear = end.getFullYear();
  const endMonth = addZero(end.getMonth() + 1);
  const endDate = addZero(end.getDate());

  // 开始时间
  const start = new Date(end.getTime() - day * 24 * 60 * 60 * 1000);
  const startYear = start.getFullYear();
  const startMonth = addZero(start.getMonth() + 1);
  const startDate = addZero(start.getDate());

  const startTime = startYear + '-' + startMonth + '-' + startDate;
  const endTime = endYear + '-' + endMonth + '-' + endDate;
  return [startTime, endTime];
}

export function getDefaultDateTimeRange(day) {
  // 结束时间
  const end = new Date();
  const endYear = end.getFullYear();
  const endMonth = addZero(end.getMonth() + 1);
  const endDate = addZero(end.getDate());
  const endHour = end.getHours();
  const endMinutes = end.getMinutes();
  const endSeconds = end.getSeconds();

  // 开始时间
  const start = new Date(end.getTime() - day * 24 * 60 * 60 * 1000);
  const startYear = start.getFullYear();
  const startMonth = addZero(start.getMonth() + 1);
  const startDate = addZero(start.getDate());

  const startTime = startYear + '-' + startMonth + '-' + startDate + ' 00:00:00';
  const endTime = endYear + '-' + endMonth + '-' + endDate + ' ' + endHour + ':' + endMinutes + ':' + endSeconds;

  return [startTime, endTime];
}

//获取开始日期
export function getDefaultStartDateTime(day) {
  const end = new Date();
  // 开始时间
  const start = new Date(end.getTime() - day * 24 * 60 * 60 * 1000);
  const startYear = start.getFullYear();
  const startMonth = addZero(start.getMonth() + 1);
  const startDate = addZero(start.getDate());

  const startTime = startYear + '-' + startMonth + '-' + startDate + ' 00:00:00';
  return startTime;
}

//获取结束日期
export function getDefaultEndDateTime(day) {
  // 结束时间
  const end = new Date();
  const endYear = end.getFullYear();
  const endMonth = addZero(end.getMonth() + 1);
  const endDate = addZero(end.getDate());
  const endHour = end.getHours();
  const endMinutes = end.getMinutes();
  const endSeconds = end.getSeconds();
  const endTime = endYear + '-' + endMonth + '-' + endDate + ' ' + endHour + ':' + endMinutes + ':' + endSeconds;
  return endTime;
}

// 获取默认时间
export function getOffsetDateTime(datetime, offset) {
  if (offset != 0) {
    datetime = new Date(datetime.getTime() - offset * 60 * 1000);
  }

  const year = datetime.getFullYear();
  const month = addZero(datetime.getMonth() + 1);
  const date = addZero(datetime.getDate());
  const hour = datetime.getHours();
  const minutes = datetime.getMinutes();
  const seconds = datetime.getSeconds();

  return year + '-' + month + '-' + date + ' ' + hour + ':' + minutes + ':' + seconds;
}

// 补零
function addZero(num) {
  if (num.toString().length == 1) {
    num = '0' + num;
  }
  return num;
}
