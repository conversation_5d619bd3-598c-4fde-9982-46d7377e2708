const { app, electron, BrowserWindow, ipcMain, shell } = require('electron');
const { autoUpdater } = require('electron-updater');
const logger = require('electron-log');
const path = require('path');
const fs = require('fs');
const os = require('os');

logger.transports.console.level = 'error';
logger.transports.file.level = 'silly';
logger.transports.file.maxSize = 100243 * 100; //文件最大不超过100M
logger.transports.file.resolvePath = () => path.join('C:/ProgramData/sentry-parking-client', 'logs/out.log');

let quitFlag = false;

function createWindow() {
  const win = new BrowserWindow({
    minWidth: 1366,
    minHeight: 768,
    title: '惠达万安岗亭值守系统',
    autoHideMenuBar: false,
    icon: path.join(__dirname, './static/favicon.ico'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      partition: 'nopersist',
      nodeIntegration: true,
      contextIsolation: false
    },
    frame: true
  });

  require('./menu');

  win.maximize();

  if (app.isPackaged) {
    win.loadFile(path.join(__dirname, '../dist/index.html'));
  } else {
    let url = 'http://localhost:3000';
    win.loadURL(url);
  }

  // win.webContents.openDevTools();

  win.on('close', (e) => {
    if (!quitFlag) {
      win.webContents.send('close-window');
      e.preventDefault();
    }
  });
}

ipcMain.on('close-window-reply', (event, data) => {
  quitFlag = data;
  app.quit();
});

app.whenReady().then(() => {
  let win = createWindow();
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      win = createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 读写配置文件
const settingJsonDir = 'C:/ProgramData/sentry-parking-client/settings';
const settingJsonFile = 'setting.json';
const settingJson = settingJsonDir + '/' + settingJsonFile;
ipcMain.on('read-setting', (event, data) => {
  let returnValue = {};

  if (!fs.existsSync(settingJson)) {
    if (!fs.existsSync(settingJsonDir)) {
      fs.mkdirSync(settingJsonDir, { recursive: true }, (err) => {
        if (err) {
          returnValue = {
            success: false,
            msg: err
          };
          event.sender.send('read-setting-reply', returnValue);
          return;
        }
      });
    }

    const settingData = {
      terminal_code: null,
      terminal_auth_code: null,
      sentry_id: null,
      park_code: null,
      agent_ip: null,
      agent_port: null
    };
    fs.writeFileSync(settingJson, JSON.stringify(settingData, null, '\t'), (err) => {});
  }

  // 读取setting.json
  let settingData = JSON.parse(fs.readFileSync(settingJson));
  returnValue = {
    success: true,
    data: settingData
  };
  event.sender.send('read-setting-reply', returnValue);
});
ipcMain.on('write-setting', (event, data) => {
  // 写入setting.json
  fs.writeFileSync(settingJson, data, (err) => {});
});

ipcMain.on('read-system', (event, data) => {
  // 网络
  let ip = undefined;
  const interfaces = os.networkInterfaces();
  for (var devName in interfaces) {
    var iface = interfaces[devName];

    for (var i = 0; i < iface.length; i++) {
      var alias = iface[i];
      if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
        ip = alias.address;
      }
    }
  }

  const system = {
    ip: ip
  };
  event.sender.send('read-system-reply', system);
});

// 自动更新
ipcMain.on('check-update', (event, arg) => {
  checkUpdate(event);
});

function checkUpdate(event) {
  let message = {
    error: '检查更新出错',
    checking: '正在检查更新...',
    updateAva: '正在更新',
    updateNotAva: '已经是最新版本',
    downloadProgress: '正在下载...'
  };

  //如下应用程序的路径请自行替换成自己应用程序的路径
  let updateFeedUrl = 'https://park.huidawanan.com/sentry-app';
  autoUpdater.setFeedURL(updateFeedUrl);

  // 设置是否自动下载，默认是true,当点击检测到新版本时，会自动下载安装包，所以设置为false
  autoUpdater.autoDownload = false;

  autoUpdater.on('error', function (error) {
    logger.info(message.error, error);
    sendUpdateMessage(event, {
      cmd: 'error',
      message: message.error,
      error
    });
  });
  autoUpdater.on('checking-for-update', function () {
    logger.info(message.checking);
    sendUpdateMessage(event, {
      cmd: 'checking-for-update',
      message: message.checking
    });
  });
  autoUpdater.on('update-available', function (info) {
    logger.info(message.updateAva);
    sendUpdateMessage(event, {
      cmd: 'update-available',
      message: message.updateAva,
      info
    });
  });
  autoUpdater.on('update-not-available', function (info) {
    logger.info(message.updateNotAva);
    sendUpdateMessage(event, {
      cmd: 'update-not-available',
      message: message.updateNotAva,
      info
    });
  });
  autoUpdater.on('download-progress', function (progressObj) {
    logger.info('触发下载...');
    sendUpdateMessage(event, {
      cmd: 'download-progress',
      message: message.downloadProgress,
      progressObj
    });
  });

  autoUpdater.on('update-downloaded', function (event, releaseNotes, releaseName, releaseDate, updateUrl, quitAndUpdate) {
    logger.info('开始更新');
    autoUpdater.quitAndInstall(true, true);
  });

  autoUpdater.checkForUpdates();
}

ipcMain.on('downloadUpdate', () => {
  logger.info('执行下载');
  autoUpdater.downloadUpdate();
});

// 通过main进程发送事件给renderer进程，提示更新信息
function sendUpdateMessage(event, data) {
  event.sender.send('check-update-reply', data);
}

// 打开默认浏览器
ipcMain.on('openDefaultBrowser', (event, url) => {
  let exec = require('child_process').exec;

  switch (process.platform) {
    case 'darwin':
      exec('open ' + url);
      break;
    case 'win32':
      exec('start ' + url);
      break;
    default:
      exec('xdg-open', [url]);
  }
});

// 日志记录
ipcMain.on('info', (event, data) => {
  logger.info(data);
});

ipcMain.on('error', (event, data) => {
  logger.error(data);
});

ipcMain.on('open-export-file', (event, data) => {
  if (fs.existsSync(data)) {
    shell.showItemInFolder(data);
  } else {
    event.sender.send('open-export-file-fail', '未找到文件');
  }
});

//rtsp视频流相关
/* const ffmpegPath = require('ffmpeg-static');
const Stream = require('node-rtsp-stream');
const rtspOpenders = {};
let addPort = 9000; */
/**
 * 开启rtsp
 * @param rtsp {String} rtsp流地址
 */
// ipcMain.on('openRtsp', (event, rtsp) => {
//   /** 判断是否已开启,若已开启,直接返回ws地址, 未开启则先开启再返回 */
//   if (rtspOpenders[rtsp]) {
//     event.returnValue = {
//       code: 200,
//       msg: '开启成功',
//       ws: rtspOpenders[rtsp].ws
//     };
//   } else {
//     addPort++;
//     const stream = new Stream({
//       name: `socket-${addPort}`,
//       streamUrl: rtsp,
//       wsPort: addPort,
//       ffmpegPath: app.isPackaged ? ffmpegPath.replace('app.asar', 'app.asar.unpacked') : ffmpegPath,
//       ffmpegOptions: {
//         '-stats': '',
//         '-r': 30
//       }
//     }).on('exitWithError', () => {
//       stream.stop();
//       delete rtspOpenders[rtsp];
//       event.returnValue = {
//         code: 400,
//         msg: '开启失败'
//       };
//     });
//     rtspOpenders[rtsp] = {
//       ws: `ws://localhost:${addPort}`,
//       stream: stream
//     };
//     event.returnValue = {
//       code: 200,
//       msg: '开启成功',
//       ws: rtspOpenders[rtsp].ws
//     };
//   }
// });

/**
 * 关闭rtsp
 */
// ipcMain.on('closeRtsp', (event, rtsp) => {
//   if (rtspOpenders[rtsp]) {
//     // 停止解析
//     rtspOpenders[rtsp].stream.stop();
//     // 删除该项
//     delete rtspOpenders[rtsp];
//     // 返回结果
//     event.returnValue = {
//       code: 200,
//       msg: '关闭成功'
//     };
//   } else {
//     event.returnValue = {
//       code: 400,
//       msg: '未找到该rtsp'
//     };
//   }
// });
