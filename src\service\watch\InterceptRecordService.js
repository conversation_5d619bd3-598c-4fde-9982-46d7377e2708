import * as interceptRecord from '@/api/watch/InterceptRecord';

/**
 * @description 拦截记录
 * <AUTHOR>
 * @date 2022/10/25
 */
export default {
  /**
   * 分页查询拦截记录
   */
  pageCarInterceptRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        interceptRecord.pageCarInterceptRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
