<template>
  <div class="page">
    <div class="header">
      <span style="color: rgba(0, 0, 0, 0.7); font-size: 22px">惠达万安岗亭值守系统</span>
      <span style="margin-left: 10px; font-size: 16px; color: #096dd9">Build: {{ version }}</span>
    </div>
    <div class="right">
      <el-space :size="20">
        <el-link type="primary" :underline="false" @click="handleCloud">云平台</el-link>
        <el-link v-if="loginForm.login_type === 1" type="primary" :underline="false" @click="handleManager">管理员登录</el-link>
        <el-link v-if="loginForm.login_type === 2" type="primary" :underline="false" @click="handleCollector">收费员登录</el-link>
        <el-icon :size="20" @click="handleClickSetting" style="color: rgba(0, 0, 0, 0.6)"><Tools /></el-icon>
      </el-space>
    </div>
    <!-- 登录 -->
    <div class="login">
      <div class="login-box">
        <el-row :gutter="10" style="width: 1000px">
          <el-col :span="14">
            <div class="image">
              <img src="@/assets/logo.png" style="width: 600px; height: 475px; object-fit: none" />
            </div>
          </el-col>
          <el-col :span="1">
            <el-divider direction="vertical" style="height: 100%"></el-divider>
          </el-col>
          <el-col :span="9">
            <div class="form">
              <el-form @keyup.enter="login(loginFormRef)" ref="loginFormRef" label-position="top" :rules="rules" :model="loginForm">
                <div class="content">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item label="Agent服务器IP">{{ setting.agent_ip ? setting.agent_ip : '' }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="Agent服务器端口">{{ setting.agent_port ? setting.agent_port : '' }}</el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <!-- 收费员登录 -->
                <div class="content" v-if="loginForm.login_type === 1">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item prop="username" label="当前值班人">
                        <el-input type="text" clearable v-model="loginForm.username" placeholder="请输入当前值班人">
                          <template #prefix>
                            <el-icon size="16">
                              <User />
                            </el-icon>
                          </template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="当前值班开始时间">
                        {{ on_time ? on_time : '' }}
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item prop="password" label="值班人密码">
                        <el-input v-model="loginForm.password" type="password" placeholder="请输入值班人密码">
                          <template #prefix>
                            <el-icon size="16">
                              <Lock />
                            </el-icon>
                          </template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item>
                    <el-button round type="primary" size="large" @click="login(loginFormRef)" style="width: 180px"> 立即进入 </el-button>
                  </el-form-item>
                </div>

                <!-- 管理员登录 -->
                <div class="content" v-if="loginForm.login_type === 2">
                  <el-form-item prop="username" label="管理员账号">
                    <el-input type="text" clearable v-model="loginForm.username" placeholder="请输入管理员账号" style="width: 80%">
                      <template #prefix>
                        <el-icon size="16">
                          <User />
                        </el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="password" label="管理员密码">
                    <el-input v-model="loginForm.password" type="password" placeholder="请输管理员密码" style="width: 80%">
                      <template #prefix>
                        <el-icon size="16">
                          <Lock />
                        </el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button round type="primary" size="large" @click="adminLogin(loginFormRef)" style="width: 180px"> 登 录 </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 系统设置 -->
    <el-dialog title="系统设置" v-model="settingDialogVisible" :close-on-click-modal="false" width="560px">
      <el-form ref="settingFormRef" label-position="top" :rules="rules" :model="settingForm">
        <el-form-item prop="park_code" label="车场代码">
          <el-input v-model="settingForm.park_code" placeholder="车场代码" />
        </el-form-item>
        <el-row :gutter="8">
          <el-col :span="10">
            <el-form-item prop="agent_ip" label="Agent服务器IP">
              <el-input v-model="settingForm.agent_ip" placeholder="Agent服务器IP" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item prop="port" label="Agent服务器端口">
              <el-input v-model="settingForm.agent_port" placeholder="Agent服务器端口" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" style="margin-top: 30px; width: 100%" @click="verify" :loading="verifyLoading">验 证</el-button>
          </el-col>
        </el-row>
        <el-form-item prop="terminal_code" label="终端编码">
          <el-input v-model="settingForm.terminal_code" placeholder="终端编码" />
        </el-form-item>
        <el-form-item prop="terminal_auth_code" label="终端授权码">
          <el-input type="textarea" :rows="4" v-model="settingForm.terminal_auth_code" placeholder="终端授权码" />
        </el-form-item>
        <el-form-item prop="sentry_id" label="岗亭">
          <el-select v-model="settingForm.sentry_id" style="width: 90%" placeholder="岗亭">
            <el-option v-for="item in sentries" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          &nbsp; &nbsp;<el-icon color="#409EFC" :size="28" @click="getSentry()" class="refresh"><Refresh /></el-icon>
        </el-form-item>
        <el-form-item prop="pressure_test" label="压测模式">
          <el-switch v-model="settingForm.pressure_test" inline-prompt active-text="开启" inactive-text="关闭" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="settingDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveSetting(settingFormRef)">保 存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import loginService from '@/service/login/LoginService';
import parkService from '@/service/park/ParkService';
import router from '@/router';
import { useUser } from '@/stores/user';
import { useSetting } from '@/stores/setting';
import project from '@/project.json';
import { useIpcRenderer } from '@vueuse/electron';

// 系统版本信息
const version = project.version;

// 用户缓存
const user = useUser();

// IPC通信
const ipcRenderer = useIpcRenderer();

const loginFormRef = ref();
const loginForm = reactive({
  username: undefined,
  password: undefined,
  login_type: 1
});

// 系统设置
const setting = useSetting();
const settingFormRef = ref();
const settingForm = reactive({
  terminal_code: undefined,
  terminal_auth_code: undefined,
  park_code: undefined,
  sentry_id: undefined,
  agent_ip: undefined,
  agent_port: undefined,
  pressure_test: false
});
const settingDialogVisible = ref(false);

const sentries = ref([]);

const rules = reactive({
  username: [
    {
      required: true,
      message: '请输入用户名',
      trigger: 'blur'
    }
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: 'blur'
    }
  ],
  terminal_code: [
    {
      required: true,
      message: '请输入终端编号',
      trigger: 'blur'
    }
  ],
  terminal_auth_code: [
    {
      required: true,
      message: '请输入客户端授权码',
      trigger: 'blur'
    }
  ],
  park_code: [
    {
      required: true,
      message: '请输入车场编号',
      trigger: 'blur'
    }
  ],
  sentry_id: [
    {
      required: true,
      message: '请选择岗亭',
      trigger: 'change'
    }
  ],
  agent_ip: [
    {
      required: true,
      message: '请输入Agent服务器IP',
      trigger: 'blur'
    }
  ],
  agent_port: [
    {
      required: true,
      message: '请输入Agent服务器端口',
      trigger: 'blur'
    }
  ]
});

const checked = ref(false);

onMounted(() => {
  if (setting.park_code && setting.agent_ip && setting.agent_port) {
    const param = {
      park_code: setting.park_code
    };
    parkService.listSentryByParkCode(param).then((res) => {
      if (res.success) {
        sentries.value = res.data;
      }
    });
  }

  // 设置当前值班人、当前值班时间
  loginForm.username = user.username;
  on_time.value = user.on_time;
});

const handleCollector = () => {
  loginForm.login_type = 1;
};
const handleManager = () => {
  loginForm.login_type = 2;
};

// 当前值班开始时间
const on_time = ref();
//收费员登录
const login = (formRef) => {
  if (!setting.terminal_code || !setting.terminal_auth_code || !setting.sentry_id || !setting.park_code || !setting.agent_ip || !setting.agent_port) {
    ElMessage({
      message: '请先完成系统设置',
      type: 'warning'
    });
  }

  formRef.validate().then(() => {
    const loginParam = {
      username: loginForm.username,
      password: loginForm.password,
      login_type: loginForm.login_type,
      terminal_code: setting.terminal_code,
      terminal_auth_code: setting.terminal_auth_code,
      park_code: setting.park_code,
      sentry_id: setting.sentry_id
    };
    loginService.collectorLogin(loginParam).then((res) => {
      if (res.success) {
        const data = res.data;

        if (data.success) {
          // 判断有效期
          if (!data.service_status) {
            ElMessage({
              message: data.service_status_msg,
              type: 'warning'
            });
            return;
          }

          const user_detail = data.user_detail;

          user.$state = {
            user_id: user_detail.user_id,
            name: user_detail.name,
            username: user_detail.username,
            park_id: user_detail.park_id,
            park_name: user_detail.park_name,
            mobile: user_detail.mobile,
            state: user_detail.state,
            token: data.token,
            park_region_id: user_detail.current_region_id,
            park_sentry_id: user_detail.current_sentry_id,
            role: user_detail.role,
            page_work_statistics: user_detail.page_work_statistics
          };

          //检查班次信息;
          const checkShiftParam = {
            park_id: user_detail.park_id,
            park_region_id: user_detail.current_region_id,
            current_user_id: user_detail.user_id,
            sentry_id: setting.sentry_id
          };
          loginService.checkShift(checkShiftParam).then((res) => {
            if (res.success) {
              const data = res.data;

              if (data.result === 0) {
                ElMessage({
                  message: '上一班次未交班，不允许登录',
                  type: 'info'
                });
                return;
              } else if (data.result === 1) {
                // 创建班次
                const createShiftRecordParam = {
                  park_id: user_detail.park_id,
                  park_region_id: user_detail.current_region_id,
                  user_id: user_detail.user_id,
                  sentry_id: setting.sentry_id
                };
                loginService.createShiftRecord(createShiftRecordParam).then((res) => {
                  if (res.success) {
                    ElMessage({
                      message: '登录成功',
                      type: 'success'
                    });
                    on_time.value = res.data.on_time;

                    // 更新班次ID、本次上班时间、网络状态
                    user.shift_handover_record_id = res.data.id;
                    user.on_time = res.data.on_time;
                    user.net_state = '正常';

                    router.push('/');
                  } else {
                    ElMessage({
                      message: '创建新的班次失败',
                      type: 'error'
                    });
                  }
                });
              } else if (data.result === 2) {
                ElMessage({
                  message: '登录成功',
                  type: 'success'
                });
                on_time.value = data.on_time;

                // 更新本次上班时间/网络状态
                user.shift_handover_record_id = data.shift_handover_record_id;
                user.on_time = data.on_time;
                user.net_state = '正常';

                router.push('/');
              }
            } else {
              ElMessage({
                message: res.detail_message ? res.detail_message : res.message,
                type: 'error'
              });
            }
          });
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          });
        }
      } else {
        ElMessage({
          message: res.detail_message ? res.detail_message : res.message,
          type: 'error'
        });
      }
    });
  });
};

// 管理员
const adminLogin = (formRef) => {
  formRef.validate().then(() => {
    if (setting != null) {
      const loginParam = {
        username: loginForm.username,
        password: loginForm.password,
        login_type: loginForm.login_type,
        terminal_code: setting.terminal_code,
        terminal_auth_code: setting.terminal_auth_code,
        park_code: setting.park_code,
        sentry_id: setting.sentry_id
      };
      loginService.adminLogin(loginParam).then((res) => {
        if (res.success) {
          const data = res.data;
          if (data.success == true) {
            // 判断有效期
            if (!data.service_status) {
              ElMessage({
                message: data.service_status_msg || data.message,
                type: 'warning'
              });
              return;
            }
            ElMessage({
              message: '登录成功',
              type: 'success'
            });

            user.$state = {
              user_id: data.user_detail.user_id,
              username: data.user_detail.username,
              name: data.user_detail.name,
              park_id: data.user_detail.park_id,
              park_name: data.user_detail.park_name,
              mobile: data.user_detail.mobile,
              role: data.user_detail.role,
              page_work_statistics: data.user_detail.page_work_statistics,
              token: data.token
            };
            router.push('/');
          } else {
            ElMessage({
              message: data.message,
              type: 'error'
            });
          }
        } else {
          ElMessage({
            message: res.detail_message ? res.detail_message : res.message,
            type: 'error'
          });
        }
      });
    } else {
      ElMessage({
        message: '未进行系统设置，请先进行系统设置',
        type: 'warning'
      });
    }
  });
};

// 弹出设置对话框
const handleClickSetting = () => {
  settingForm.terminal_code = setting.terminal_code;
  settingForm.terminal_auth_code = setting.terminal_auth_code;
  settingForm.park_code = setting.park_code;
  settingForm.sentry_id = setting.sentry_id;
  settingForm.agent_ip = setting.agent_ip;
  settingForm.agent_port = setting.agent_port;
  settingForm.pressure_test = setting.pressure_test;

  sentries.value = [];

  settingDialogVisible.value = true;
};
const verifyLoading = ref(false);
// 验证Agent配置
const verify = () => {
  //先将agent参数放缓存
  setting.agent_ip = settingForm.agent_ip;
  setting.agent_port = settingForm.agent_port;

  if (!settingForm.park_code) {
    ElMessage({
      message: '请填写车场编码',
      type: 'warning'
    });
    return;
  }

  const param = {
    park_code: settingForm.park_code
  };
  verifyLoading.value = true;
  parkService
    .listSentryByParkCode(param)
    .then((res) => {
      if (res.success) {
        ElMessage({
          message: '验证通过',
          type: 'success'
        });

        sentries.value = res.data;
        settingForm.sentry_id = undefined;
      } else {
        ElMessage({
          message: '验证失败，请检查Agent服务器的IP和端口配置',
          type: 'error'
        });
      }
    })
    .finally(() => {
      verifyLoading.value = false;
    });
};

// 刷新
const getSentry = () => {
  //先将agent参数放缓存
  setting.agent_ip = settingForm.agent_ip;
  setting.agent_port = settingForm.agent_port;

  if (!settingForm.park_code) {
    ElMessage({
      message: '请填写车场编码',
      type: 'warning'
    });
    return;
  }

  const param = {
    park_code: settingForm.park_code
  };
  parkService.listSentryByParkCode(param).then((res) => {
    if (res.success) {
      ElMessage({
        message: '刷新岗亭成功',
        type: 'success'
      });

      sentries.value = res.data;
      settingForm.sentry_id = undefined;
    } else {
      ElMessage({
        message: '刷新失败，请检查Agent服务器的IP和端口配置',
        type: 'error'
      });
    }
  });
};

// 取消设置
const cancelSetting = (formRef) => {
  if (!formRef) return;
  formRef.resetFields();

  settingDialogVisible.value = false;
};

// 保存设置
const saveSetting = (formRef) => {
  formRef.validate().then(() => {
    if (sentries.value.length == 0) {
      ElMessage({
        message: '请先完成验证',
        type: 'success'
      });
      return;
    }
    const sentry = sentries.value.find((item) => item.id === settingForm.sentry_id);

    setting.$state = {
      terminal_code: settingForm.terminal_code,
      terminal_auth_code: settingForm.terminal_auth_code,
      sentry_id: settingForm.sentry_id,
      sentry_name: sentry.name,
      park_code: settingForm.park_code,
      agent_ip: settingForm.agent_ip,
      agent_port: settingForm.agent_port,
      pressure_test: settingForm.pressure_test
    };

    // 写入文件
    ipcRenderer.send('write-setting', JSON.stringify(setting.$state, null, '\t'));

    settingDialogVisible.value = false;
    ElMessage({
      message: '系统设置成功',
      type: 'success'
    });
  });
};

// 打开云平台
const handleCloud = () => {
  ipcRenderer.send('openDefaultBrowser', 'https://park.huidawanan.com');
};
</script>

<style scoped lang="scss">
.page {
  width: 100vw;
  min-height: 100vh;
  background-image: url(../assets/background.png);
  background-size: 100% 100%;

  .header {
    text-align: center;
    height: 64px;
    line-height: 64px;
    margin: 0px;
    padding-left: 30px;
    border-bottom: 1px solid #eee;
  }
  .right {
    float: right;
    margin-top: -40px;
    margin-right: 30px;
  }
  .login {
    position: absolute;
    top: 10;
    display: flex;
    width: 100vw;
    height: 100vh;
    align-items: flex-start;
    justify-content: center;
    .login-box {
      width: 1200px;
      overflow: hidden;
      width: auto;
      padding: 0;
      background: #fafafa;
      border: 2px solid #f1f1f1;
      border-radius: 6px;
      margin-top: 160px;
      box-shadow: 0px 2px 8px #fefefe;
      opacity: 0.8;
    }
    .image {
      img {
        display: block;
        width: 400px;
        height: 475px;
        margin: 0 auto;
        user-select: none;
      }
    }
    .form {
      margin-top: 36px;
      text-align: center;

      .login-title {
        font-size: 28px;
        color: #096dd9;
      }
      .content {
        padding: 10px 0px;
      }
    }
  }
}

@media screen and (max-width: 720px) {
  .login {
    display: flex;
    align-items: center;
    justify-content: center;
    .login-box {
      width: 340px;
      margin-top: 0;
    }
  }
}
.content :deep(.el-input__prefix) {
  display: flex;
  align-items: center;
}

.el-form-item__label {
  margin-bottom: 5px;
}

.refresh:hover {
  color: #096dd9;
}
</style>
