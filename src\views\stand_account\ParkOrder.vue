<template>
  <div class="container">
    <park-order-search @form-search="searchParkOrder" @form-reset="resetParamsAndData" />
    <park-order-table ref="table" />  
  </div>
</template>

<script setup name="ParkOrder">
import ParkOrderSearch from './park_order/ParkOrderSearch.vue';
import ParkOrderTable from './park_order/ParkOrderTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchParkOrder = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
