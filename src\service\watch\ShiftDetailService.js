import * as shiftDetail from '@/api/watch/ShiftDetail';

/**
 * @description 交接班统计详情
 * <AUTHOR>
 * @date 2022/10/25
 */
export default {
  /**
   * 交接班明细统计
   */
  statShift(data) {
    return new Promise((resolve, reject) => {
      try {
        shiftDetail.statShift(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
