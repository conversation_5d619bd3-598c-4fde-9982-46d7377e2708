<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-02-27 16:11:11
 * @LastEditTime: 2024-03-01 09:06:48
 * @LastEditors: 达万安 段世煜
 * @Description: 事件列表搜索栏
 * @FilePath: \parking-client-ui\src\views\lane_monitor\components\eventSearch.vue
-->
<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
    <form-search-item>
      <el-date-picker style="width: 100%" v-model="timeRange" type="datetimerange" range-separator="至"
        start-placeholder="事件开始时间" end-placeholder="事件结束时间" :shortcuts="shortcuts" value-format="YYYY-MM-DD HH:mm:ss"
        @change="handleTimeChange" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.gateway_id" placeholder="通道名称" clearable>
        <el-option v-for="item in gatewayIds" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.gateway_type" placeholder="跟车类型" clearable>
        <el-option v-for="item in gatewayTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </form-search-item>
    <template #button>
      <el-col :span="18" class="export-button" style="justify-content: right;">
        <!-- <el-button type="primary" icon="Download" @click="() => emits('export', 'image')">下载图片</el-button> -->
        <el-button type="primary" icon="Operation" @click="() => emits('export', 'list')">导出报表</el-button>
      </el-col>
    </template>
  </FormSearch>
</template>

<script name="DeviceSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive, ref } from 'vue';



const emits = defineEmits(['form-search', 'export']);
// 搜索参数
const form = reactive({
  queryParams: {
    start_time: undefined,
    end_time: undefined
  }
});
const gatewayIds = ref([
  {
    label: '出口',
    value: 1
  },
  {
    label: '入口',
    value: 2
  },
  {
    label: '出入混合',
    value: 3
  }
])
const gatewayTypes = ref([
  {
    label: '出场跟车',
    value: 1
  },
  {
    label: '入场跟车',
    value: 2
  },
])
// 事件类型
const types = [
  {
    label: '报警',
    value: 0
  },
  {
    label: '通知',
    value: 1
  }
];
// 时间选择器 快捷选项
const shortcuts = [
  {
    text: '最近一天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24);
      return [start, end];
    }
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  }
];
// 时间选择器 所选实际值
const timeRange = ref([]);
/**
 * @description 时间选择器变化
 */
const handleTimeChange = (val) => {
  console.log(val);
  if (val && val.length) {
    form.queryParams.start_time = val[0];
    form.queryParams.end_time = val[1];
  }
};
/**
 * @description 搜索事件
 */
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams);
  emits('form-search', query);
};
/**
 * @description 重置事件
 */
const handleAllReset = () => {
  form.queryParams = {
    start_time: undefined,
    end_time: undefined,
    gateway_id: undefined,
    plate_no: undefined,
  };
  timeRange.value = [];
  emits('form-reset', form.queryParams);
};
</script>
<style lang="scss" scoped>
.export-button {
  margin-top: 8px;
  text-align: right;
}
</style>
