<template>
  <el-sub-menu :index="menu.type + menu.id">
    <template #title>
      <el-icon size="18">
        <component :is="menu.icon"></component>
      </el-icon>
      <div>{{ menu.title }}</div>
    </template>
    <template v-for="item in menu.children" :key="item.id">
      <template v-if="!item.children">
        <el-menu-item :index="menu.type + item.id" :key="item.id" @click="activeRouteTab({ path: item.path })" v-if="item.visible === 1">
          <template #title>
            <el-icon size="18">
              <component :is="item.icon"></component>
            </el-icon>
            <span>{{ item.title }}</span>
          </template>
        </el-menu-item>
      </template>
      <template v-else>
        <FrameMenuItem :menu="item" />
      </template>
    </template>
  </el-sub-menu>
</template>

<script setup>
import FrameMenuItem from './FrameMenuItem.vue';
import { activeRouteTab } from '@/utils/tabKit';

const props = defineProps({
  menu: {
    type: Object,
    required: true
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-sub-menu__title) {
  display: block;
  text-align: center;
  padding: 0px;
}
:deep(.el-sub-menu__title span) {
  visibility: visible !important;
  color: #fff !important;
  display: block !important;
}

.el-menu-item {
  height: 42px !important;
  line-height: 42px !important;
}

.el-menu-item.is-active {
  color: #08979c;
}
</style>
