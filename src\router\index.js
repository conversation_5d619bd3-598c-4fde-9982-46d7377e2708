import { createRouter, createWebHashHistory } from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { useUser } from '@/stores/user';
import { staticRoutes } from './static';
import { loadMenusAndRoutes } from '@/utils/menuKit';

const router = createRouter({
  history: createWebHashHistory(),
  routes: staticRoutes
});

// 路由守卫
router.beforeEach((to, from, next) => {
  NProgress.configure({ showSpinner: false });
  NProgress.start();

  const user = useUser();
  if (to.path === '/login') {
    // 如果是访问登录界面，如果用户会话信息存在，代表已登录过，跳转到主页
    user.token ? next({ path: '/' }) : next();
  } else {
    if (!user.token) {
      // 如果访问非登录界面，且户会话信息不存在，代表未登录，则跳转到登录界面
      next({ path: '/login' });
    } else {
      loadMenusAndRoutes(to);
    }

    next();
  }
});

// 路由加载完成
router.afterEach((to, from) => {
  document.title = to.meta.title ? to.meta.title : '惠达万安岗亭值守系统';
  NProgress.done();
});

export default router;
