export function findNavMenus() {
  const data = [
    {
      id: '1',
      name: 'Watch',
      title: '岗亭值守',
      icon: 'DataBoard',
      type: 'menu',
      children: [
        {
          id: '101',
          name: 'Watch',
          title: '岗亭值守',
          path: '/watch/watch',
          type: 'page',
          visible: 1,
          cached: 1,
          component: '/src/views/watch/Watch.vue'
        },
        {
          id: '102',
          name: 'ShiftQuery',
          title: '交接班查询',
          path: '/watch/shiftQuery',
          type: 'page',
          visible: 1,
          cached: 1,
          component: '/src/views/watch/ShiftQuery.vue'
        },
        {
          id: '103',
          name: 'ShiftDetail',
          title: '班次统计信息',
          path: '/watch/shiftDetail',
          type: 'page',
          visible: 0,
          cached: 1,
          component: '/src/views/watch/ShiftDetail.vue'
        },
        {
          id: '104',
          name: 'CurrentShiftDetail',
          title: '本班统计信息',
          path: '/watch/currentShiftDetail',
          type: 'page',
          visible: 1,
          cached: 1,
          component: '/src/views/watch/CurrentShiftDetail.vue'
        },
        {
          id: '105',
          name: 'InterceptRecord',
          title: '拦截记录',
          path: '/watch/interceptRecord',
          type: 'page',
          visible: 1,
          component: '/src/views/watch/InterceptRecord.vue'
        },
        {
          id: '106',
          name: 'MockCar',
          title: '模拟车辆',
          path: '/watch/MockCar',
          type: 'page',
          visible: 1,
          cached: 1,
          component: '/src/views/watch/MockCar.vue'
        }
      ]
    },
    {
      id: '2',
      name: 'StandAccount',
      title: '台账查询',
      icon: 'Tickets',
      type: 'menu',
      children: [
        {
          id: '201',
          name: 'PassInRecord',
          title: '入场记录',
          path: '/stand_account/passInRecord',
          type: 'page',
          visible: 1,
          component: '/src/views/stand_account/PassInRecord.vue'
        },
        {
          id: '202',
          name: 'PassOutRecord',
          title: '出场记录',
          path: '/stand_account/passOutRecord',
          type: 'page',
          visible: 1,
          component: '/src/views/stand_account/PassOutRecord.vue'
        },
        {
          id: '203',
          name: 'ParkOrder',
          title: '停车订单',
          path: '/stand_account/parkOrder',
          type: 'page',
          visible: 1,
          component: '/src/views/stand_account/ParkOrder.vue'
        }
      ]
    },
    {
      id: '3',
      name: 'Car',
      title: '车辆查询',
      icon: 'Van',
      type: 'menu',
      children: [
        {
          id: '301',
          name: 'ParkInCar',
          title: '在场车辆',
          path: '/car/parkInCar',
          type: 'page',
          visible: 1,
          component: '/src/views/car/ParkInCar.vue'
        },
        {
          id: '302',
          name: 'NoPlateCar',
          title: '无牌车辆',
          path: '/car/noPlateCar',
          type: 'page',
          visible: 1,
          component: '/src/views/car/NoPlateCar.vue'
        },
        {
          id: '303',
          name: 'LongStopCar',
          title: '滞留车辆',
          path: '/car/longStopCar',
          type: 'page',
          visible: 1,
          component: '/src/views/car/LongStopCar.vue'
        },
        {
          id: '304',
          name: 'CarInfo',
          title: '一户多车',
          path: '/car/carInfo',
          type: 'page',
          visible: 1,
          component: '/src/views/car/CarInfo.vue'
        }
      ]
    },
    {
      id: '4',
      name: 'Park',
      title: '车场信息',
      icon: 'Place',
      type: 'menu',
      children: [
        {
          id: '401',
          name: 'FeeModel',
          title: '计费模型',
          path: '/park/feeModel',
          type: 'page',
          visible: 1,
          component: '/src/views/park/FeeModel.vue'
        },
        {
          id: '402',
          name: 'FeeModelDetail',
          title: '计费模型详情',
          path: '/park/feeModelDetail',
          type: 'page',
          visible: 0,
          component: '/src/views/park/FeeModelDetail.vue'
        },
        {
          id: '403',
          name: 'ParkInfo',
          title: '停车场信息',
          path: '/park/parkInfo',
          type: 'page',
          visible: 1,
          component: '/src/views/park/ParkInfo.vue'
        },
        {
          id: '404',
          name: 'BlackList',
          title: '黑名单列表',
          path: '/park/blackList',
          type: 'page',
          visible: 1,
          component: '/src/views/park/BlackList.vue'
        },
        {
          id: '405',
          name: 'WhiteList',
          title: '白名单列表',
          path: '/park/whiteList',
          type: 'page',
          visible: 1,
          component: '/src/views/park/WhiteList.vue'
        },
        {
          id: '406',
          name: 'RentCar',
          title: '长租车',
          path: '/park/rentCar',
          type: 'page',
          visible: 1,
          component: '/src/views/park/RentCar.vue'
        },
        {
          id: '407',
          name: 'InterceptRule',
          title: '区域防控规则',
          path: '/park/interceptRule',
          type: 'page',
          visible: 1,
          component: '/src/views/park/InterceptRule.vue'
        }
      ]
    },
    {
      id: '5',
      name: 'Device',
      title: '设备查询',
      icon: 'Cpu',
      type: 'menu',
      children: [
        {
          id: '501',
          name: 'Device',
          title: '设备通信',
          path: '/device/device',
          type: 'page',
          visible: 1,
          component: '/src/views/device/Device.vue'
        },
        {
          id: '502',
          name: 'DeviceState',
          title: '设备状态',
          path: '/device/deviceState',
          type: 'page',
          visible: 1,
          component: '/src/views/device/DeviceState.vue'
        },
        {
          id: '503',
          name: 'Terminal',
          title: '终端查询',
          path: '/device/terminal',
          type: 'page',
          visible: 1,
          component: '/src/views/device/Terminal.vue'
        },
        {
          id: '504',
          name: 'DeviceDetail',
          title: '设备详情',
          path: '/device/deviceDetail',
          type: 'page',
          visible: 0,
          component: '/src/views/device/DeviceDetail.vue'
        }
      ]
    },
    {
      id: '6',
      name: 'Log',
      title: '日志',
      icon: 'Notebook',
      type: 'menu',
      children: [
        {
          id: '601',
          name: 'SysLog',
          title: '系统日志',
          path: '/log/sysLog',
          type: 'page',
          visible: 1,
          component: '/src/views/log/SysLog.vue'
        }
        // {
        //   id: '602',
        //   name: 'OperateLog',
        //   title: '操作记录',
        //   path: '/log/operateLog',
        //   type: 'page',
        //   visible: 1,
        //   component: '/src/views/log/OperateLog.vue'
        // }
      ]
    },
    {
      id: '7',
      name: 'Sync',
      title: '数据同步',
      icon: 'Link',
      type: 'menu',
      children: [
        {
          id: '701',
          name: 'UpData',
          title: '上行数据',
          path: '/sync/upData',
          type: 'page',
          visible: 1,
          component: '/src/views/sync/UpData.vue'
        }
        // {
        //   id: '702',
        //   name: 'DownData',
        //   title: '下行数据',
        //   path: '/sync/downData',
        //   type: 'page',
        //   visible: 1,
        //   component: '/src/views/sync/DownData.vue'
        // }
      ]
    },
    {
      id: '8',
      name: 'Monitor',
      title: '识别相机',
      icon: 'Monitor',
      type: 'menu',
      children: [
        {
          id: '801',
          name: 'MonitorChannel',
          title: '识别相机',
          path: '/monitor/monitorChannel',
          type: 'page',
          visible: 1,
          component: '/src/views/monitor/MonitorChannel.vue'
        }
      ]
    }
  ];

  return {
    url: '/menu/findNavMenus',
    type: 'get',
    data: {
      code: 200,
      success: true,
      message: null,
      data: data
    }
  };
}
