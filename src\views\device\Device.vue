<template>
  <div class="container">
    <device-search @form-search="searchDevice" @form-reset="resetParamsAndData" />
    <device-table ref="table" />  
  </div>
</template>

<script name="Device" setup>
import DeviceSearch from './device/DeviceSearch.vue';
import DeviceTable from './device/DeviceTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchDevice = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
<style lang="scss" scoped></style>
