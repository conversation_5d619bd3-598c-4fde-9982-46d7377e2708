<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-02-26 10:21:40
 * @LastEditTime: 2024-03-01 10:01:02
 * @LastEditors: 达万安 段世煜
 * @Description: 
 * @FilePath: \parking-client-ui\src\views\lane_monitor\components\webFrame.vue
-->
<template>
  <div class="iframe-container" v-loading="loading" element-loading-text="页面加载中，请稍候">
    <iframe ref="iframe" :src="url" class="web-frame" @onload="handleLoad" @onerror="handleError"></iframe>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';
import { getCameraInfo } from '@/api/lane_monitor/laneMonitorApi';
import { useSetting } from '@/stores/setting';
import { storeToRefs } from 'pinia';

const iframe = ref(null);
const url = ref('');

const setting = useSetting();
const { lane_monitor } = storeToRefs(setting);

onMounted(() => {
  nextTick(() => {
    getConfigUrl(lane_monitor.value);
    dealIframeEvent();
  });
});

watch(lane_monitor, (val) => {
  getConfigUrl(val);
});

/**
 * @description : 获取配置地址
 */
const getConfigUrl = async (id) => {
  const { data } = await getCameraInfo(id);
  url.value = data.device_url;
};

const loading = ref(true);
const dealIframeEvent = () => {
  iframe.value.onload = () => {
    handleLoad();
  };
};
const handleLoad = () => {
  loading.value = false;
};
</script>

<style scoped>
.iframe-container,
.web-frame {
  width: 100%;
  height: 100%;
  border: none;
  overflow: hidden;
}
</style>
