import * as common from '@/api/common/CommonApi';

/**
 * 公共服务
 */
export default {
  /**
   * 获取枚举
   * @param {*} module
   * @param {*} param
   * @returns
   */
  findEnums(module, param) {
    return new Promise((resolve, reject) => {
      try {
        common.findEnums(module, param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
