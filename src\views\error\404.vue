<template>
  <el-result>
    <template #title>
      <span class="title">404</span>
    </template>
    <template #sub-title>
      <span class="sub-title">Page Not Found</span>
    </template>
    <template #icon>
      <img src="../../assets/404.png" />
    </template>
    <template #extra>
      <el-button type="primary" @click="backHome">返回首页 </el-button>
    </template>
  </el-result>
</template>

<script setup>
import router from '@/router';

const backHome = () => {
  router.push('/');
};
</script>

<style scoped>
.title {
  font-size: 48px;
}

.sub-title {
  font-size: 28px;
  color: rgba(86, 86, 86, 0.5);
}
</style>
