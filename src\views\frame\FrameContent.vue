<template>
  <div class="frame-content">
    <router-view v-slot="{ Component }">
      <keep-alive :include="includeKeepAlives" :exclude="excludeKeepAlives">
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup>
import { RouterView } from 'vue-router';
import { useMenu } from '@/stores/menu';
import { computed } from 'vue';

const menu = useMenu();

const includeKeepAlives = computed({
  get() {
    return menu.state.includeKeepAlives.join(',');
  }
});
const excludeKeepAlives = computed({
  get() {
    return menu.state.excludeKeepAlives.join(',');
  }
});
</script>

<style lang="scss" scoped>
.frame-content {
  margin-top: 10px;
  padding: 0px 10px;
  height: calc(100vh - 163px);
}
</style>
