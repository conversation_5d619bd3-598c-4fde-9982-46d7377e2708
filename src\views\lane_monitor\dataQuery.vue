<template>
  <div class="container">
    <event-search @form-search="searchEvent" @form-reset="resetParamsAndData" @export="handleExport" />
    <event-table ref="table" />
  </div>
</template>

<script setup>
import eventSearch from './components/eventSearch.vue';
import eventTable from './components/eventTable.vue';
import { ref } from 'vue';

const table = ref(null);

const searchEvent = (queryParams) => {
  table.value.getList(queryParams, true);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams, true);
};
const handleExport = (type) => {
  if (type === 'image') {
    table.value.exportImage();
  } else {
    table.value.exportList();
  }
};
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
