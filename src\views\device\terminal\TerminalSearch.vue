<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="终端名称" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.terminal_code" placeholder="终端编码" /></form-search-item>
  </FormSearch>
</template>

<script name="TerminalSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive } from 'vue';

const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    terminal_code: '',
    name: '',
    page: 1,
    limit: 30
  },
  dateRange: []
});

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    terminal_code: '',
    name: '',
    page: 1,
    limit: 30
  };
  emits('form-reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
