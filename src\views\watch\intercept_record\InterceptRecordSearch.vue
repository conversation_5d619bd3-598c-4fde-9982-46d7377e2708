<template>
  <FormSearch @search="handleSearch" @reset="handleReset">
    <form-search-item>
      <el-input v-model="state.query.plate_no" placeholder="车牌号" />
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="state.dateRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="拦截开始时间"
        end-placeholder="拦截结束时间"
        style="width: 100%"
        format="YYYY-MM-DD hh:mm:ss"
        value-format="YYYY-MM-DD hh:mm:ss"
      />
    </form-search-item>
  </FormSearch>
</template>

<script name="InterceptRecordSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { onMounted, reactive, ref } from 'vue';
import { getDefaultDateTimeRange } from '@/utils/common';

const emits = defineEmits(['form-search']);

const state = reactive({
  query: {
    plate_no: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

onMounted(() => {
  state.dateRange = getDefaultDateTimeRange(7);
  handleSearch();
});

const handleSearch = () => {
  if (undefined !== state.dateRange && state.dateRange.length > 0) {
    state.query.start_time = state.dateRange[0];
    state.query.end_time = state.dateRange[1];
  }
  if (state.dateRange === null) {
    state.query.start_time = undefined;
    state.query.end_time = undefined;
  }
  const query = Object.assign(state.query, { page: 1, limit: 30 });
  emits('form-search', query);
};

const handleReset = () => {
  emits('form-search', resetQuery());
};

const resetQuery = () => {
  state.dateRange = [];
  return (state.query = {
    plate_no: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  });
};
</script>

<style lang="scss" scoped></style>
