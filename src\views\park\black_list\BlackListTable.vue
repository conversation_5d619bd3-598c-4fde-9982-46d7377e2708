<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="name" label="车主" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
        <el-table-column prop="effective_start_time" label="开始时间" align="center" />
        <el-table-column prop="effective_end_time" label="结束时间" align="center" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="BlackListTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import parkService from '@/service/park/ParkService';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {
    plate_no: '',
    start_time: '',
    end_time: ''
  }
});
onMounted(() => {
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  parkService.pageBlackList(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
