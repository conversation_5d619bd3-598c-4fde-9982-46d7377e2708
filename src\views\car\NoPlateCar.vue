<template>
  <div class="container">
    <no-plate-car-search @form-search="searchNoPlateCar" @form-reset="resetParamsAndData" />
    <no-plate-car-table ref="table" />  
  </div>
</template>

<script name="NoPlateCar" setup>
import NoPlateCarSearch from './no_plate_car/NoPlateCarSearch.vue';
import NoPlateCarTable from './no_plate_car/NoPlateCarTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchNoPlateCar = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>

<style lang="scss" scoped></style>
