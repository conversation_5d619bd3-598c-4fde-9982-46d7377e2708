<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-02-27 09:32:35
 * @LastEditTime: 2024-03-01 15:58:29
 * @LastEditors: 达万安 段世煜
 * @Description: 车道识别相机-实时视频-事件列表
 * @FilePath: \parking-client-ui\src\views\lane_monitor\components\eventList.vue
-->
<template>
  <div class="list-container">
    <div class="list-item" v-for="(item, index) in eventList" :key="index">
      <el-image class="item-pic" :initial-index="index" :preview-src-list="srcList" :src="item.event_data"></el-image>
      <div class="item-info">
        <p class="info name">{{ item.event_type }}</p>
        <p class="info">
          <span class="title">事件等级:</span><span class="value type" :class="{ 'warning': item.event_level === '报警' }">{{ item.event_level }}</span>
        </p>
        <p class="info">
          <span class="title">通知时间:</span><span class="value">{{ item.event_time }}</span>
        </p>
      </div>
    </div>
    <div class="page-contaianer">
      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="param.page"
        small
        background
        layout="prev, pager, next"
        hide-on-single-page
        :total="param.total"
        :page-size="param.limit"
        @current-change="handelPageChange"
      />
    </div>
    <el-empty v-show="!eventList.length" description="暂无异常事件" />
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount, reactive } from 'vue';
import { getEventList } from '@/api/lane_monitor/laneMonitorApi';
// 事件列表数据
const eventList = ref([]);
// 预览图片列表
const srcList = ref([]);
// 列表查询参数
const param = reactive({
  total: 0,
  limit: 6,
  page: 1,
  camera_id: ''
});
/**
 * @description: 获取事件列表数据
 */
const fetchEventList = async () => {
  // 调用后端接口获取事件列表数据
  try {
    const { data } = await getEventList(param);
    param.total = parseInt(data.total);
    const usaeData = data.rows;
    const baseUrl = 'data:image/jpg;base64,';
    usaeData.forEach((item) => {
      if (item.event_data && item.event_data.indexOf(baseUrl) === -1) {
        item.event_data = baseUrl + item.event_data;
      } else {
        item.event_data = item.event_data || '';
      }
      srcList.value.push(item.event_data);
    });
    eventList.value = usaeData;
  } catch (error) {
    destoryInterval();
  }
};
/**
 * @description: 分页事件
 */
const handelPageChange = () => {
  fetchEventList();
};

// 循环事件
const intervalId = ref(null);
/**
 * @description: 初始化轮询 5s查询一次报警事件
 */
const initInterval = (id) => {
  // 设置摄像头id
  param.camera_id = id;
  // 如果定时器存在，则销毁定时器
  if (intervalId.value) {
    destoryInterval();
  }
  // 获取事件列表
  fetchEventList();
  // 设置定时器，每隔5000毫秒执行一次fetchEventList函数
  intervalId.value = setInterval(() => {
    fetchEventList();
  }, 5000);
};
/**
 * @description: 销毁轮询
 */
const destoryInterval = () => {
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
};

onBeforeUnmount(() => {
  destoryInterval();
});

defineExpose({
  initInterval
});
</script>

<style scoped lang="scss">
.list-container {
  padding: 10px;
  height: 100%;
  position: relative;
  .list-item {
    display: flex;
    margin-bottom: 20px;
    .item-pic {
      width: 134px;
      height: 86px;
      margin-right: 10px;
    }
    .item-info {
      height: 86px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      color: #606266;
      .name {
        font-size: 15px;
        color: #303133;
      }
      .title {
        opacity: 0.9;
        margin-right: 5px;
      }
      .type::before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        background-color: #e6a23c;
        border-radius: 100%;
        margin-right: 4px;
      }
      .warning::before {
        background-color: #b88230 !important;
      }
    }
  }
  .page-contaianer {
    position: absolute;
    bottom: 35px;
    right: 0;
  }
}
</style>
