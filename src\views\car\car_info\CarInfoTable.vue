<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-divider border-style="dashed">已开通车位：{{ parking_spaces.length }}个</el-divider>
          <el-table :data="parking_spaces" v-loading="loading" border>
            <el-table-column type="selection" style="text-align: center" width="40" />
            <el-table-column prop="park_name" label="车场名" align="center" min-width="140" />
            <el-table-column prop="region" label="区域名" align="center" min-width="160" />
            <el-table-column prop="valid_time" label="有效期" align="center" min-width="100" />
          </el-table>
        </el-col>
        <el-col :span="12">
          <el-divider border-style="dashed">已注册车辆：{{ related_cars.length }}个</el-divider>
          <el-table :data="related_cars" v-loading="loading" border>
            <el-table-column type="selection" style="text-align: center" width="40" />
            <el-table-column prop="plate_no" label="车牌号" align="center" min-width="140" />
            <el-table-column prop="car_owner" label="车主" align="center" min-width="160" />
            <el-table-column prop="mobile" label="手机号" align="center" min-width="100" />
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-divider border-style="dashed">已在场车辆：{{ not_drive_outs.length }}个</el-divider>
          <el-table :data="not_drive_outs" v-loading="loading" border>
            <el-table-column type="selection" style="text-align: center" width="40" />
            <el-table-column prop="car_in_biz_no" label="入场记录流水号" align="center" min-width="140" />
            <el-table-column prop="park_name" label="车场名" align="center" min-width="160" />
            <el-table-column prop="park_region_name" label="区域名" align="center" min-width="100" />
            <el-table-column prop="gateway_name" label="通道名" align="center" min-width="140" />
            <el-table-column prop="in_time" label="入场时间" align="center" min-width="160" />
            <el-table-column prop="plate_no" label="车牌号" align="center" min-width="100" />
            <el-table-column prop="car_type_desc" label="车辆类型" align="center" min-width="100" />
            <el-table-column prop="car_color" label="颜色" align="center" min-width="100" />
            <el-table-column label="入场图片" align="center" min-width="100">
              <template v-slot="scope">
                <el-image
                  v-if="scope.row.car_photo_url !== '' && scope.row.car_photo_url !== null && scope.row.car_photo_url !== undefined"
                  style="width: 100px; height: 100px"
                  :src="scope.row.car_photo_url"
                  :fit="fit"
                  @click="showImage(scope.row.car_photo_url)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="out_state_desc" label="出场状态" align="center" min-width="100" />
            <el-table-column prop="in_type_desc" label="入场类型" align="center" min-width="100" />
          </el-table>
        </el-col>
        <el-col :span="12">
          <el-divider border-style="dashed">最近一天停车记录：{{ last_day_park_in.length }}个</el-divider>
          <el-table :data="last_day_park_in" v-loading="loading" border>
            <el-table-column type="selection" style="text-align: center" width="40" />
            <el-table-column prop="car_in_biz_no" label="入场记录流水号" align="center" min-width="140" />
            <el-table-column prop="park_name" label="车场名" align="center" min-width="160" />
            <el-table-column prop="park_region_name" label="区域名" align="center" min-width="100" />
            <el-table-column prop="gateway_name" label="通道名" align="center" min-width="140" />
            <el-table-column prop="in_time" label="入场时间" align="center" min-width="160" />
            <el-table-column prop="plate_no" label="车牌号" align="center" min-width="100" />
            <el-table-column prop="car_type_desc" label="车辆类型" align="center" min-width="100" />
            <el-table-column prop="car_color" label="颜色" align="center" min-width="100" />
            <el-table-column label="入场图片" align="center" min-width="100">
              <template v-slot="scope">
                <el-image
                  v-if="scope.row.car_photo_url !== '' && scope.row.car_photo_url !== null && scope.row.car_photo_url !== undefined"
                  style="width: 100px; height: 100px"
                  :src="scope.row.car_photo_url"
                  :fit="fit"
                  @click="showImage(scope.row.car_photo_url)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="out_state_desc" label="出场状态" align="center" min-width="100" />
            <el-table-column prop="in_type_desc" label="入场类型" align="center" min-width="100" />
          </el-table>
        </el-col>
      </el-row>
    </div>

    <el-dialog v-model="imageDialogVisible" title="入场图片">
      <img w-full :src="dialogImageUrl" style="max-width: 100%; max-height: 100%" alt="Preview Image" />
    </el-dialog>
  </el-card>
</template>

<script name="CarInfoTable" setup>
import { reactive, ref } from 'vue';
import carService from '@/service/car/CarService';
import { ElMessage } from 'element-plus';

const loading = ref(false);
const parking_spaces = ref([]);
const related_cars = ref([]);
const not_drive_outs = ref([]);
const last_day_park_in = ref([]);
const imageDialogVisible = ref(false);
const dialogImageUrl = ref('');
const data = reactive({
  queryParams: {}
});

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  carService.getCarBindInfo(params).then((response) => {
    if (response.success === true) {
      parking_spaces.value = response.data.parking_spaces;
      related_cars.value = response.data.related_cars;
      not_drive_outs.value = response.data.not_drive_outs;
      last_day_park_in.value = response.data.last_day_park_in;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const showImage = (val) => {
  dialogImageUrl.value = val;
  imageDialogVisible.value = true;
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
