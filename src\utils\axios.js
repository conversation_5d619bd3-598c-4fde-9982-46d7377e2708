import axios from 'axios';
import router from '../router';
import { getToken, getSetting, updateNetState } from './common';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useIpc<PERSON>enderer } from '@vueuse/electron';

// 网络错误，仅提示一次信息
let networkErrorRepeat = true;

const ipcRenderer = useIpcRenderer();

const $ = axios.create({
  timeout: 1000 * 10,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
    'Accept-Language': 'zh-cn'
  },
  withCredentials: true,
  responseType: 'json'
});

// 请求拦截
$.interceptors.request.use(
  (config) => {
    if (config.headers && getToken()) {
      config.headers.Authorization = getToken();
    } else {
      router.push('/login');
    }

    const setting = getSetting();
    if (setting.agent_port && setting.agent_port) {
      const baseURL = 'http://' + setting.agent_ip + ':' + setting.agent_port;
      config.url = baseURL + config.url;
    } else {
      config.url = 'http://127.0.0.1:3000' + config.url;
    }

    ipcRenderer.send('info', {
      source: 'http 请求',
      data: {
        url: config.url,
        method: config.method,
        data: JSON.stringify(config.data)
      }
    });
    return config;
  },
  (error) => {
    const errorInfo = error.response;

    if (errorInfo) {
      error = errorInfo.data;
      const code = errorInfo.code;

      router.push({
        path: `/error/${code}`
      });
    }

    ipcRenderer.send('error', {
      source: 'http 请求',
      error: error
    });
    return Promise.reject(error);
  }
);

// 响应拦截
$.interceptors.response.use(
  (response) => {
    // 修改网络状态
    updateNetState('正常');
    networkErrorRepeat = true;

    let data = response.data;

    if (response.data === undefined) {
      data = JSON.parse(response.request.responseText);
    } else {
      data = response.data;
    }

    ipcRenderer.send('info', {
      source: 'http 响应',
      request: {
        url: response.config.url,
        method: response.config.method,
        data: JSON.stringify(response.config.data)
      },
      response: {
        data: JSON.stringify(data)
      }
    });
    return data;
  },
  (error) => {
    // 处理错误状态码
    ipcRenderer.send('error', {
      source: 'http 响应',
      error: error.toString()
    });

    httpErrorStatusHandle(error);
    return Promise.reject(error);
  }
);

export default $;

/**
 * 处理错误状态码
 * @param {*} error
 * @returns
 */
function httpErrorStatusHandle(error) {
  // 处理被取消的请求
  if (axios.isCancel(error)) {
    return console.error('因请求重复被自动取消：' + error.message);
  }

  let message = '';
  if (error && error.response) {
    switch (error.response.status) {
      case 302:
        message = '接口重定向！';
        break;
      case 400:
        message = '参数不正确！';
        break;
      case 401:
        message = '您未登录系统，或者登陆已经超时，请先登录！';
        break;
      case 403:
        message = '您没有权限操作！';
        break;
      case 404:
        message = '请求地址出错：' + error.response.config.url;
        break;
      case 408:
        message = '请求超时！';
        break;
      case 409:
        message = '系统已存在相同数据！';
        break;
      case 500:
        message = '服务器内部错误！';
        break;
      case 501:
        message = '服务未实现！';
        break;
      case 502:
        message = '网关错误！';
        break;
      case 503:
        message = '服务不可用！';
        break;
      case 504:
        message = '服务暂时无法访问，请稍后再试';
        break;
      case 505:
        message = 'Http 版本不受支持！';
        break;
      default:
        message = '系统异常，请联系管理员！';
        break;
    }
  }
  if (error.message.includes('timeout')) {
    message = '网络请求超时！';
  }

  if (error.message.includes('Network Error')) {
    if (networkErrorRepeat) {
      ElMessageBox.alert('无法连接到AgentServer，请确认IP地址和端口是否正确或联系管理员确认服务是否已运行', '提示', {
        type: 'warning',
        confirmButtonText: '关 闭',
        callback: (action) => {}
      });

      updateNetState('异常');

      networkErrorRepeat = false;
    }
    return;
  } else {
    ElMessage({
      message: message,
      type: 'error'
    });
  }
}
