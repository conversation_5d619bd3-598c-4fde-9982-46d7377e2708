import { useIpc<PERSON><PERSON><PERSON> } from '@vueuse/electron';
import { ElMessageBox, ElProgress } from 'element-plus';
import { h, ref } from 'vue';

const ipcRenderer = useIpcRenderer();
const percentage = ref(0);

export const checkForUpdate = (autoTrigger) => {
  ipcRenderer.on('check-update-reply', (event, res) => {
    const cmd = res.cmd;
    if (cmd === 'update-available') {
      ElMessageBox.confirm('检查到新版本，请立即更新', '提示', {
        confirmButtonText: '确定',
        showClose: false,
        showCancelButton: false,
        closeOnClickModal: false
      }).then(() => {
        ipcRenderer.send('downloadUpdate');

        ElMessageBox({
          title: '正在下载安装包...',
          message: () =>
            h(ElProgress, {
              percentage: percentage.value,
              textInside: true,
              strokeWidth: 18,
              'onUpdate:percentage': (val) => {
                percentage.value = val;
              }
            }),
          showClose: false,
          closeOnClickModal: false,
          showConfirmButton: false
        });
      });
    } else if (cmd === 'update-not-available') {
      if (!autoTrigger) {
        ElMessageBox({
          title: '提示',
          message: res.message,
          showClose: false,
          closeOnClickModal: false,
          type: 'info'
        });
      }
    } else if (cmd === 'download-progress') {
      percentage.value = Math.floor(res.progressObj.percent * 100) / 100;
    } else if (cmd === 'error') {
      ElMessageBox({
        title: '错误',
        message: res.error,
        showClose: false,
        closeOnClickModal: false,
        type: 'error'
      });
    }
  });
  ipcRenderer.send('check-update');
};
