<template>
  <div class="frame-footer">
    <span>
      <span class="label">当前车场：</span>
      <span class="value">{{ user.park_name }}</span>
      <span class="label">车场代码：</span>
      <span class="value">{{ setting.park_code }}</span>
      <span class="label">岗亭：</span>
      <span class="value">{{ setting.sentry_name }}</span>
      <span class="label">总车位：</span>
      <span class="value">{{ allCarCount }} 个</span>
      <span class="label">空闲车位：</span>
      <span class="value">{{ user.free_space }} 个</span>
      <el-button size="small" type="primary" @click="handleShowAdjustSpace" style="margin-top: -3px">校准</el-button>
    </span>
    <span>
      <span class="label">当前登录IP：</span>
      <span class="value">{{ state.system.ip }}</span>
      <span class="label">AgentServer：</span>
      <span class="value">{{ setting.agent_ip }}</span>
      <span class="label">端口：</span>
      <span class="value">{{ setting.agent_port }}</span>
      <span class="label">网络状态：</span>
      <span class="value" style="color: #7cb305; font-weight: bold" v-if="user.net_state === '正常'">{{ user.net_state }}</span>
      <span class="value" style="color: #f5222d; font-weight: bold" v-if="user.net_state === '异常'">{{ user.net_state }}</span>
    </span>
  </div>

  <el-dialog v-model="spaceAdjustVisible" title="车位校准" :close-on-click-modal="false" width="350px">
    <div class="rowflex">
      <div class="rowflex-l">
        <div style="display: flex; align-items: center">
          <el-tooltip>
            <template #content>总车位（XXXX）-系统占用车辆（XXXXX）=xxx个</template><el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          空闲车位:
        </div>
      </div>
      <div>{{ user.free_space }}</div>
    </div>
    <div class="rowflex">
      <div class="rowflex-l">
        <div style="display: flex; align-items: center">
          <el-tooltip>
            <template #content>实际偏差的空闲车位，可进行加或减</template><el-icon><QuestionFilled style="cursor: pointer" /></el-icon> </el-tooltip
          >修正值:
        </div>
      </div>
      <el-input-number v-model="spaceAdjust" placeholder="请输入" style="width: 100%" :precision="0" />
    </div>
    <div class="rowflex">
      <div class="rowflex-l">剩余车位数:</div>
      <div>{{ sxData ? sxData : '' }}</div>
    </div>
    <div class="" style="margin-top: 20px; color: red">
      <div>说明: 校正后1分钟生效，请勿重复操作。</div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="spaceAdjustVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAdjustSpace">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script name="FrameFooter" setup>
import { onMounted, onUnmounted, reactive, ref, computed } from 'vue';
import { useUser } from '@/stores/user';
import { useSetting } from '@/stores/setting';
import { useIpcRenderer } from '@vueuse/electron';
import { ElMessage } from 'element-plus';
import watchService from '@/service/watch/WatchService';
import * as watch from '@/api/watch/Watch';
const allCarCount = ref(0);
const hasCarCount = ref(0);
const user = useUser();
const setting = useSetting();
const ipcRenderer = useIpcRenderer();

const spaceAdjustVisible = ref(false);
const spaceAdjust = ref();
const state = reactive({
  system: {
    ip: undefined
  }
});
const sxData = computed(() => {
  return Number(user.free_space) + (Number(spaceAdjust.value) || 0);
});
const freeSpaceTimer = ref(null);
onMounted(() => {
  getAllCar();
  ipcRenderer.on('read-system-reply', (event, data) => {
    state.system = data;
  });

  ipcRenderer.send('read-system', '');

  freeSpaceTimer.value = setInterval(() => {
    watchService.adjustParkSpace(0).then((res) => {
      console.log('获取空闲车位', res.data);
      if (res.success) {
        user.free_space = res.data;
      }
    });
  }, 60 * 1000);
});
//获取总车位
const getAllCar = async () => {
  const rudata = await watch.allCarData();
  if (rudata.code == 200) {
    allCarCount.value = rudata.data.total_spaces;
  }
};
//获取空闲车位
const gethasCar = async () => {
  watchService.adjustParkSpace(0).then((res) => {
    if (res.success) {
      user.free_space = res.data;
    }
  });
};
onUnmounted(() => {
  if (freeSpaceTimer.value) {
    clearInterval(freeSpaceTimer.value);
  }
});

const handleShowAdjustSpace = () => {
  getAllCar();
  gethasCar();
  spaceAdjust.value = undefined;
  spaceAdjustVisible.value = true;
};

const handleAdjustSpace = () => {
  if (spaceAdjust.value === undefined) {
    ElMessage({
      message: '车位偏离变量不能为空',
      type: 'warning'
    });
    return;
  }
  watchService.adjustParkSpace(spaceAdjust.value).then((res) => {
    if (res.success) {
      ElMessage({
        message: res.message,
        type: 'success'
      });
      user.free_space = res.data;
      spaceAdjustVisible.value = false;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.rowflex {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
  .rowflex-l {
    min-width: 80px;
  }
}
.frame-footer {
  display: flex;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  line-height: 36px;
  background-color: #00474f;
  color: rgba(255, 255, 255, 0.95);
  padding: 0px 16px;
  user-select: none;
}

.label {
  color: rgba(255, 255, 255, 0.8);
}

.value {
  color: #fff566;
  margin-right: 10px;
}
</style>
