<template>
  <div class="container">
    <el-form :model="data.parkInfo" label-width="155px">
      <el-card shadow="hover">
        <template #header>
          <div style="display: inline-block; line-height: 32px">基本信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="停车场编号">
                {{ data.parkInfo.code }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场名称" prop="name">
                {{ data.parkInfo.name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场状态" prop="state">
                {{ data.parkInfo.state }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="省市区">
                {{ data.parkInfo.province_name + '-' + data.parkInfo.city_name + '-' + data.parkInfo.district_name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场坐标"> 经度：{{ data.parkInfo.longitude }} ; 纬度：{{ data.parkInfo.latitude }} </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场地址" prop="address">
                {{ data.parkInfo.address }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="停车场类型" prop="type">
                {{ data.parkInfo.type }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="运营方" prop="org_department_id">
                {{ data.parkInfo.org_department_name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="产权方">
                {{ data.parkInfo.property_name }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="总车位数" prop="total_spaces">
                {{ data.parkInfo.total_spaces }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场面积"> {{ data.parkInfo.area }} &nbsp;㎡ </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开放时段">
                {{ data.parkInfo.open_start_time + '-' + data.parkInfo.open_end_time }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="限高">
                {{ data.parkInfo.limited_height }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="终端截止日期">
                {{ data.parkInfo.terminal_by_date }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">备案信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="备案证编号">
                {{ data.parkInfo.filing_no }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备案车位数">
                {{ data.parkInfo.filing_spaces }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备案证到期时间">
                {{ data.parkInfo.filing_expired_date }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">增值服务</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="租车服务" prop="car_rent">
                {{ data.parkInfo.car_rent_desc }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="洗车服务" prop="car_wash">
                {{ data.parkInfo.car_wash_desc }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否云端值守" prop="cloud_watch">
                {{ data.parkInfo.cloud_watch_desc }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="充电桩快充数量">
                {{ data.parkInfo.fast_charge_piles }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="充电桩慢充数量">
                {{ data.parkInfo.slow_charge_piles }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="充电桩总充数量">
                {{ data.parkInfo.fast_charge_piles + data.parkInfo.slow_charge_piles }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="充电收费标准">
                {{ data.parkInfo.charge_fee_memo }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </el-form>
  </div>
</template>
<script name="ParkInfo" setup>
import { reactive, ref, onMounted } from 'vue';
import parkService from '@/service/park/ParkService';

const loading = ref(false);
const data = reactive({
  parkInfo: {}
});

onMounted(() => {
  getParkInfo();
});

const getParkInfo = () => {
  loading.value = true;
  parkService.getParkInfo().then((response) => {
    if (response.success === true) {
      data.parkInfo = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
</script>

<style lang="scss" scoped></style>
