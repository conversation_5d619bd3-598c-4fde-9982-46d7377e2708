<template>
  <div class="container">
    <passin-record-search @form-search="searchPassinRecord" @form-reset="resetParamsAndData" />
    <passin-record-table ref="table" />  
  </div>
</template>

<script setup name="PassinRecord">
import PassinRecordSearch from './pass_in_record/PassinRecordSearch.vue';
import PassinRecordTable from './pass_in_record/PassinRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchPassinRecord = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  // table.value.getList(queryParams);
};
</script>
