<template>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-card shadow="never" :body-style="{ padding: '16px' }">
        <template #header>
          <div style="display: inline-block; line-height: 32px">模拟车辆入场</div>
        </template>
        <el-form ref="carInFormRef" :model="carIn.form" :rules="carIn.rules" label-position="top">
          <el-form-item prop="gateway_id" label="入口通道">
            <el-select v-model="carIn.form.gateway_id" placeholder="请选择" @change="handleInGatewayChange" style="width: 260px">
              <el-option v-for="item in state.in_gateways" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="gateway_device_id" label="通道设备">
            <el-select v-model="carIn.form.gateway_device_id" placeholder="请选择" style="width: 260px" :disabled="!carIn.form.gateway_id">
              <el-option v-for="item in state.in_gateway_devices" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="plate_no" label="车牌号">
            <el-input v-model="carIn.form.plate_no" placeholder="请输入" style="width: 260px" />
          </el-form-item>
          <el-form-item prop="in_time" label="入场时间">
            <el-date-picker
              v-model="carIn.form.in_time"
              type="datetime"
              placeholder="入场时间"
              style="width: 260px"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <span style="color: #2f54eb">&ensp;偏移时间（分钟）&ensp;</span>
            <el-input-number
              v-model="carIn.offset"
              placeholder="请输入"
              controls-position="right"
              :min="0"
              :precision="0"
              :value-on-clear="0"
              style="width: 100px"
              @change="handleOffsetChange"
            />
          </el-form-item>
          <el-form-item prop="car_type" label="车型">
            <el-select v-model="carIn.form.car_type" placeholder="请选择" style="width: 260px">
              <el-option v-for="item in state.car_type_options" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <div style="margin-top: 30px; text-align: left">
          <el-space>
            <el-button type="primary" @click="mockCarIn(carInFormRef)">模拟入场</el-button>
            <el-button @click="resetCarIn(carInFormRef)">重 置</el-button>
            <el-button @click="convertToCarOut(carInFormRef)">生成出场信息</el-button>
          </el-space>
        </div>
      </el-card>
    </el-col>

    <el-col :span="12">
      <el-card shadow="never" :body-style="{ padding: '16px' }">
        <template #header>
          <div style="display: inline-block; line-height: 32px">模拟车辆出场</div>
        </template>
        <el-form ref="carOutFormRef" :model="carOut.form" :rules="carOut.rules" label-position="top">
          <el-form-item prop="gateway_id" label="出口通道">
            <el-select v-model="carOut.form.gateway_id" placeholder="请选择" @change="handleOutGatewayChange" style="width: 260px">
              <el-option v-for="item in state.out_gateways" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="gateway_device_id" label="通道设备">
            <el-select v-model="carOut.form.gateway_device_id" placeholder="请选择" style="width: 260px" :disabled="!carOut.form.gateway_id">
              <el-option v-for="item in state.out_gateway_devices" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="plate_no" label="车牌号">
            <el-input v-model="carOut.form.plate_no" placeholder="请输入" style="width: 260px" />
          </el-form-item>
          <el-form-item prop="in_time" label="出场时间">
            <!-- <el-date-picker
              v-model="carOut.form.out_time"
              type="datetime"
              placeholder="出场时间"
              style="width: 260px"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            /> -->
            （默认为当前系统时间）
          </el-form-item>
          <el-form-item prop="car_type" label="车型">
            <el-select v-model="carOut.form.car_type" placeholder="请选择" style="width: 260px">
              <el-option v-for="item in state.car_type_options" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <div style="margin-top: 30px; text-align: left">
          <el-space>
            <el-button type="primary" @click="mockCarOut(carOutFormRef)">模拟出场</el-button>
            <el-button @click="resetCarOut(carOutFormRef)">重 置</el-button>
          </el-space>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script name="MockCar" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useSetting } from '@/stores/setting';
import mockCarService from '@/service/watch/MockCarService';
import commonService from '@/service/common/CommonService';
import { getOffsetDateTime } from '@/utils/common';

const setting = useSetting();

const state = reactive({
  in_gateways: [],
  out_gateways: [],
  in_gateway_devices: [],
  out_gateway_devices: [],
  car_type_options: []
});

onMounted(() => {
  // 查询岗亭值守的通道
  querySentryGateway();

  // 初始化枚举下拉框
  initDict();

  // 初始化默认入场
  setDefaultCarIn();
  // 初始化默认出场
  setDefaultCarOut();
});

// 初始化字典值
const initDict = () => {
  const param = [
    {
      enum_key: 'car_type_options',
      enum_value: 'EnumCarType'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    state.car_type_options = response.data.car_type_options;
  });
};

// 查询岗亭值守的通道信息
const querySentryGateway = () => {
  const param = {
    sentry_id: setting.sentry_id
  };
  mockCarService.querySentryGateway(param).then((res) => {
    if (res.success) {
      state.in_gateways = res.data.filter((item) => item.type === 2);
      state.out_gateways = res.data.filter((item) => item.type === 1);

      // 设置默认通道
      carIn.form.gateway_id = state.in_gateways.length > 0 ? state.in_gateways[0].id : undefined;
      carOut.form.gateway_id = state.out_gateways.length > 0 ? state.out_gateways[0].id : undefined;

      // 加载通道设备
      queryGatewayDevice(2);
      queryGatewayDevice(1);
    } else {
      ElMessage({
        message: res.detail_message ? res.detail_message : res.message,
        type: 'error'
      });
    }
  });
};

// 入口通道变更监听
const handleInGatewayChange = (val) => {
  state.in_gateway_devices = [];
  queryGatewayDevice(2);
};

// 入口时间偏移监听
const handleOffsetChange = (val) => {
  const datetime = carIn.form.in_time !== null && carIn.form.in_time !== undefined ? carIn.form.in_time : new Date();
  carIn.form.in_time = getOffsetDateTime(new Date(Date.parse(datetime)), val);
};

// 出口通道变更监听
const handleOutGatewayChange = (val) => {
  state.out_gateway_devices = [];
  queryGatewayDevice(1);
};

// 查询通道设备
const queryGatewayDevice = (gateway_type) => {
  const useForm = gateway_type === 1 ? carOut : carIn;
  mockCarService.queryGatewayDevice(useForm.form.gateway_id).then((res) => {
    if (res.success) {
      if (gateway_type === 1) {
        // 出口通道
        state.out_gateway_devices = res.data;

        // 设置默认通道设备
        carOut.form.gateway_device_id = state.out_gateway_devices.length > 0 ? state.out_gateway_devices[0].id : undefined;
      }
      if (gateway_type === 2) {
        // 入口通道
        state.in_gateway_devices = res.data;

        // 设置默认通道设备
        carIn.form.gateway_device_id = state.in_gateway_devices.length > 0 ? state.in_gateway_devices[0].id : undefined;
      }
    } else {
      ElMessage({
        message: res.detail_message ? res.detail_message : res.message,
        type: 'error'
      });
    }
  });
};

// 模拟入场
const carInFormRef = ref();
const carIn = reactive({
  dialogVisible: false,
  form: {
    gateway_id: undefined,
    gateway_name: undefined,
    gateway_device_id: undefined,
    plate_no: undefined,
    plate_color: '蓝色',
    in_time: undefined,
    car_type: undefined
  },
  rules: {
    gateway_id: [{ required: true, message: '请选择入口通道', trigger: 'change' }],
    gateway_device_id: [{ required: true, message: '请选择通道设备', trigger: 'change' }],
    plate_no: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
    in_time: [{ required: true, message: '请选择入场时间', trigger: 'change' }],
    car_type: [{ required: true, message: '请选择车型', trigger: 'change' }]
  },
  offset: 30
});
// 防抖工具函数
function debounce(fn, delay = 800) {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}
// 模拟车辆入场
const mockCarIn = debounce((formRef) => {
  console.log('模拟车辆入场');
  formRef.validate().then(() => {
    const inGateway = state.in_gateways.find((item) => item.id === carIn.form.gateway_id);
    carIn.form.gateway_name = inGateway.name;

    mockCarService.mockCarIn(carIn.form).then((res) => {
      if (res.success) {
        ElMessage({
          message: '模拟车辆入场成功',
          type: 'success'
        });
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
}, 800);
// 重置入场表单
const resetCarIn = (formRef) => {
  if (!formRef) return;
  formRef.resetFields();

  // 设置默认入场信息
  setDefaultCarIn();
};
// 转为出场信息
const convertToCarOut = (formRef) => {
  formRef.validate().then(() => {
    carOut.form.plate_no = carIn.form.plate_no;
    carOut.form.out_time = getOffsetDateTime(new Date(Date.parse(carIn.form.in_time)), 0 - carIn.offset);
    carOut.form.car_type = carIn.form.car_type;
  });
};
// 设置默认入场信息
const setDefaultCarIn = () => {
  // 设置默认通道
  carIn.form.gateway_id = state.in_gateways.length > 0 ? state.in_gateways[0].id : undefined;

  // 设置默认通道设备
  carIn.form.gateway_device_id = state.in_gateway_devices.length > 0 ? state.in_gateway_devices[0].id : undefined;

  // 设置默认入场时间
  carIn.form.in_time = getOffsetDateTime(new Date(), carIn.offset);

  // 设置车型
  carIn.form.car_type = 1;
};

// 模拟出场
const carOutFormRef = ref();
const carOut = reactive({
  dialogVisible: false,
  form: {
    gateway_id: undefined,
    gateway_device_id: undefined,
    plate_no: undefined,
    plate_color: '蓝色',
    out_time: undefined,
    car_type: undefined
  },
  rules: {
    gateway_id: [{ required: true, message: '请选择出口通道', trigger: 'change' }],
    gateway_device_id: [{ required: true, message: '请选择通道设备', trigger: 'change' }],
    plate_no: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
    out_time: [{ required: true, message: '请选择出场时间', trigger: 'change' }],
    car_type: [{ required: true, message: '请选择车型', trigger: 'change' }]
  }
});
// 模拟车辆出场
const mockCarOut = (formRef) => {
  carOut.form.out_time = getOffsetDateTime(new Date(), 0);
  formRef.validate().then(() => {
    mockCarService.mockCarOut(carOut.form).then((res) => {
      if (res.success) {
        ElMessage({
          message: '模拟车辆出场成功',
          type: 'success'
        });
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
};
// 重置出场表单
const resetCarOut = (formRef) => {
  if (!formRef) return;
  formRef.resetFields();

  // 设置默认出场信息
  setDefaultCarOut();
};
// 设置默认出场信息
const setDefaultCarOut = () => {
  // 设置默认通道
  carOut.form.gateway_id = state.out_gateways.length > 0 ? state.out_gateways[0].id : undefined;

  // 设置默认通道设备
  carOut.form.gateway_device_id = state.out_gateway_devices.length > 0 ? state.out_gateway_devices[0].id : undefined;

  // 设置默认出场时间
  carOut.form.out_time = getOffsetDateTime(new Date(), 0);

  // 设置车型
  carOut.form.car_type = 1;
};
</script>
