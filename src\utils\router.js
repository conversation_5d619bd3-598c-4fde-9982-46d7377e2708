import router from '@/router/index';
import { isNavigationFailure, NavigationFailureType } from 'vue-router';
import { ElNotification } from 'element-plus';
import { baseRoute } from '@/router/static';

/**
 * 导航失败有错误消息的路由push
 */
export const routePush = async (obj) => {
  try {
    let failure = undefined;
    if (obj.path != undefined) {
      failure = await router.push({
        path: obj.path,
        query: obj.query === undefined ? {} : obj.query
      });
    } else {
      failure = await router.push({
        name: obj.name,
        params: obj.params === undefined ? {} : obj.params
      });
    }

    if (isNavigationFailure(failure, NavigationFailureType.aborted)) {
      ElNotification({
        message: '导航失败，导航守卫拦截！',
        type: 'error'
      });

      return false;
    }

    return true;
  } catch (error) {
    ElNotification({
      message: '导航失败，路由无效！',
      type: 'error'
    });

    return false;
  }
};

/**
 * 添加动态路由
 */
export const addDynamicRoutes = (routes) => {
  const viewsComponent = import.meta.globEager('/src/views/**/*.vue');
  addRouteAll(viewsComponent, routes);
};

/**
 * 动态添加路由-带子路由
 */
export const addRouteAll = (viewsComponent, routes) => {
  for (const idx in routes) {
    if (routes[idx].type == 'page' && viewsComponent[routes[idx].component]) {
      addRouteItem(viewsComponent, routes[idx]);
    }

    if (routes[idx].children && routes[idx].children.length > 0) {
      addRouteAll(viewsComponent, routes[idx].children);
    }
  }
};

/**
 * 动态添加路由
 */
export const addRouteItem = (viewsComponent, route) => {
  baseRoute.children.push({
    path: route.path,
    name: route.name,
    component: viewsComponent[route.component].default,
    meta: {
      title: route.title
    }
  });

  router.addRoute(baseRoute);
};
