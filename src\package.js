const fs = require('fs');

// 读取package.json
const packageJson = './package.json';
let packageData = JSON.parse(fs.readFileSync(packageJson));
// 切割后的版本号数组
let arr = packageData.version.split('.');
arr[2] = parseInt(arr[2]) + 1;
// 转换为以"."分割的字符串
packageData.version = arr.join('.');
// 用packageData覆盖package.json内容
fs.writeFile(packageJson, JSON.stringify(packageData, null, '\t'), (err) => {});

// 读取project.json
const projectJson = './src/project.json';
let projectData = JSON.parse(fs.readFileSync(projectJson));
projectData.version = packageData.version;
fs.writeFile(projectJson, JSON.stringify(projectData, null, '\t'), (err) => {});
