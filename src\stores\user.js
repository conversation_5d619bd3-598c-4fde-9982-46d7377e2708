import { defineStore } from 'pinia';

export const useUser = defineStore('user', {
  id: 'user',
  state: () => {
    return {
      user_id: undefined,
      name: undefined,
      username: undefined,
      mobile: undefined,
      role: undefined,
      page_work_statistics: undefined,
      park_id: undefined,
      park_name: undefined,
      park_region_id: undefined,
      park_sentry_id: undefined,
      shift_handover_record_id: undefined,
      on_time: undefined,
      token: undefined,
      net_state: '正常',
      total_money: 0,
      payed_money: 0,
      free_space: 0
    };
  },
  actions: {
    removeUser() {
      this.user_id = undefined;
      this.name = undefined;
      // this.username = undefined;
      this.mobile = undefined;
      this.role = undefined;
      this.park_id = undefined;
      this.park_name = undefined;
      this.park_region_id = undefined;
      this.park_sentry_id = undefined;
      this.token = undefined;
      this.net_state = '正常';
      this.total_money = 0;
      this.payed_money = 0;
      this.free_space = 0;
    },
    removeToken() {
      this.token = '';
    }
  },
  persist: {
    enable: true
  }
});
