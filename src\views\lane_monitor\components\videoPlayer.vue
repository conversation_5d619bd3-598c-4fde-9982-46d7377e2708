<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-02-27 14:14:11
 * @LastEditTime: 2024-03-01 09:20:19
 * @LastEditors: 达万安 段世煜
 * @Description: 视频播放组件
 * @FilePath: \parking-client-ui\src\views\lane_monitor\components\videoPlayer.vue
-->
<template>
  <div class="video-container">
    <div
      ref="player"
      class="video-player"
      v-show="props.url"
      :has-audio="false"
      :muted="true"
      video-title="实时视频"
      @error="
        (e) => {
          e.stopPropagation();
        }
      "
    ></div>
    <el-empty v-show="!props.url" description="暂未获取到视频" />
</div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  url: {
    type: String,
    default: ''
  }
});

const player = ref();

const dp = ref();

const initPlayer = () => {
  if (!props.url) return;
  // eslint-disable-next-line
  dp.value = new DPlayer({
    container: player.value,
    preload: 'none',
    autoplay: true,
    mutex: false,
    video: {
      url: props.url,
      type: 'flv',
      autoplay: true
    },
    live: true
  });
  dp.value.on('canplay', () => {
    dp.value.play();
  });
  dp.value.on('error', (e) => {
    console.error(e);
    e.stopPropagation();
  });
};

defineExpose({
  initPlayer
});
</script>

<style scoped lang="scss">
.video-container {
  height: calc(100% - 90px);
  display: flex;
  justify-content: center;
  .video-player {
    width: 100%;
    height: 100%;
  }
}
</style>
