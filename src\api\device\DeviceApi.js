import $ from '@/utils/axios';

/**
 * 设备信息
 * @param {*} data
 * @returns
 */

//查询停车场相关设备
export const queryDeviceStates = (data) => {
  return $({
    url: '/sentry/device/queryDeviceStates',
    method: 'post',
    data
  });
};

//查询终端（岗亭端）信息
export const pagingSentryTerminals = (data) => {
  return $({
    url: '/sentry/terminal/pagingSentryTerminals',
    method: 'post',
    data
  });
};

//查询设备明细
export const getDeviceStateDetail = (data) => {
  return $({
    url: '/sentry/device/getDeviceStateDetail',
    method: 'post',
    data
  });
};
//查询设备状态
export const listDeviceStates = () => {
  return $({
    url: '/sentry/device/listDeviceStates',
    method: 'get'
  });
};
