<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="primary" @click="handelDetail(scope.row.id)"> 详情 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="park_name" label="车场" align="center" min-width="160" />
        <el-table-column prop="dev_device_name" label="设备名称" align="center" />
        <el-table-column prop="sn" label="设备序列号" align="center" />
        <el-table-column prop="type_desc" label="设备类型" align="center" min-width="160" />
        <el-table-column prop="model" label="设备型号" align="center" />
        <el-table-column prop="dev_factory_name" label="设备厂家" align="center" />
        <el-table-column prop="park_region_name" label="区域" align="center" min-width="160" />
        <el-table-column prop="gateway_name" label="通道" align="center" min-width="110" />
        <el-table-column prop="ip" label="设备IP" align="center" min-width="150" />
        <el-table-column prop="port" label="控制端口" align="center" />
        <el-table-column prop="state_desc" label="设备状态" align="center" />
        <el-table-column prop="online_desc" label="在线状态" align="center" />
        <el-table-column prop="last_heart_time" label="最后一次心跳时间" align="center" min-width="160" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="DeviceTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import deviceService from '@/service/device/DeviceService';
import { activeRouteTab } from '@/utils/tabKit';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});
onMounted(() => {
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  deviceService.queryDeviceStates(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handelDetail = (id) => {
  activeRouteTab({
    path: '/device/deviceDetail',
    query: {
      id: id
    }
  });
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
