<template>
  <el-tabs type="border-card" v-model="editableTabsValue">
    <el-tab-pane v-for="gateway in state.outGateways" :key="gateway.id" :label="gateway.name + '(' + gateway.type_desc + ')'">
      <watch-out
        @first-handler="firstHandler"
        :isActived="state.outGateways[editableTabsValue].id == gateway.id"
        :gateway="gateway"
        v-if="gateway.type === 1"
      ></watch-out>
      <watch-in :gateway="gateway" v-if="gateway.type === 2"></watch-in>
      <div style="margin-top: 8px; float: right">
        <el-button v-if="gateway.name.includes('潮汐')" @click="setAdminLoginDialogVisible(gateway)"
          >切换{{ gateway.type === 1 ? '入口' : '出口' }}</el-button
        >
        <!-- <el-button @click="fleetMode(gateway)">打开车队</el-button> -->
        <!-- <el-button @click="fleetModeClose(gateway)">关闭车队</el-button> -->
        <el-button @click="mockCar">模拟车辆</el-button>
        <el-button @click="testLift(gateway)">抬杆放行</el-button>
        <el-button @click="testOneMachine(gateway)">一体机测试</el-button>
      </div>
    </el-tab-pane>
  </el-tabs>
  <!-- 切换出入口验证 -->
  <el-dialog title="权限验证" v-model="adminLoginDialogVisible" :close-on-click-modal="false" width="560px">
    <el-form ref="loginFormRef" label-position="top" :rules="rules" :model="loginForm">
      <el-form-item prop="username" label="管理员账号">
        <el-input v-model="loginForm.username" placeholder="请输入管理员账号" />
      </el-form-item>
      <el-form-item prop="password" label="管理员密码">
        <el-input v-model="loginForm.password" type="password" placeholder="请输入管理员密码" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancleLogin">取 消</el-button>
        <el-button type="primary" @click="adminLogin">验 证</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 抬杆放行 -->
  <el-dialog title="抬杆放行" v-model="listDialogVisible" :close-on-click-modal="false" width="560px">
    <el-form ref="liftFormRef" label-position="top" :rules="liftRules" :model="liftForm">
      <el-form-item prop="reason_type" label="抬杆原因">
        <el-select v-model="liftForm.reason_type" placeholder="请选择抬杆原因">
          <el-option v-for="item in liftRollReasons" :key="item.value" :label="item.key" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="remark" label="备注">
        <el-input v-model="liftForm.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancleLift">取 消</el-button>
        <el-button type="primary" @click="liftRoll">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script name="Watch" setup>
import loginService from '@/service/login/LoginService';
import watchService from '@/service/watch/WatchService';
import commonService from '@/service/common/CommonService';
import { useSetting } from '@/stores/setting';
import { useUser } from '@/stores/user';
import { activeRouteTab } from '@/utils/tabKit';
import { useIpcRenderer } from '@vueuse/electron';
import { ElMessage } from 'element-plus';
import { v4 as uuid } from 'uuid';
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import WatchIn from './WatchIn.vue';
import WatchOut from './WatchOut.vue';

const ipcRenderer = useIpcRenderer();
const setting = useSetting();
const user = useUser();
const loginFormRef = ref();

const adminLoginDialogVisible = ref(false);
const editableTabsValue = ref('0');
const loginForm = reactive({
  username: '',
  password: '',
  login_type: 2
});

const rules = reactive({
  username: [
    {
      required: true,
      message: '请输入请输入用户名',
      trigger: 'blur'
    }
  ],
  password: [
    {
      required: true,
      message: '请输入管理员密码',
      trigger: 'blur'
    }
  ]
});

const state = reactive({
  path: 'ws://' + setting.agent_ip + ':8989/ws',
  socket: undefined,
  intervalPush: undefined,
  gatewayIds: [],
  outGateways: [],
  manualClose: false
});

onMounted(() => {
  // 查询岗亭值守的通道
  querySentryGateway();
  initSelects();
});

onUnmounted(() => {
  clearInterval(state.intervalPush);
  state.manualClose = true;
  state.socket.close();
  state.socket = undefined;
});
const firstHandler = (v) => {
  let index = state.outGateways.findIndex((item) => item.id == v);
  if (index >= 0) {
    editableTabsValue.value = String(index);
  }
};
// 查询岗亭值守的通道信息
const querySentryGateway = () => {
  const param = {
    sentry_id: setting.sentry_id
  };
  watchService.querySentryGateway(param).then((res) => {
    if (res.success) {
      state.gatewayIds = res.data.map((item) => item.id);
      // state.outGateways = res.data.filter((item) => item.type === 1 || item.type === 3);
      state.outGateways = res.data;
      // 初始化WebSocket
      createWebSocket();
    } else {
      ElMessage({
        message: res.detail_message ? res.detail_message : res.message,
        type: 'error'
      });
    }
  });
};

const liftRollReasons = ref([]);
const initSelects = () => {
  const param = [{ enum_key: 'liftRollReasons', enum_value: 'EnumLiftRollReason' }];
  commonService.findEnums('park', param).then((response) => {
    liftRollReasons.value = response.data.liftRollReasons;
  });
};

// 初始化WebSocket
const createWebSocket = () => {
  if (typeof WebSocket === 'undefined') {
    ElMessage({
      message: '浏览器不支持WebSocket',
      type: 'warning'
    });
  } else {
    if (!state.socket) {
      // 实例化socket
      state.socket = new WebSocket(state.path);
      // 监听socket消息
      state.socket.onmessage = handleMessage;

      // 监听socket关闭
      state.socket.onclose = handleClose;

      // 监听socket连接
      state.socket.onopen = handleOpen;
    } else {
      handleOpen();
    }
  }
};

// WebSocket打开处理
const handleOpen = () => {
  // 注册
  registSentry();

  // 心跳
  heartBeat();

  if (state.socket.readyState == WebSocket.OPEN) {
    console.log('WebSocket 连接成功');

    ipcRenderer.send('info', {
      source: 'WebSocket 状态消息',
      data: 'WebSocket 连接成功'
    });

    user.net_state = '正常';
  } else {
    user.net_state = '异常';
  }
};

// WebSocket关闭处理
const handleClose = () => {
  clearInterval(state.intervalPush);
  console.log('WebSocket 连接已关闭');
  ipcRenderer.send('info', {
    source: 'WebSocket 状态消息',
    data: 'WebSocket 连接已关闭'
  });

  user.net_state = '异常';

  if (!state.manualClose) {
    state.intervalPush = setInterval(() => {
      state.socket = undefined;
      console.log('WebSocket 正在尝试重连...');

      ipcRenderer.send('info', {
        source: 'WebSocket 状态消息',
        data: 'WebSocket 正在尝试重连...'
      });
      createWebSocket();
    }, 1000 * 10);
  }
};

// 接收消息
const handleMessage = (msgEvent) => {
  const msg = JSON.parse(msgEvent.data);
  ipcRenderer.send('info', {
    source: 'WebSocket 收到消息',
    data: msg
  });

  if (msg.command === 'c2s.sentry-register') {
    // 岗亭注册响应
    console.log(msg);
  } else if (msg.command === 's2c.car-event') {
    // 车辆事件
    console.log(msg);

    window.dispatchEvent(
      new CustomEvent('onmessage', {
        detail: msg
      })
    );

    // 给服务端返回结果
    const param = {
      packet_header: {},
      msg_id: msg.msg_id,
      command: 's2c.car-event',
      type: 'ACK',
      result: true,
      message: '成功'
    };
    send(JSON.stringify(param));
  } else if (msg.command === 's2c.car-pay-over-event') {
    // 支付事件
    console.log(msg);

    window.dispatchEvent(
      new CustomEvent('onmessage', {
        detail: msg
      })
    );

    // 给服务端返回结果
    const param = {
      packet_header: {},
      msg_id: msg.msg_id,
      command: 's2c.car-pay-over-event',
      type: 'ACK',
      result: true,
      message: '成功'
    };
    send(JSON.stringify(param));
  } else if (msg.command === 's2c.car-in-abnormal-event') {
    // 告警信息
    console.log(msg);

    window.dispatchEvent(
      new CustomEvent('onmessage', {
        detail: msg
      })
    );

    // 给服务端返回结果
    const param = {
      packet_header: {},
      msg_id: msg.msg_id,
      command: 's2c.car-in-abnormal-event',
      type: 'ACK',
      result: true,
      message: '成功'
    };
    send(JSON.stringify(param));
  } else if (msg.command === 'c2s.heart-beat') {
    // console.log(msg);

    // 如果岗亭不在线，重新注册岗亭
    if (!msg.online) {
      registSentry();
    }
  } else {
    console.log(msg);
  }

  user.net_state = '正常';
};

// 发送消息
const send = (msg) => {
  if (state.socket.readyState == WebSocket.OPEN) {
    //如果WebSocket是打开状态
    state.socket.send(msg);

    ipcRenderer.send('info', {
      source: 'WebSocket 发送消息',
      data: JSON.parse(msg)
    });
  } else if (state.socket.readyState == WebSocket.CLOSED) {
    createWebSocket();
  }
};

// 心跳
const heartBeat = () => {
  clearInterval(state.intervalPush);

  state.intervalPush = setInterval(() => {
    const param = {
      packet_header: {},
      msg_id: uuid(),
      command: 'c2s.heart-beat',
      type: 'REQ',
      sentry_id: setting.sentry_id,
      timestamp: new Date().getTime()
    };

    send(JSON.stringify(param));

    if (state.socket.readyState === WebSocket.OPEN) {
      user.net_state = '正常';
    } else {
      user.net_state = '异常';
    }
  }, 1000 * 10);
};

// 注册岗亭端
const registSentry = () => {
  const param = {
    packet_header: {},
    msg_id: uuid(),
    command: 'c2s.sentry-register',
    type: 'REQ',
    sentry_id: setting.sentry_id,
    gateway_ids: state.gatewayIds,
    user_id: user.user_id
  };
  send(JSON.stringify(param));
};

// 模拟车辆
const mockCar = () => {
  activeRouteTab({
    path: '/watch/MockCar'
  });
};

const liftForm = ref({
  gateway_id: '',
  direction: '',
  scene: 1,
  reason_type: undefined,
  remark: undefined
});
const listDialogVisible = ref(false);
const liftFormRef = ref();
const liftRules = reactive({
  reason_type: [
    {
      required: true,
      message: '请选择抬杆原因',
      trigger: 'change'
    }
  ],
  remark: [
    {
      required: true,
      message: '请输入备注',
      trigger: 'blur'
    }
  ]
});

// 抬杆放行
const testLift = (gateway) => {
  liftForm.value = {
    gateway_id: gateway.id,
    direction: gateway.type,
    scene: 1,
    reason_type: undefined,
    remark: undefined
  };
  listDialogVisible.value = true;
};
const cancleLift = () => {
  liftFormRef.value.resetFields();
  listDialogVisible.value = false;
};
const liftRoll = () => {
  liftFormRef.value.validate().then(() => {
    watchService.liftRoll(liftForm.value).then((res) => {
      if (res.success) {
        ElMessage({
          type: 'success',
          message: res.message
        });
        listDialogVisible.value = false;
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
};

const currentGateway = ref();
const setAdminLoginDialogVisible = (gateway) => {
  adminLoginDialogVisible.value = true;
  currentGateway.value = gateway;
};
const adminLogin = () => {
  loginFormRef.value.validate().then(() => {
    const loginParam = {
      username: loginForm.username,
      password: loginForm.password,
      login_type: loginForm.login_type,
      terminal_code: setting.terminal_code,
      terminal_auth_code: setting.terminal_auth_code,
      park_code: setting.park_code,
      sentry_id: setting.sentry_id
    };
    loginService.adminLogin(loginParam).then((res) => {
      if (res.success) {
        const data = res.data;
        if (data.success == true) {
          // 判断有效期
          if (!data.service_status) {
            ElMessage({
              message: data.service_status_msg || data.message,
              type: 'warning'
            });
          } else {
            changeInOut(currentGateway.value);
            loginFormRef.value.resetFields();
            adminLoginDialogVisible.value = false;
          }
        } else {
          ElMessage({
            message: data.service_status_msg || data.message,
            type: 'error'
          });
        }
      }
    });
  });
};
const cancleLogin = () => {
  loginFormRef.value.resetFields();
  adminLoginDialogVisible.value = false;
};
// 切换出入口
const changeInOut = (gateway) => {
  const params = {
    gatewayId: gateway.id,
    gatewayDirection: gateway.type
  };
  watchService.changeInOut(params).then((res) => {
    if (res.success) {
      watchService.querySentryGateway({ sentry_id: setting.sentry_id }).then((res) => {
        if (res.success) {
          state.gatewayIds = res.data.map((item) => item.id);
          state.outGateways = res.data;
        } else {
          ElMessage({
            message: res.detail_message ? res.detail_message : res.message,
            type: 'error'
          });
        }
      });
    }
  });
};
// 打开车队
const fleetMode = (gateway) => {
  const param = {
    gateway_id: gateway.id,
    direction: gateway.type
  };
  watchService.fleetMode(param).then((res) => {
    if (res.success) {
      ElMessage({
        type: 'success',
        message: res.message
      });
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 关闭车队
const fleetModeClose = (gateway) => {
  const param = {
    gateway_id: gateway.id,
    direction: gateway.type
  };
  watchService.fleetModeClose(param).then((res) => {
    if (res.success) {
      ElMessage({
        type: 'success',
        message: res.message
      });
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 一体机测试
const testOneMachine = (gateway) => {
  const param = {
    gateway_id: gateway.id,
    direction: gateway.type,
    scene_id: 6
  };
  watchService.testOneMachine(param).then((res) => {
    if (res.success) {
      ElMessage({
        type: 'success',
        message: res.message
      });
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
</script>

<style lang="scss" scoped></style>
