<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="park_name" label="车场名	" align="center" />
        <el-table-column prop="gateway_name" label="通道名" align="center" />
        <el-table-column prop="device_id" label="设备ID" align="center" />
        <el-table-column prop="name" label="设备名" align="center" />
        <el-table-column prop="type_desc" label="设备类型" align="center" />
        <el-table-column prop="ip" label="IP" align="center" />
        <el-table-column prop="port" label="端口" align="center" />
        <el-table-column prop="username" label="管理账号" align="center" />
        <el-table-column prop="in_out_desc" label="设备方向" align="center" />
        <el-table-column prop="enabled_desc" label="是否启用" align="center" />
        <el-table-column prop="manufactory" label="厂商" align="center" />
        <el-table-column prop="manufactory_code" label="厂商编码" align="center" />
        <el-table-column prop="online_desc" label="是否在线" align="center" />
        <el-table-column prop="last_check_time" label="最后检查时间" align="center" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="DeviceStateTable" setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import deviceService from '@/service/device/DeviceService';

const tableData = ref([]);
const loading = ref(false);
onMounted(() => {
  getList();
});

const getList = () => {
  loading.value = true;
  deviceService.listDeviceStates().then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
</script>
<style lang="scss" scoped></style>
