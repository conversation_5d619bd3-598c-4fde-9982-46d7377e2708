<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-02-26 10:16:18
 * @LastEditTime: 2024-03-01 08:54:25
 * @LastEditors: 惠达万安 段世煜
 * @Description: 车道识别相机-实时视频
 * @FilePath: \parking-client-ui\src\views\lane_monitor\realtimeVideo.vue
-->
<template>
  <div class="real-video-container">
    <div class="video child-container">
      <!-- 视频播放器 -->
      <div class="title">实时视频</div>
      <div class="video-info">
        <div class="info-item">
          <div class="info">
            <el-icon class="title-icon"><Camera /></el-icon>
            <span class="value">{{ videoInfo.name || '- -' }}</span>
          </div>
        </div>
        <div class="info-item">
          <div class="info">
            <el-icon class="title-icon"><LocationInformation /></el-icon>
            <span class="value">{{ videoInfo.location || '- -' }}</span>
          </div>
        </div>
      </div>
      <video-player :url="videoInfo.url" ref="videoRef" />
    </div>
    <div class="event child-container">
      <!-- 事件列表 -->
      <div class="title">异常事件</div>
      <event-list ref="listRef" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, nextTick } from 'vue';
import { getCameraInfo } from '@/api/lane_monitor/laneMonitorApi';
import { useSetting } from '@/stores/setting';
import eventList from './components/eventList.vue';
import videoPlayer from './components/videoPlayer.vue';
import { storeToRefs } from 'pinia';

// 事件list实体
const listRef = ref();
// 视频组件实体
const videoRef = ref();
// 摄像头信息
const videoInfo = reactive({
  name: '',
  location: '',
  url: ''
});

/**
 * @description 获取相机基本信息
 */
const fetchVideoInfo = async (id) => {
  const { data } = await getCameraInfo(id);
  videoInfo.name = data.device_name;
  videoInfo.location = data.device_position;
  videoInfo.url = data.flv_url;
  nextTick(() => {
    if (videoRef.value) {
      videoRef.value.initPlayer(id);
    }
  });
};
// camera_id
const setting = useSetting();
const { lane_monitor } = storeToRefs(setting);

onMounted(() => {
  initData(lane_monitor.value);
});

watch(lane_monitor, (val) => {
  initData(val);
});
/**
 * @description: 初始化数据
 * @param {*} val
 */
const initData = (val) => {
  fetchVideoInfo(val);
  if (listRef.value) {
    listRef.value.initInterval(val);
  }
};
</script>

<style scoped lang="scss">
.real-video-container {
  display: flex;
  justify-content: space-between;
  height: 100%;
  .child-container {
    background-color: #fff;
    padding: 10px 20px;
    height: 100%;
  }
  .video {
    width: calc(75% - 10px);
    .video-info {
      display: flex;
      padding: 0 20px;
      margin-top: 10px;
      background-color: #007aff20;
      border-radius: 5px;
      height: 40px;
      align-items: center;
      .info-item {
        width: 50%;
        color: #606266;
        .title-icon {
          margin-right: 6px;
        }
      }
    }
  }
  .event {
    width: 25%;
  }
  .title {
    height: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #303133;
  }
  .title::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 20px;
    background-color: #007aff;
    border-radius: 8px;
    margin-right: 8px;
  }
}
</style>
