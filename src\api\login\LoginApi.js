import $ from '@/utils/axios';

/**
 * 登录/退出登录
 */

/**
 * 收费员登录
 * @param {*} data
 * @returns
 */
export const collectorLogin = (data) => {
  return $({
    url: '/sentry/auth/login/collectorLogin',
    method: 'post',
    data
  });
};
/**
 * 管理员登录
 * @param {*} data
 * @returns
 */
export const adminLogin = (data) => {
  return $({
    url: '/sentry/auth/login/adminLogin',
    method: 'post',
    data
  });
};
/**
 * 退出登录
 * @param {*} data
 * @returns
 */
export const logout = () => {
  return $({
    url: '/sentry/auth/login/logout',
    method: 'post'
  });
};

/**
 * 检查班次信息
 * @param {*} data
 * @returns
 */
export const checkShift = (data) => {
  return $({
    url: '/sentry/shift/checkShift',
    method: 'post',
    data
  });
};

/**
 * 创建新的班次
 * @param {*} data
 * @returns
 */
export const createShiftRecord = (data) => {
  return $({
    url: '/sentry/shift/createShiftRecord',
    method: 'post',
    data
  });
};

//修改密码;
export const changePasswd = (data) => {
  return $({
    url: '/sentry/auth/login/changePasswd',
    method: 'post',
    data
  });
};
