<template>
  <div class="container">
    <intercept-record-search @form-search="search" />
    <intercept-record-table ref="table" />
  </div>
</template>

<script name="InterceptRecord" setup>
import InterceptRecordSearch from './intercept_record/InterceptRecordSearch.vue';
import InterceptRecordTable from './intercept_record/InterceptRecordTable.vue';
import { ref } from 'vue';

const table = ref();

const search = (query) => {
  table.value.list(query);
};
</script>

<style lang="scss" scoped></style>
