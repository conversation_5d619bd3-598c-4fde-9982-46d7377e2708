<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.sn" placeholder="设备序列号" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.online_state" placeholder="是否在线" clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="DeviceSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive, onMounted, ref } from 'vue';
import commonService from '@/service/common/CommonService';

const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    sn: undefined,
    online_state: undefined
  }
});
const states = ref([]);

onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumDeviceOnlineState' }];
  commonService.findEnums('park', param).then((response) => {
    states.value = response.data.states;
  });
};

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams);
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    sn: undefined,
    online_state: undefined
  };
  emits('form-reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
