<template>
    <FormSearch @search="handleDataSearch" @reset="handleAllReset">
        <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="值班人" /></form-search-item>
        <form-search-item>
            <el-date-picker
                    v-model="form.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="width: 100%"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
            />
        </form-search-item>
    </FormSearch>
</template>

<script name="RentCarSearch" setup>
    import FormSearch from '@/components/FormSearch.vue';
    import FormSearchItem from '@/components/FormSearchItem.vue';
    import { reactive, onMounted } from 'vue';
    import { getDefaultDateRange } from '@/utils/common';

    const emits = defineEmits(['form-search']);

    const form = reactive({
        queryParams: {
            plate_no: '',
            start_time: '',
            end_time: ''
        },
        dateRange: []
    });

    onMounted(() => {
        form.dateRange = getDefaultDateRange(7);
    });

    const handleDataSearch = () => {
        if (undefined !== form.dateRange && form.dateRange.length > 0) {
            form.queryParams.start_time = form.dateRange[0];
            form.queryParams.end_time = form.dateRange[1];
        }
        if (form.dateRange === null) {
            form.queryParams.start_time = undefined;
            form.queryParams.end_time = undefined;
        }
        const query = Object.assign(form.queryParams);
        emits('form-search', query);
    };
    const handleAllReset = () => {
        form.queryParams = {
            plate_no: '',
            start_time: '',
            end_time: ''
        };
        form.dateRange = [];
        emits('form-reset', form.queryParams);
    };
</script>
<style lang="scss" scoped></style>
