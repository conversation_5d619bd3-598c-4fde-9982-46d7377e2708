<template>
  <el-card shadow="never">
    <div>
      <span class="label">当班人</span>
      <el-input style="width: 300px" :value="state.param.collector_name" readonly></el-input>
    </div>
    <div style="margin-top: 10px">
      <span class="label">当班时间</span>
      <el-input style="width: 300px" :value="state.param.on_time" readonly></el-input>
    </div>
  </el-card>
  <el-card class="table" shadow="never">
    <div ref="tableRef">
      <el-table :data="table.data" border v-loading="table.loading">
        <el-table-column>
          <template #header>
            应收
            <span class="table-column-header">
              <br />
              ( <span class="money">金额</span> / <span class="count">笔数</span> )
            </span>
          </template>
          <template v-slot="scope">
            <span class="money">{{ scope.row.should_pay_money }}</span>
            <br />
            <span class="count">{{ scope.row.should_pay_money_cnt }}</span>
          </template>
        </el-table-column>
        <el-table-column>
          <template #header>
            实收
            <span class="table-column-header">
              <br />
              ( <span class="money">金额</span> / <span class="count">笔数</span> )
            </span>
          </template>
          <template v-slot="scope">
            <span class="money">{{ scope.row.payed_money }}</span>
            <br />
            <span class="count">{{ scope.row.payed_money_cnt }}</span>
          </template>
        </el-table-column>
        <el-table-column label="电子支付">
          <el-table-column prop="ali_pay_money">
            <template #header>
              支付宝支付
              <span class="table-column-header">
                <br />
                ( <span class="money">金额</span> / <span class="count">笔数</span> )
              </span>
            </template>
            <template v-slot="scope">
              <span class="money">{{ scope.row.ali_money }}</span>
              <br />
              <span class="count">{{ scope.row.ali_money_cnt }}</span>
            </template>
          </el-table-column>
          <el-table-column>
            <template #header>
              微信支付
              <span class="table-column-header">
                <br />
                ( <span class="money">金额</span> / <span class="count">笔数</span> )
              </span>
            </template>
            <template v-slot="scope">
              <span class="money">{{ scope.row.wx_money }}</span>
              <br />
              <span class="count">{{ scope.row.wx_money_cnt }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="现金收费">
          <el-table-column>
            <template #header>
              现金支付
              <span class="table-column-header">
                <br />
                ( <span class="money">金额</span> / <span class="count">笔数</span> )
              </span>
            </template>
            <template v-slot="scope">
              <span class="money">{{ scope.row.cash_money }}</span>
              <br />
              <span class="count">{{ scope.row.cash_money_cnt }}</span>
            </template>
          </el-table-column>
          <el-table-column>
            <template #header>
              特殊处理
              <span class="table-column-header">
                <br />
                ( <span class="money">金额</span> / <span class="count">笔数</span> )
              </span>
            </template>
            <template v-slot="scope">
              <span class="money">{{ scope.row.special_money }}</span>
              <br />
              <span class="count">{{ scope.row.special_money_cnt }}</span>
            </template>
          </el-table-column>
          <el-table-column>
            <template #header>
              其他
              <span class="table-column-header">
                <br />
                ( <span class="money">金额</span> / <span class="count">笔数</span> )
              </span>
            </template>
            <template v-slot="scope">
              <span class="money">{{ scope.row.other_money }}</span>
              <br />
              <span class="count">{{ scope.row.other_money_cnt }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="异常处理损失">
          <el-table-column>
            <template #header>
              特殊处理损失
              <span class="table-column-header">
                <br />
                ( <span class="money">金额</span> / <span class="count">笔数</span> )
              </span>
            </template>
            <template v-slot="scope">
              <span class="money">{{ scope.row.special_loss_money }}</span>
              <br />
              <span class="count">{{ scope.row.special_loss_money_cnt }}</span>
            </template>
          </el-table-column>
          <el-table-column>
            <template #header>
              被冲车辆损失
              <span class="table-column-header">
                <br />
                ( <span class="money">金额</span> / <span class="count">笔数</span> )
              </span>
            </template>
            <template v-slot="scope">
              <span class="money">{{ scope.row.flush_loss_money }}</span>
              <br />
              <span class="count">{{ scope.row.flush_loss_money_cnt }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column>
          <template #header>
            优免抵扣
            <span class="table-column-header">
              <br />
              ( <span class="money">金额</span> / <span class="count">笔数</span> )
            </span>
          </template>
          <template v-slot="scope">
            <span class="money">{{ scope.row.debate_money }}</span>
            <br />
            <span class="count">{{ scope.row.debate_money_cnt }}</span>
          </template>
        </el-table-column>
        <el-table-column>
          <template #header>
            手动抬杆
            <span class="table-column-header">
              <br />
              ( <span class="money">金额</span> / <span class="count">笔数</span> )
            </span>
          </template>
          <template v-slot="scope">
            <span class="money">{{ scope.row.manual_money }}</span>
            <br />
            <span class="count">{{ scope.row.manual_money_cnt }}</span>
          </template>
        </el-table-column>
        <el-table-column label="统计时间" prop="stat_time" width="160"></el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script name="CurrentShiftDetail" setup>
import { reactive, ref, onActivated } from 'vue';
import { useUser } from '@/stores/user';
import shiftDetailService from '@/service/watch/ShiftDetailService';
import { ElMessage } from 'element-plus';

const user = useUser();

const state = reactive({
  param: {
    collector_name: undefined,
    on_time: undefined
  }
});

const tableRef = ref();
const table = reactive({
  query: {
    shift_record_id: undefined
  },
  data: [],
  loading: false
});

onActivated(() => {
  state.param = {
    collector_name: user.name,
    on_time: user.on_time
  };

  table.query = {
    shift_record_id: user.shift_handover_record_id
  };

  stateShift();
});

const stateShift = () => {
  table.loading = true;

  shiftDetailService.statShift(table.query).then((res) => {
    if (res.success) {
      table.data = [res.data];
    } else {
      ElMessage({
        message: res.detail_message ? res.detail_message : res.message,
        type: 'error'
      });
    }
    table.loading = false;
  });
};
</script>

<style lang="scss" scoped>
.label {
  display: inline-block;
  color: rgba(0, 0, 0, 0.65);
  text-align: right;
  width: 90px;
  margin-right: 8px;
}

.table-column-header {
  color: rgba(0, 0, 0, 0.5);
}

.money {
  color: #f5222d;
}
.count {
  color: #1890ff;
}
</style>
