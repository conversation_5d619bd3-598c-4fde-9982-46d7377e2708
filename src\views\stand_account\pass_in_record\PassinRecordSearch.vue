<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.in_type" style="width: 100%" placeholder="入场类型">
        <el-option v-for="item in inTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker v-model="form.queryParams.start_time" type="datetime" style="width: 45%" placeholder="入场开始时间"
        format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
      -
      <el-date-picker v-model="form.queryParams.end_time" type="datetime" style="width: 45%" placeholder="入场结束时间"
        format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" />
    </form-search-item>
  </FormSearch>
</template>

<script name="PassinRecordSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { getDefaultEndDateTime, getDefaultStartDateTime } from '@/utils/common';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    plate_no: undefined,
    in_type: undefined,
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  },
  dateRange: []
});

onMounted(() => {
  form.queryParams.start_time = getDefaultStartDateTime(7);
  form.queryParams.end_time = getDefaultEndDateTime(7);
  handleDataSearch();
});
const inTypeList = ref([
  { value: 1, key: '正常入场' },
  { value: 5, key: '手动补录' },
  { value: 6, key: '批量导入' },
  { value: 7, key: '跟车入场' },
  { value: 8, key: '断网补录入场' },
  { value: 9, key: '折返入场' },
  { value: 10, key: '手动匹配' },
  { value: 11, key: '模拟入场' }
])
const handleDataSearch = () => {
  console.log(form.queryParams.start_time);
  if (form.queryParams.start_time == null || form.queryParams.end_time == null) {
    ElMessage({
      message: '请选择入场时间',
      type: 'error'
    });
  } else {
    const param = {
      plate_no: form.queryParams.plate_no,
      in_type: form.queryParams.in_type,
      start_time: form.queryParams.start_time,
      end_time: form.queryParams.end_time,
      page: 1,
      limit: 30
    };
    const query = Object.assign(param, { page: 1, limit: 30 });
    emits('form-search', query);
  }
};
const handleAllReset = () => {
  form.queryParams = {
    plate_no: undefined,
    in_type: undefined,
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  };
  const param = {
    plate_no: form.queryParams.plate_no,
    in_type: form.queryParams.in_type,
    start_time: form.queryParams.start_time,
    end_time: form.queryParams.end_time,
    page: 1,
    limit: 30
  };
  emits('form-reset', param);
};
</script>
<style lang="scss" scoped></style>
