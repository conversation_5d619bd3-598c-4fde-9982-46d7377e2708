import * as mockCar from '@/api/watch/MockCar';

/**
 * @description 模拟车辆
 * <AUTHOR>
 * @date 2023/1/9
 */
export default {
  /**
   * 查询通道数据
   */
  querySentryGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        mockCar.querySentryGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询通道设备数据
   */
  queryGatewayDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        mockCar.queryGatewayDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 模拟入场
   */
  mockCarIn(data) {
    return new Promise((resolve, reject) => {
      try {
        mockCar.mockCarIn(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 模拟出场
   */
  mockCarOut(data) {
    return new Promise((resolve, reject) => {
      try {
        mockCar.mockCarOut(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
