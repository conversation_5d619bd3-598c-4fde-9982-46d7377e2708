<template>
  <div class="container">
    <intercept-rule-search @form-search="searchInterceptRule" @form-reset="resetParamsAndData" />
    <intercept-rule-table ref="table" />  
  </div>
</template>

<script name="InterceptRule" setup>
import InterceptRuleSearch from './intercept_rule/InterceptRuleSearch.vue';
import InterceptRuleTable from './intercept_rule/InterceptRuleTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchInterceptRule = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
<style lang="scss" scoped></style>
