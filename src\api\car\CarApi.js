import $ from '@/utils/axios';

/**
 * 车辆查询
 * @param {*} data
 * @returns
 */

//查询在场（长期滞留）车辆
export const pageCarInRecordNotOut = (data) => {
  return $({
    url: '/sentry/watch/pageCarInRecordNotOut',
    method: 'post',
    data
  });
};

//查询无牌车辆
export const pageCarInRecordNoPlate = (data) => {
  return $({
    url: '/sentry/watch/pageCarInRecordNoPlate',
    method: 'post',
    data
  });
};

//查询一户多车关联情况
export const getCarBindInfo = (data) => {
  return $({
    url: '/sentry/car/getCarBindInfo',
    method: 'post',
    data
  });
};
