<template>
  <div class="container">
    <sys-log-search @form-search="searchSysLog" @form-reset="resetParamsAndData" />
    <sys-log-table ref="table" />  
  </div>
</template>

<script name="SysLog" setup>
import SysLogSearch from './sys_log/SysLogSearch.vue';
import SysLogTable from './sys_log/SysLogTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchSysLog = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
<style lang="scss" scoped></style>
