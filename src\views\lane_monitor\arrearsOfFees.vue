<template>
  <div class="container">
    <el-tabs v-model="activeName" type="border-card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="折返事件" name="1">
        <event-search @form-search="searchEvent" @form-reset="resetParamsAndData" @export="handleExport" />
        <event-table ref="table" />
      </el-tab-pane>
      <el-tab-pane label="跟车事件" name="2">
        <event-search2 @form-search="searchEvent2" @form-reset="resetParamsAndData2" @export="handleExport2" />
        <event-table2 ref="table2" />
      </el-tab-pane>
    </el-tabs>


  </div>
</template>

<script setup>
import ArrearsOfFeesApi from '@/api/arrears-of-fees/arrearsOfFees';
import { onMounted, ref } from 'vue';
import eventSearch from './components/arrearsOfFeesSearch.vue';
import eventSearch2 from './components/arrearsOfFeesSearch2.vue';
import eventTable from './components/arrearsOfFeesTable.vue';
import eventTable2 from './components/arrearsOfFeesTable2.vue';
const table = ref(null);
const searchEvent = (queryParams) => {
  table.value.getList(queryParams, true);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams, true);
};
const handleExport = (type) => {
  if (type === 'image') {
    table.value.exportImage();
  } else {
    table.value.exportList();
  }
};

const table2 = ref(null);

const searchEvent2 = (queryParams) => {
  table2.value.getList(queryParams, true);
};
const resetParamsAndData2 = (queryParams) => {
  table2.value.getList(queryParams, true);
};
const handleExport2 = (type) => {
  if (type === 'image') {
    table2.value.exportImage();
  } else {
    table2.value.exportList();
  }
};
const activeName = ref('1')
const handleClick = (tab, event) => {
  console.log(tab.index)
  if (tab.index == 0) {
    getTopnum(84)
  } else {
    getTopnum(85)
  }
  activeName.value = Number(tab.index) + 1
}
const tableTop = ref({
  all: 0,
  today: 0,
  yesterday: 0,
  last7days: 0

})
const getTopnum = async (eventTypeId) => {
  await ArrearsOfFeesApi.monitorcount({ startTime: '', endTime: '', eventTypeId: eventTypeId }).then((res) => {
    tableTop.value.all = res.data
  })
  await ArrearsOfFeesApi.monitorcount({
    ...getDateRange('today'),
    eventTypeId: eventTypeId
  }).then((res) => {
    tableTop.value.today = res.data
  })
  await ArrearsOfFeesApi.monitorcount({ ...getDateRange('yesterday'), eventTypeId: eventTypeId }).then((res) => {
    tableTop.value.yesterday = res.data
  })
  await ArrearsOfFeesApi.monitorcount({ ...getDateRange('last7days'), eventTypeId: eventTypeId }).then((res) => {
    tableTop.value.last7days = res.data
    table.value.tableTop = tableTop.value
    table2.value.tableTop = tableTop.value
  })
}
onMounted(async () => {
  console.log(getDateRange('today'), getDateRange('yesterday'), getDateRange('last7days'))

  getTopnum(84)
})
// utils/dateUtils.js
const getDateRange = (type) => {
  const today = new Date(); // 根据题目要求固定为2025-05-19
  let startDate, endDate;

  switch (type) {
    case 'today':
      startDate = new Date(today);
      endDate = new Date(today);
      break;

    case 'yesterday':
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      startDate = yesterday;
      endDate = yesterday;
      break;

    case 'last7days':
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6); // 包括今天共7天
      startDate = sevenDaysAgo;
      endDate = new Date(today);
      break;

    default:
      throw new Error('Invalid date range type');
  }

  // 格式化为 YYYY-MM-DD
  return {
    startTime: formatDate(startDate),
    endTime: formatDate(endDate)
  };
}

const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
