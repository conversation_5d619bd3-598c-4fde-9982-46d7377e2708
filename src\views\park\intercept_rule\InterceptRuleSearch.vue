<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="规则名称" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.states" placeholder="是否生效" multiple clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="InterceptRuleSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { reactive, onMounted, ref } from 'vue';

const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    states: [],
    plate_no: ''
  }
});
const states = ref([]);

onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumBlackWhiteListState' }];
  commonService.findEnums('park', param).then((response) => {
    states.value = response.data.states;
  });
};

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams);
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    states: [],
    plate_no: ''
  };
  emits('form-reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
