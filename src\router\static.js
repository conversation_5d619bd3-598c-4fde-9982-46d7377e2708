const staticRoutes = [
  {
    // 登录页
    path: '/login',
    name: '登录',
    meta: {
      title: '登录'
    },
    component: () => import('@/views/Login.vue')
  },
  {
    // 404
    path: '/404',
    name: 'notFound',
    meta: {
      title: '网页走丢了'
    },
    component: () => import('@/views/error/404.vue')
  }
];

const baseRoute = {
  path: '/',
  name: '首页',
  component: () => import('@/views/Home.vue'),
  children: []
};

staticRoutes.push(baseRoute);

export { staticRoutes, baseRoute };
