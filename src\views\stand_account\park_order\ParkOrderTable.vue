<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="park_name" label="车场名" align="center" min-width="150" />
        <el-table-column prop="plate_no" label="车牌号" align="center" min-width="150" />
        <el-table-column prop="order_no" label="订单号" align="center" min-width="160" />
        <el-table-column prop="car_in_biz_no" label="入场流水号" align="center" min-width="160" />
        <el-table-column prop="order_state_desc" label="订单状态" align="center" min-width="100" />
        <el-table-column prop="pay_state_desc" label="付款状态" align="center" min-width="100" />
        <el-table-column prop="pay_method_desc" label="付款方式" align="center" min-width="100" />
        <el-table-column prop="refund_state_desc" label="退款状态" align="center" min-width="100" />
        <el-table-column prop="in_gateway_name" label="入场通道名" align="center" min-width="110" />
        <el-table-column prop="out_gateway_name" label="出场通道名" align="center" min-width="110" />
        <el-table-column prop="in_time" label="入场时间" align="center" min-width="160" />
        <el-table-column prop="to_time" label="出场时间" align="center" min-width="160" />
        <el-table-column prop="order_money" label="停车总金额" align="center" min-width="110" />
        <el-table-column prop="payed_money" label="停车已支付金额" align="center" min-width="130" />
        <el-table-column prop="order_type_desc" label="订单类型" align="center" min-width="100" />
        <el-table-column prop="should_pay_money" label="未收金额" align="center" min-width="110" />
        <el-table-column prop="current_coupon_money" label="抵扣金额" align="center" min-width="100" />
        <el-table-column prop="pay_time" label="付款时间" align="center" min-width="160" />
        <el-table-column prop="charge_name" label="收费员" align="center" min-width="100" />
        <el-table-column prop="car_type_desc" label="车型" align="center" min-width="100" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ParkOrderTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import stantAccountService from '@/service/stand_account/StantAccountService';
import { getDefaultDateTimeRange } from '@/utils/common';

const loading = ref(false);
const tableData = ref([]);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    start_time: '',
    end_time: ''
  },
  dateRange: []
});
onMounted(() => {
  data.dateRange = getDefaultDateTimeRange(7);
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.start_time === '' ? (params.start_time = data.dateRange[0]) : (params.start_time = data.queryParams.start_time);
  params.end_time === '' ? (params.end_time = data.dateRange[1]) : (params.end_time = data.queryParams.end_time);
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  stantAccountService.pageParkOrder(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
