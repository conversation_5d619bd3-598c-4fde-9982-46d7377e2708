<template>
    <el-card class="table" shadow="never">
        <div ref="table">
            <el-table :data="tableData" v-loading="loading" border>
                <el-table-column type="selection" style="text-align: center" width="40" />
                <el-table-column prop="action" label="操作" align="center" width="160">
                    <template v-slot="scope">
                        <el-button link @click="handelEditPlateNo(scope.row)" type="primary"> 修改车牌号 </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="id" label="ID" align="center" />
                <el-table-column prop="car_in_biz_no" label="入场业务流水号" align="center" />
                <el-table-column prop="park_name" label="车场名" align="center" />
                <el-table-column prop="park_region_name" label="区域名称" align="center" />
                <el-table-column prop="gateway_name" label="通道名称" align="center" />
                <el-table-column prop="in_time" label="入场时间" align="center" />
                <el-table-column prop="plate_no" label="车牌号" align="center" />
                <el-table-column prop="car_type_desc" label="车辆类型" align="center" />
                <el-table-column prop="car_color" label="车辆颜色" align="center" />\
                <el-table-column prop="out_state_desc" label="出场状态" align="center" />
                <el-table-column label="入场图片" align="center">
                    <template v-slot="scope">
                        <el-image
                                v-if="scope.row.car_photo_url !== '' && scope.row.car_photo_url !== null && scope.row.car_photo_url !== undefined"
                                style="width: 100px; height: 100px"
                                :src="scope.row.car_photo_url"
                                :fit="fit"
                                @click="showImage(scope.row.car_photo_url)"
                        />
                    </template>
                </el-table-column>
                <el-table-column prop="in_type_desc" label="入场类型" align="center" />
            </el-table>
            <el-pagination
                    background
                    :current-page="data.queryParams.page"
                    :page-sizes="[10, 30, 50, 100]"
                    :page-size="data.queryParams.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    class="table-pagination"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
            />
        </div>

        <!-- 修改车牌号 -->
        <el-dialog title="修改车牌号" v-model="dialogVisible" :close-on-click-modal="false" width="500px">
            <el-form :model="data.editPlateNoForm" label-position="top">
                <el-form-item label="车牌号" class="required">
                    <el-row :gutter="10">
                        <el-col :span="6">
                            <el-select v-model="data.editPlateNoForm.plateNoFirst" filterable style="width: 100%" disabled v-if="data.noPlateCarFlag == '1'">
                                <el-option v-for="item in plateNoFirsts" :key="item.label" :label="item.label" :value="item.label" />
                            </el-select>
                            <el-select v-else v-model="data.editPlateNoForm.plateNoFirst" filterable style="width: 100%">
                                <el-option v-for="item in plateNoFirsts" :key="item.label" :label="item.label" :value="item.label" />
                            </el-select>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                    type="text"
                                    v-model="data.editPlateNoForm.plateNoSecond"
                                    show-word-limit
                                    maxlength="10"
                                    style="width: 100%"
                                    disabled
                                    v-if="data.noPlateCarFlag == '1'"
                            />
                            <el-input type="text" v-model="data.editPlateNoForm.plateNoSecond" show-word-limit maxlength="10" style="width: 100%" v-else />
                        </el-col>
                        <el-col :span="6">
                            <el-select v-model="data.editPlateNoForm.plateNoThirdly" filterable style="width: 100%" disabled v-if="data.noPlateCarFlag == '1'">
                                <el-option v-for="item in plateNoThirdlies" :key="item.label" :label="item.label" :value="item.label" />
                            </el-select>
                            <el-select v-model="data.editPlateNoForm.plateNoThirdly" filterable style="width: 100%" v-else>
                                <el-option v-for="item in plateNoThirdlies" :key="item.label" :label="item.label" :value="item.label" />
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <el-checkbox
                                    v-model="data.noPlateCarFlag"
                                    true-label="1"
                                    false-label="0"
                                    @change="changeToNoPlate"
                                    style="display: inline; margin-top: 10px"
                            >
                                无牌车
                            </el-checkbox>
                        </el-col>
                    </el-row>
                </el-form-item>
            </el-form>
            <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPlateNo()">重 置</el-button>
          <el-button type="primary" @click="savePlateNo()">确 定</el-button>
        </span>
            </template>
        </el-dialog>

        <el-dialog v-model="imageDialogVisible" title="入场图片">
            <img w-full :src="data.dialogImageUrl" style="max-width: 100%; max-height: 100%" alt="Preview Image" />
        </el-dialog>
    </el-card>
</template>

<script name="ParkInCarTable" setup>
    import { reactive, ref, onMounted } from 'vue';
    import { ElMessage } from 'element-plus';
    import carService from '@/service/car/CarService';
    import watchService from '@/service/watch/WatchService';

    const tableData = ref([]);
    const loading = ref(false);
    const total = ref(0);
    const dialogVisible = ref(false);
    const imageDialogVisible = ref(false);

    const plateNoFirsts = ref([
        { label: '京' },
        { label: '冀' },
        { label: '晋' },
        { label: '蒙' },
        { label: '辽' },
        { label: '吉' },
        { label: '黑' },
        { label: '沪' },
        { label: '苏' },
        { label: '浙' },
        { label: '皖' },
        { label: '闽' },
        { label: '赣' },
        { label: '鲁' },
        { label: '豫' },
        { label: '鄂' },
        { label: '湘' },
        { label: '粤' },
        { label: '桂' },
        { label: '琼' },
        { label: '渝' },
        { label: '川' },
        { label: '贵' },
        { label: '云' },
        { label: '藏' },
        { label: '陕' },
        { label: '甘' },
        { label: '青' },
        { label: '宁' },
        { label: '新' },
        { label: '民航' },
        { label: '使' },
        { label: '无' }
    ]);
    const plateNoThirdlies = ref([
        { label: '警' },
        { label: '学' },
        { label: '使' },
        { label: '领' },
        { label: '挂' },
        { label: '应急' },
        { label: '无' }
    ]);
    const data = reactive({
        queryParams: {
            page: 1,
            limit: 30,
            start_time: '',
            end_time: '',
            day_cnt: 0
        },
        dateRange: [],
        editPlateNoForm: {
            id: undefined,
            gateway_id: undefined,
            plateNoFirst: undefined,
            plateNoSecond: undefined,
            plateNoThirdly: undefined
        },
        noPlateCarFlag: '0',
        dialogImageUrl: ''
    });

    const getList = (params) => {
        loading.value = true;
        data.queryParams.start_time = params.start_time;
        data.queryParams.end_time = params.end_time;
        params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
        params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
        data.queryParams = params;
        carService.pageCarInRecordNotOut(params).then((response) => {
            if (response.success === true) {
                tableData.value = response.data.rows;
                total.value = parseInt(response.data.total);
                loading.value = false;
            } else {
                ElMessage({
                    message: response.detail_message != '' ? response.detail_message : response.message,
                    type: 'error'
                });
                loading.value = false;
            }
        });
    };

    const handleSizeChange = (val) => {
        data.queryParams.limit = val;
        getList(data.queryParams);
    };
    const handleCurrentChange = (val) => {
        data.queryParams.page = val;
        getList(data.queryParams);
    };

    const resetPlateNo = () => {
        data.editPlateNoForm = {
            id: undefined,
            gateway_id: undefined,
            plateNoFirst: undefined,
            plateNoSecond: undefined,
            plateNoThirdly: undefined
        };
        data.noPlateCarFlag = '0';
    };
    const changeToNoPlate = (val) => {
        if (val == '1') {
            data.editPlateNoForm = {
                id: undefined,
                gateway_id: undefined,
                plateNoFirst: undefined,
                plateNoSecond: undefined,
                plateNoThirdly: undefined
            };
        }
    };
    const handelEditPlateNo = (val) => {
        data.editPlateNoForm.id = val.id;
        data.editPlateNoForm.gateway_id = val.gateway_id;
        //普通
        var c_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNOPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
        //特种
        var ts_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{4}[学挂领试超练警]{1}$/u;
        //武警
        var wj_reg = /^WJ[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]?[0-9A-Z]{5}$/iu;
        //军牌
        var j_reg = /^[QVKHBSLJNGCEZ]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
        //新能源
        // 小型车
        var xs_reg =
            /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[DF]{1}[1-9ABCDEFGHJKLMNPQRSTUVWXYZ]{1}[0-9]{4}$/u;

        // 大型车
        var xb_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9]{5}[DF]{1}$/u;
        //民航
        var mh_reg = /^民航[0-9A-Z]{5}$/u;
        //使馆
        var s_reg = /^[1-3]{1}[0-9]{2}[0-9A-Z]{3}使$/u;
        var s1_reg = /^使[0-9]{6}$/u;
        //领馆
        var l_reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[1-3]{1}[0-9]{2}[0-9A-Z]{2}领$/u;
        //应急
        var yj_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[0-9A-Z]{5}应急$/u;
        //判断并进行拆分
        if (c_reg.test(val.plate_no) || xs_reg.test(val.plate_no) || xb_reg.test(val.plate_no)) {
            data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
            data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1);
            data.editPlateNoForm.plateNoThirdly = '无';
        }
        if (ts_reg.test(val.plate_no)) {
            data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
            data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1, val.plate_no.length - 1);
            data.editPlateNoForm.plateNoThirdly = val.plate_no.substring(val.plate_no.length - 1);
        }
        if (wj_reg.test(val.plate_no) || j_reg.test(val.plate_no)) {
            data.editPlateNoForm.plateNoFirst = '无';
            data.editPlateNoForm.plateNoSecond = val.plate_no;
            data.editPlateNoForm.plateNoThirdly = '无';
        }
        if (mh_reg.test(val.plate_no)) {
            data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 2);
            data.editPlateNoForm.plateNoSecond = val.plate_no.substring(2);
            data.editPlateNoForm.plateNoThirdly = '无';
        }
        if (s_reg.test(val.plate_no)) {
            data.editPlateNoForm.plateNoFirst = '无';
            data.editPlateNoForm.plateNoSecond = val.plate_no.substring(0, val.plate_no.length - 1);
            data.editPlateNoForm.plateNoThirdly = val.plate_no.substring(val.plate_no.length - 1);
        }
        if (s1_reg.test(val.plate_no)) {
            data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
            data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1);
            data.editPlateNoForm.plateNoThirdly = '无';
        }
        if (l_reg.test(val.plate_no)) {
            data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
            data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1, val.plate_no.length - 1);
            data.editPlateNoForm.plateNoThirdly = val.plate_no.substring(val.plate_no.length - 1);
        }
        if (yj_reg.test(val.plate_no)) {
            data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
            data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1, val.plate_no.length - 2);
            data.editPlateNoForm.plateNoThirdly = val.plate_no.substring(val.plate_no.length - 2);
        }
        data.noPlateCarFlag = '0';
        dialogVisible.value = true;
    };

    const savePlateNo = () => {
        if (data.noPlateCarFlag == '0') {
            if (data.editPlateNoForm.plateNoFirst == undefined) {
                ElMessage({
                    message: '请输入车牌号！',
                    type: 'error'
                });
                return;
            }

            if (data.editPlateNoForm.plateNoSecond == undefined) {
                ElMessage({
                    message: '请输入车牌号！',
                    type: 'error'
                });
                return;
            }

            if (data.editPlateNoForm.plateNoThirdly == undefined) {
                ElMessage({
                    message: '请输入车牌号！',
                    type: 'error'
                });
                return;
            }
            var regex = /^[0-9A-Z]+$/;
            if (!regex.test(data.editPlateNoForm.plateNoSecond)) {
                ElMessage({
                    message: '请输入正确的车牌号！',
                    type: 'error'
                });
                return;
            }

            const param = {
                first_no: data.editPlateNoForm.plateNoFirst,
                mid_no: data.editPlateNoForm.plateNoSecond,
                last_no: data.editPlateNoForm.plateNoThirdly,
                id: data.editPlateNoForm.id,
                no_plate_car: 0,
                gateway_id: data.editPlateNoForm.gateway_id
            };
            watchService.modifyPlateNo(param).then((res) => {
                if (res.success) {
                    ElMessage({
                        type: 'success',
                        message: '修改车牌号成功'
                    });
                    getList(data.queryParams);
                    resetPlateNo();
                    dialogVisible.value = false;
                } else {
                    ElMessage({
                        message: response.message,
                        type: 'error'
                    });
                }
            });
        } else {
            //无牌车
            const plateParam = {
                park_id: state.lastParkInRecord.park_id
            };
            watchService.generateCarNoPlateNo(plateParam).then((res) => {
                if (res.success) {
                    updatePlateNo(res.data.plate_no);
                } else {
                    ElMessage({
                        message: response.message,
                        type: 'error'
                    });
                }
            });
        }
    };
    const updatePlateNo = (plate_no) => {
        const param = {
            first_no: data.editPlateNoForm.plateNoFirst,
            mid_no: plate_no,
            last_no: data.editPlateNoForm.plateNoThirdly,
            id: data.editPlateNoForm.id,
            no_plate_car: 1,
            gateway_id: data.editPlateNoForm.gateway_id
        };
        watchService.modifyPlateNo(param).then((res) => {
            if (res.success) {
                ElMessage({
                    type: 'success',
                    message: '修改车牌号成功'
                });
                getList(data.queryParams);
                resetPlateNo();
                dialogVisible.value = false;
            } else {
                ElMessage({
                    message: res.message,
                    type: 'error'
                });
            }
        });
    };
    const showImage = (val) => {
        data.dialogImageUrl = val;
        imageDialogVisible.value = true;
    };
    defineExpose({
        getList
    });
</script>
<style lang="scss" scoped>
  :deep(.required .el-form-item__label::before) {
    padding-right: 5px;
    content: '*';
    color: #f5222d;
  }
</style>
