<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.queryParams.start_time"
        type="datetime"
        style="width: 45%"
        placeholder="入场开始时间"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
      -
      <el-date-picker
        v-model="form.queryParams.end_time"
        type="datetime"
        style="width: 45%"
        placeholder="入场结束时间"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.pay_state" placeholder="付款状态" clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="ParkOrderSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onMounted } from 'vue';
import { getDefaultStartDateTime, getDefaultEndDateTime } from '@/utils/common';

const emits = defineEmits(['form-search']);
const states = ref([]);
const form = reactive({
  queryParams: {
    plate_no: undefined,
    start_time: '',
    end_time: '',
    pay_state: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

onMounted(() => {
  // 数据初始化
  initSelects();
  form.queryParams.start_time = getDefaultStartDateTime(7);
  form.queryParams.end_time = getDefaultEndDateTime(7);
});

const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumPayState' }];
  commonService.findEnums('order', param).then((response) => {
    states.value = response.data.states;
  });
};

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    plate_no: undefined,
    start_time: '',
    end_time: '',
    pay_state: undefined,
    page: 1,
    limit: 30
  };
  emits('form-reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
