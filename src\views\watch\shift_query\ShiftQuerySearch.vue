<template>
  <FormSearch @search="handleSearch" @reset="handleReset">
    <form-search-item>
      <el-date-picker
        v-model="state.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </form-search-item>
  </FormSearch>
</template>

<script name="ShiftQuerySearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useUser } from '@/stores/user';
import { getDefaultDateRange } from '@/utils/common';

const emits = defineEmits(['form-search']);

const user = useUser();

const state = reactive({
  dateRange: [],
  query: {
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  }
});

onMounted(() => {
  state.dateRange = getDefaultDateRange(7);
  handleSearch();
});

const handleSearch = () => {
  if (undefined !== state.dateRange && state.dateRange.length > 0) {
    state.query.start_time = state.dateRange[0];
    state.query.end_time = state.dateRange[1];
  }
  if (state.dateRange === null) {
    state.query.start_time = undefined;
    state.query.end_time = undefined;
  }

  const query = Object.assign(state.query, { page: 1, limit: 30 });
  emits('form-search', query);
};

const handleReset = () => {
  emits('form-search', resetQuery());
};

const resetQuery = () => {
  state.dateRange = [];
  return (state.query = {
    park_id: user.park_id,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  });
};
</script>
<style lang="scss" scoped></style>
