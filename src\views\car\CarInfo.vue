<template>
  <div class="container">
    <car-info-search @form-search="searchCarInfo" @form-reset="resetParamsAndData" />
    <car-info-table ref="table" />  
  </div>
</template>

<script name="CarInfo" setup>
import CarInfoSearch from './car_info/CarInfoSearch.vue';
import CarInfoTable from './car_info/CarInfoTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchCarInfo = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>

<style lang="scss" scoped></style>
