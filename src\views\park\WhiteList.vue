<template>
  <div class="container">
    <white-list-search @form-search="searchWhiteList" @form-reset="resetParamsAndData" />
    <white-list-table ref="table" />  
  </div>
</template>

<script name="WhiteList" setup>
import WhiteListSearch from './white_list/WhiteListSearch.vue';
import WhiteListTable from './white_list/WhiteListTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchWhiteList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>

<style lang="scss" scoped></style>
