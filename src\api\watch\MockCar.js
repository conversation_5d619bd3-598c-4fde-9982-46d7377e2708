import $ from '@/utils/axios';

// 查询通道数据
export const querySentryGateway = (data) => {
  return $({
    url: '/sentry/watch/querySentryGateway',
    method: 'post',
    data
  });
};

// 查询通道设备数据
export const queryGatewayDevice = (gateway_id) => {
  return $({
    url: '/sentry/watch/queryGatewayDevice?gatewayId=' + gateway_id,
    method: 'get'
  });
};

// 模拟入场
export const mockCarIn = (data) => {
  return $({
    url: '/mock/mockCarIn',
    method: 'post',
    data
  });
};

// 模拟出场
export const mockCarOut = (data) => {
  return $({
    url: '/mock/mockCarOut',
    method: 'post',
    data
  });
};
