const { Menu } = require('electron');

const menuConfig = [
  {
    label: '退出',
    role: 'quit'
  },
  {
    label: '编辑',
    submenu: [
      {
        label: '撤销',
        role: 'undo'
      },
      {
        label: '恢复',
        role: 'redo'
      },
      {
        type: 'separator'
      },
      {
        label: '剪切',
        role: 'cut'
      },
      {
        label: '复制',
        role: 'copy'
      },
      {
        label: '粘贴',
        role: 'paste'
      },
      {
        type: 'separator'
      },
      {
        label: '全选',
        role: 'selectAll'
      },
      {
        label: '删除',
        role: 'delete'
      }
    ]
  },
  {
    label: '查看',
    submenu: [
      {
        label: '放大',
        role: 'zoomin'
      },
      {
        label: '缩小',
        role: 'zoomout'
      },
      {
        label: '重置',
        role: 'resetzoom'
      }
    ]
  },
  {
    label: '窗口',
    submenu: [
      {
        label: '重新加载',
        role: 'reload'
      },
      {
        label: '强制重新加载',
        role: 'forcereload'
      },
      {
        type: 'separator'
      },
      {
        label: '全屏',
        role: 'togglefullscreen'
      },
      {
        type: 'separator'
      },
      {
        label: '最小化',
        role: 'minimize'
      },
      {
        label: '关闭',
        role: 'close'
      },
      {
        type: 'separator'
      },
      {
        label: '开发者工具',
        role: 'toggledevtools'
      }
    ]
  },
  {
    label: '关于',
    role: 'about'
  }
];

const menu = Menu.buildFromTemplate(menuConfig);

Menu.setApplicationMenu(menu);
