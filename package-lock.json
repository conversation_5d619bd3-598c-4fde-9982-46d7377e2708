{"name": "parking-client-ui", "version": "1.6.5", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "parking-client-ui", "version": "1.6.5", "dependencies": {"@element-plus/icons-vue": "^2.0.9", "axios": "^0.25.0", "big.js": "^6.2.2", "electron-log": "^4.4.8", "electron-updater": "^5.3.0", "element-plus": "^2.2.26", "lodash": "^4.17.21", "mitt": "^3.0.0", "mockjs": "^1.1.0", "node-rtsp-stream": "^0.0.9", "nprogress": "^0.2.0", "pinia": "^2.0.17", "pinia-plugin-persistedstate": "^2.1.1", "uuid": "^9.0.0", "vue": "^3.2.37", "vue-router": "^4.1.3", "vue3-video-play": "^1.3.1-beta.6"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@vitejs/plugin-vue": "^3.0.1", "@vue/eslint-config-prettier": "^7.0.0", "@vueuse/electron": "^9.5.0", "concurrently": "^7.3.0", "cross-env": "^7.0.3", "electron": "^20.3.12", "electron-builder": "^23.6.0", "eslint": "^8.21.0", "eslint-plugin-vue": "^9.3.0", "jsmpeg-player": "^3.0.3", "prettier": "^2.7.1", "sass": "^1.54.8", "vite": "^3.1.6", "vite-plugin-electron": "^0.10.4", "wait-on": "^6.0.1"}}, "node_modules/@babel/helper-string-parser": {"version": "7.24.8", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.24.7", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.25.6", "license": "MIT", "dependencies": {"@babel/types": "^7.25.6"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/runtime": {"version": "7.26.9", "resolved": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.26.9.tgz", "integrity": "sha512-aA63XwOkcl4xxQa3HjPMqOP6LiK0ZDv3mUPYEFXkpHbaFjtGggE1A61FjFzJnB+p7/oy2gA8E+rcBNl/zC1tMg==", "dev": true, "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.25.6", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "to-fast-properties": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@ctrl/tinycolor": {"version": "3.6.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@cycjimmy/awesome-js-funcs": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/@cycjimmy/sass-lib": {"version": "0.1.0", "dev": true, "license": "MIT"}, "node_modules/@develar/schema-utils": {"version": "2.6.5", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/@electron/get": {"version": "1.14.1", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "env-paths": "^2.2.0", "fs-extra": "^8.1.0", "got": "^9.6.0", "progress": "^2.0.3", "semver": "^6.2.0", "sumchecker": "^3.0.1"}, "engines": {"node": ">=8.6"}, "optionalDependencies": {"global-agent": "^3.0.0", "global-tunnel-ng": "^2.7.1"}}, "node_modules/@electron/universal": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"@malept/cross-spawn-promise": "^1.1.0", "asar": "^3.1.0", "debug": "^4.3.1", "dir-compare": "^2.4.0", "fs-extra": "^9.0.1", "minimatch": "^3.0.4", "plist": "^3.0.4"}, "engines": {"node": ">=8.6"}}, "node_modules/@electron/universal/node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@electron/universal/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@electron/universal/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@element-plus/icons-vue": {"version": "2.3.1", "license": "MIT", "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.11.0", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/js": {"version": "8.57.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@floating-ui/core": {"version": "1.6.8", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.7"}}, "node_modules/@floating-ui/dom": {"version": "1.6.10", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.6.0", "@floating-ui/utils": "^0.2.7"}}, "node_modules/@floating-ui/utils": {"version": "0.2.7", "license": "MIT"}, "node_modules/@hapi/hoek": {"version": "9.3.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hapi/topo": {"version": "5.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.11.14", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.2", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@malept/cross-spawn-promise": {"version": "1.1.1", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/malept"}, {"type": "tidelift", "url": "https://tidelift.com/subscription/pkg/npm-.malept-cross-spawn-promise?utm_medium=referral&utm_source=npm_fund"}], "license": "Apache-2.0", "dependencies": {"cross-spawn": "^7.0.1"}, "engines": {"node": ">= 10"}}, "node_modules/@malept/flatpak-bundler": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "fs-extra": "^9.0.0", "lodash": "^4.17.15", "tmp-promise": "^3.0.2"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@malept/flatpak-bundler/node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@malept/flatpak-bundler/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@malept/flatpak-bundler/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@popperjs/core": {"name": "@sxzz/popperjs-es", "version": "2.11.7", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@rushstack/eslint-patch": {"version": "1.10.4", "dev": true, "license": "MIT"}, "node_modules/@sideway/address": {"version": "4.1.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@sideway/formula": {"version": "3.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sideway/pinpoint": {"version": "2.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sindresorhus/is": {"version": "0.14.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@szmarczak/http-timer": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"defer-to-connect": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/@tootallnate/once": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/@types/debug": {"version": "4.1.12", "dev": true, "license": "MIT", "dependencies": {"@types/ms": "*"}}, "node_modules/@types/fs-extra": {"version": "9.0.13", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/glob": {"version": "7.2.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/minimatch": "*", "@types/node": "*"}}, "node_modules/@types/lodash": {"version": "4.17.7", "license": "MIT"}, "node_modules/@types/lodash-es": {"version": "4.17.12", "license": "MIT", "dependencies": {"@types/lodash": "*"}}, "node_modules/@types/minimatch": {"version": "5.1.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/@types/ms": {"version": "0.7.34", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "16.18.108", "dev": true, "license": "MIT"}, "node_modules/@types/semver": {"version": "7.5.8", "license": "MIT"}, "node_modules/@types/web-bluetooth": {"version": "0.0.16", "license": "MIT"}, "node_modules/@types/yargs": {"version": "17.0.33", "dev": true, "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "dev": true, "license": "MIT"}, "node_modules/@types/yauzl": {"version": "2.10.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*"}}, "node_modules/@ungap/structured-clone": {"version": "1.2.0", "dev": true, "license": "ISC"}, "node_modules/@vitejs/plugin-vue": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^3.0.0", "vue": "^3.2.25"}}, "node_modules/@vue/compiler-core": {"version": "3.5.4", "license": "MIT", "dependencies": {"@babel/parser": "^7.25.3", "@vue/shared": "3.5.4", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.0"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.4", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.4", "@vue/shared": "3.5.4"}}, "node_modules/@vue/compiler-sfc": {"version": "3.5.4", "license": "MIT", "dependencies": {"@babel/parser": "^7.25.3", "@vue/compiler-core": "3.5.4", "@vue/compiler-dom": "3.5.4", "@vue/compiler-ssr": "3.5.4", "@vue/shared": "3.5.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.11", "postcss": "^8.4.44", "source-map-js": "^1.2.0"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.4", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.4", "@vue/shared": "3.5.4"}}, "node_modules/@vue/devtools-api": {"version": "6.6.4", "license": "MIT"}, "node_modules/@vue/eslint-config-prettier": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"eslint": ">= 7.28.0", "prettier": ">= 2.0.0"}}, "node_modules/@vue/reactivity": {"version": "3.5.4", "license": "MIT", "dependencies": {"@vue/shared": "3.5.4"}}, "node_modules/@vue/runtime-core": {"version": "3.5.4", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.4", "@vue/shared": "3.5.4"}}, "node_modules/@vue/runtime-dom": {"version": "3.5.4", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.4", "@vue/runtime-core": "3.5.4", "@vue/shared": "3.5.4", "csstype": "^3.1.3"}}, "node_modules/@vue/server-renderer": {"version": "3.5.4", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.5.4", "@vue/shared": "3.5.4"}, "peerDependencies": {"vue": "3.5.4"}}, "node_modules/@vue/shared": {"version": "3.5.4", "license": "MIT"}, "node_modules/@vueuse/core": {"version": "9.13.0", "license": "MIT", "dependencies": {"@types/web-bluetooth": "^0.0.16", "@vueuse/metadata": "9.13.0", "@vueuse/shared": "9.13.0", "vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/core/node_modules/vue-demi": {"version": "0.14.10", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vueuse/electron": {"version": "9.13.0", "dev": true, "license": "MIT", "dependencies": {"@vueuse/shared": "9.13.0", "vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"electron": ">=9.0.0"}}, "node_modules/@vueuse/electron/node_modules/vue-demi": {"version": "0.14.10", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vueuse/metadata": {"version": "9.13.0", "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared": {"version": "9.13.0", "license": "MIT", "dependencies": {"vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared/node_modules/vue-demi": {"version": "0.14.10", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@xmldom/xmldom": {"version": "0.8.10", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/7zip-bin": {"version": "5.1.1", "dev": true, "license": "MIT"}, "node_modules/acorn": {"version": "8.12.1", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/agent-base": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-keywords": {"version": "3.5.2", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.3", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/app-builder-bin": {"version": "4.0.0", "dev": true, "license": "MIT"}, "node_modules/app-builder-lib": {"version": "23.6.0", "dev": true, "license": "MIT", "dependencies": {"@develar/schema-utils": "~2.6.5", "@electron/universal": "1.2.1", "@malept/flatpak-bundler": "^0.4.0", "7zip-bin": "~5.1.1", "async-exit-hook": "^2.0.1", "bluebird-lst": "^1.0.9", "builder-util": "23.6.0", "builder-util-runtime": "9.1.1", "chromium-pickle-js": "^0.2.0", "debug": "^4.3.4", "ejs": "^3.1.7", "electron-osx-sign": "^0.6.0", "electron-publish": "23.6.0", "form-data": "^4.0.0", "fs-extra": "^10.1.0", "hosted-git-info": "^4.1.0", "is-ci": "^3.0.0", "isbinaryfile": "^4.0.10", "js-yaml": "^4.1.0", "lazy-val": "^1.0.5", "minimatch": "^3.1.2", "read-config-file": "6.2.0", "sanitize-filename": "^1.6.3", "semver": "^7.3.7", "tar": "^6.1.11", "temp-file": "^3.4.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/app-builder-lib/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/app-builder-lib/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/app-builder-lib/node_modules/semver": {"version": "7.6.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/app-builder-lib/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/asar": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"chromium-pickle-js": "^0.2.0", "commander": "^5.0.0", "glob": "^7.1.6", "minimatch": "^3.0.4"}, "bin": {"asar": "bin/asar.js"}, "engines": {"node": ">=10.12.0"}, "optionalDependencies": {"@types/glob": "^7.1.1"}}, "node_modules/async": {"version": "3.2.6", "dev": true, "license": "MIT"}, "node_modules/async-exit-hook": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/async-validator": {"version": "4.2.5", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "dev": true, "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "dev": true, "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/axios": {"version": "0.25.0", "license": "MIT", "dependencies": {"follow-redirects": "^1.14.7"}}, "node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/big.js": {"version": "6.2.2", "resolved": "https://registry.npmmirror.com/big.js/-/big.js-6.2.2.tgz", "integrity": "sha512-y/ie+Faknx7sZA5MfGA2xKlu0GDv8RWrXGsmlteyJQ2lvoKv9GBK/fpRMc2qlSoBAgNxrixICFCBefIq8WCQpQ==", "engines": {"node": "*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/bigjs"}}, "node_modules/binary-extensions": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bluebird": {"version": "3.7.2", "dev": true, "license": "MIT"}, "node_modules/bluebird-lst": {"version": "1.0.9", "dev": true, "license": "MIT", "dependencies": {"bluebird": "^3.5.5"}}, "node_modules/boolbase": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/boolean": {"version": "3.2.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/buffer-alloc": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "node_modules/buffer-alloc-unsafe": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/buffer-crc32": {"version": "0.2.13", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/buffer-equal": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/buffer-fill": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/buffer-from": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/builder-util": {"version": "23.6.0", "dev": true, "license": "MIT", "dependencies": {"@types/debug": "^4.1.6", "@types/fs-extra": "^9.0.11", "7zip-bin": "~5.1.1", "app-builder-bin": "4.0.0", "bluebird-lst": "^1.0.9", "builder-util-runtime": "9.1.1", "chalk": "^4.1.1", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "fs-extra": "^10.0.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-ci": "^3.0.0", "js-yaml": "^4.1.0", "source-map-support": "^0.5.19", "stat-mode": "^1.0.0", "temp-file": "^3.4.0"}}, "node_modules/builder-util-runtime": {"version": "9.1.1", "license": "MIT", "dependencies": {"debug": "^4.3.4", "sax": "^1.2.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/builder-util/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/builder-util/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/builder-util/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/cacheable-request": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^3.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^4.1.0", "responselike": "^1.0.2"}, "engines": {"node": ">=8"}}, "node_modules/cacheable-request/node_modules/json-buffer": {"version": "3.0.0", "dev": true, "license": "MIT"}, "node_modules/cacheable-request/node_modules/keyv": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.0"}}, "node_modules/cacheable-request/node_modules/lowercase-keys": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/chokidar": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/chownr": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/chromium-pickle-js": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/ci-info": {"version": "3.9.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cliui": {"version": "8.0.1", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/clone-response": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/colors": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "5.1.0", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/compare-version": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/concat-map": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/concurrently": {"version": "7.6.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "date-fns": "^2.29.1", "lodash": "^4.17.21", "rxjs": "^7.0.0", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0", "tree-kill": "^1.2.2", "yargs": "^17.3.1"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "funding": {"url": "https://github.com/open-cli-tools/concurrently?sponsor=1"}}, "node_modules/config-chain": {"version": "1.1.13", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "node_modules/cross-env": {"version": "7.0.3", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.1"}, "bin": {"cross-env": "src/bin/cross-env.js", "cross-env-shell": "src/bin/cross-env-shell.js"}, "engines": {"node": ">=10.14", "npm": ">=6", "yarn": ">=1"}}, "node_modules/cross-spawn": {"version": "7.0.3", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cssesc": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "node_modules/date-fns": {"version": "2.30.0", "dev": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0"}, "engines": {"node": ">=0.11"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/date-fns"}}, "node_modules/dayjs": {"version": "1.11.13", "license": "MIT"}, "node_modules/debug": {"version": "4.3.7", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decompress-response": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/defer-to-connect": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/define-data-property": {"version": "1.1.4", "dev": true, "license": "MIT", "optional": true, "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/detect-node": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/dir-compare": {"version": "2.4.0", "dev": true, "license": "MIT", "dependencies": {"buffer-equal": "1.0.0", "colors": "1.0.3", "commander": "2.9.0", "minimatch": "3.0.4"}, "bin": {"dircompare": "src/cli/dircompare.js"}}, "node_modules/dir-compare/node_modules/commander": {"version": "2.9.0", "dev": true, "license": "MIT", "dependencies": {"graceful-readlink": ">= 1.0.0"}, "engines": {"node": ">= 0.6.x"}}, "node_modules/dir-compare/node_modules/minimatch": {"version": "3.0.4", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/dmg-builder": {"version": "23.6.0", "dev": true, "license": "MIT", "dependencies": {"app-builder-lib": "23.6.0", "builder-util": "23.6.0", "builder-util-runtime": "9.1.1", "fs-extra": "^10.0.0", "iconv-lite": "^0.6.2", "js-yaml": "^4.1.0"}, "optionalDependencies": {"dmg-license": "^1.0.11"}}, "node_modules/dmg-builder/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/dmg-builder/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/dmg-builder/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dotenv": {"version": "9.0.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/dotenv-expand": {"version": "5.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/duplexer3": {"version": "0.1.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ejs": {"version": "3.1.10", "dev": true, "license": "Apache-2.0", "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/electron": {"version": "20.3.12", "resolved": "https://registry.npmmirror.com/electron/-/electron-20.3.12.tgz", "integrity": "sha512-CuCZKhwdSXaUNIoEuPVZ25YH18zmox3wEM8Acwcy9tlpD8Jiuq3ji3RZ98eVWJalrpqbdE9LtTD/sLC86GIkLg==", "dev": true, "hasInstallScript": true, "dependencies": {"@electron/get": "^1.14.1", "@types/node": "^16.11.26", "extract-zip": "^2.0.1"}, "bin": {"electron": "cli.js"}, "engines": {"node": ">= 10.17.0"}}, "node_modules/electron-builder": {"version": "23.6.0", "dev": true, "license": "MIT", "dependencies": {"@types/yargs": "^17.0.1", "app-builder-lib": "23.6.0", "builder-util": "23.6.0", "builder-util-runtime": "9.1.1", "chalk": "^4.1.1", "dmg-builder": "23.6.0", "fs-extra": "^10.0.0", "is-ci": "^3.0.0", "lazy-val": "^1.0.5", "read-config-file": "6.2.0", "simple-update-notifier": "^1.0.7", "yargs": "^17.5.1"}, "bin": {"electron-builder": "cli.js", "install-app-deps": "install-app-deps.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/electron-builder/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/electron-builder/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/electron-builder/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/electron-log": {"version": "4.4.8", "license": "MIT"}, "node_modules/electron-osx-sign": {"version": "0.6.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"bluebird": "^3.5.0", "compare-version": "^0.1.2", "debug": "^2.6.8", "isbinaryfile": "^3.0.2", "minimist": "^1.2.0", "plist": "^3.0.1"}, "bin": {"electron-osx-flat": "bin/electron-osx-flat.js", "electron-osx-sign": "bin/electron-osx-sign.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/electron-osx-sign/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/electron-osx-sign/node_modules/isbinaryfile": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"buffer-alloc": "^1.2.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/electron-osx-sign/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/electron-publish": {"version": "23.6.0", "dev": true, "license": "MIT", "dependencies": {"@types/fs-extra": "^9.0.11", "builder-util": "23.6.0", "builder-util-runtime": "9.1.1", "chalk": "^4.1.1", "fs-extra": "^10.0.0", "lazy-val": "^1.0.5", "mime": "^2.5.2"}}, "node_modules/electron-publish/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/electron-publish/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/electron-publish/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/electron-updater": {"version": "5.3.0", "license": "MIT", "dependencies": {"@types/semver": "^7.3.6", "builder-util-runtime": "9.1.1", "fs-extra": "^10.0.0", "js-yaml": "^4.1.0", "lazy-val": "^1.0.5", "lodash.escaperegexp": "^4.1.2", "lodash.isequal": "^4.5.0", "semver": "^7.3.5", "typed-emitter": "^2.1.0"}}, "node_modules/electron-updater/node_modules/fs-extra": {"version": "10.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/electron-updater/node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/electron-updater/node_modules/semver": {"version": "7.6.3", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/electron-updater/node_modules/universalify": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/element-plus": {"version": "2.8.2", "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.4.1", "@element-plus/icons-vue": "^2.3.1", "@floating-ui/dom": "^1.0.1", "@popperjs/core": "npm:@sxzz/popperjs-es@^2.11.7", "@types/lodash": "^4.14.182", "@types/lodash-es": "^4.17.6", "@vueuse/core": "^9.1.0", "async-validator": "^4.2.5", "dayjs": "^1.11.3", "escape-html": "^1.0.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "memoize-one": "^6.0.0", "normalize-wheel-es": "^1.2.0"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.4", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/env-paths": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/es-define-property": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"get-intrinsic": "^1.2.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/es6-error": {"version": "4.1.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/esbuild": {"version": "0.15.18", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.15.18", "@esbuild/linux-loong64": "0.15.18", "esbuild-android-64": "0.15.18", "esbuild-android-arm64": "0.15.18", "esbuild-darwin-64": "0.15.18", "esbuild-darwin-arm64": "0.15.18", "esbuild-freebsd-64": "0.15.18", "esbuild-freebsd-arm64": "0.15.18", "esbuild-linux-32": "0.15.18", "esbuild-linux-64": "0.15.18", "esbuild-linux-arm": "0.15.18", "esbuild-linux-arm64": "0.15.18", "esbuild-linux-mips64le": "0.15.18", "esbuild-linux-ppc64le": "0.15.18", "esbuild-linux-riscv64": "0.15.18", "esbuild-linux-s390x": "0.15.18", "esbuild-netbsd-64": "0.15.18", "esbuild-openbsd-64": "0.15.18", "esbuild-sunos-64": "0.15.18", "esbuild-windows-32": "0.15.18", "esbuild-windows-64": "0.15.18", "esbuild-windows-arm64": "0.15.18"}}, "node_modules/esbuild-windows-64": {"version": "0.15.18", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/escalade": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "8.57.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.0", "@humanwhocodes/config-array": "^0.11.14", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-prettier": {"version": "8.10.0", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-plugin-prettier": {"version": "4.2.1", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"eslint": ">=7.28.0", "prettier": ">=2.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-plugin-vue": {"version": "9.28.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "globals": "^13.24.0", "natural-compare": "^1.4.0", "nth-check": "^2.1.1", "postcss-selector-parser": "^6.0.15", "semver": "^7.6.3", "vue-eslint-parser": "^9.4.3", "xml-name-validator": "^4.0.0"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0"}}, "node_modules/eslint-plugin-vue/node_modules/semver": {"version": "7.6.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/eslint-scope": {"version": "7.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree": {"version": "9.6.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/extract-zip": {"version": "2.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}, "bin": {"extract-zip": "cli.js"}, "engines": {"node": ">= 10.17.0"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.17.1", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fd-slicer": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"pend": "~1.2.0"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/filelist": {"version": "1.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/filelist/node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/filelist/node_modules/minimatch": {"version": "5.1.6", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/fill-range": {"version": "7.1.1", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.3.1", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/fs-extra": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-minipass": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-caller-file": {"version": "2.0.5", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.2.4", "dev": true, "license": "MIT", "optional": true, "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob": {"version": "7.2.3", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/global-agent": {"version": "3.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"boolean": "^3.0.1", "es6-error": "^4.1.1", "matcher": "^3.0.0", "roarr": "^2.15.3", "semver": "^7.3.2", "serialize-error": "^7.0.1"}, "engines": {"node": ">=10.0"}}, "node_modules/global-agent/node_modules/semver": {"version": "7.6.3", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/global-tunnel-ng": {"version": "2.7.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"encodeurl": "^1.0.2", "lodash": "^4.17.10", "npm-conf": "^1.1.3", "tunnel": "^0.0.6"}, "engines": {"node": ">=0.10"}}, "node_modules/globals": {"version": "13.24.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globalthis": {"version": "1.0.4", "dev": true, "license": "MIT", "optional": true, "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"get-intrinsic": "^1.1.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/got": {"version": "9.6.0", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/is": "^0.14.0", "@szmarczak/http-timer": "^1.1.2", "cacheable-request": "^6.0.0", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^4.1.0", "lowercase-keys": "^1.0.1", "mimic-response": "^1.0.1", "p-cancelable": "^1.0.0", "to-readable-stream": "^1.0.0", "url-parse-lax": "^3.0.0"}, "engines": {"node": ">=8.6"}}, "node_modules/got/node_modules/get-stream": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/graceful-readlink": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.3", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hls.js": {"version": "1.5.15", "license": "Apache-2.0"}, "node_modules/hosted-git-info": {"version": "4.1.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/http-cache-semantics": {"version": "4.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-proxy-agent": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.3.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/immutable": {"version": "4.3.7", "dev": true, "license": "MIT"}, "node_modules/import-fresh": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC", "optional": true}, "node_modules/is-binary-path": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-ci": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"ci-info": "^3.2.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.15.1", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/isbinaryfile": {"version": "4.0.10", "dev": true, "license": "MIT", "engines": {"node": ">= 8.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}}, "node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/jake": {"version": "10.9.2", "dev": true, "license": "Apache-2.0", "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/joi": {"version": "17.13.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsmpeg-player": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"@cycjimmy/awesome-js-funcs": "^1.0.1", "@cycjimmy/sass-lib": "^0.1.0"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "dev": true, "license": "ISC", "optional": true}, "node_modules/json5": {"version": "2.2.3", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "4.0.0", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/lazy-val": {"version": "1.0.5", "license": "MIT"}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash-unified": {"version": "1.0.3", "license": "MIT", "peerDependencies": {"@types/lodash-es": "*", "lodash": "*", "lodash-es": "*"}}, "node_modules/lodash.escaperegexp": {"version": "4.1.2", "license": "MIT"}, "node_modules/lodash.isequal": {"version": "4.5.0", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lowercase-keys": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/magic-string": {"version": "0.30.11", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/matcher": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"escape-string-regexp": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/memoize-one": {"version": "6.0.0", "license": "MIT"}, "node_modules/mime": {"version": "2.6.0", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mime-db": {"version": "1.52.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "dev": true, "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-response": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "5.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/mitt": {"version": "3.0.1", "license": "MIT"}, "node_modules/mkdirp": {"version": "1.0.4", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/mockjs": {"version": "1.1.0", "dependencies": {"commander": "*"}, "bin": {"random": "bin/random"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.7", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/node-rtsp-stream": {"version": "0.0.9", "license": "MIT", "dependencies": {"ws": "^7.0.0"}}, "node_modules/normalize-path": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "4.5.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/normalize-wheel-es": {"version": "1.2.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/npm-conf": {"version": "1.1.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"config-chain": "^1.1.11", "pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/nprogress": {"version": "0.2.0", "license": "MIT"}, "node_modules/nth-check": {"version": "2.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/object-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/optionator": {"version": "0.9.4", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-cancelable": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "dev": true, "license": "MIT"}, "node_modules/pend": {"version": "1.2.0", "dev": true, "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.0", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=4"}}, "node_modules/pinia": {"version": "2.2.2", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.3", "vue-demi": "^0.14.10"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"@vue/composition-api": "^1.4.0", "typescript": ">=4.4.4", "vue": "^2.6.14 || ^3.3.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "typescript": {"optional": true}}}, "node_modules/pinia-plugin-persistedstate": {"version": "2.4.0", "license": "MIT", "peerDependencies": {"pinia": "^2.0.0"}, "peerDependenciesMeta": {"pinia": {"optional": true}}}, "node_modules/pinia/node_modules/vue-demi": {"version": "0.14.10", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/plist": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}, "engines": {"node": ">=10.4.0"}}, "node_modules/postcss": {"version": "8.4.45", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prepend-http": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/prettier": {"version": "2.8.8", "dev": true, "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/progress": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/proto-list": {"version": "1.2.4", "dev": true, "license": "ISC", "optional": true}, "node_modules/pump": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/read-config-file": {"version": "6.2.0", "dev": true, "license": "MIT", "dependencies": {"dotenv": "^9.0.2", "dotenv-expand": "^5.1.0", "js-yaml": "^4.1.0", "json5": "^2.2.0", "lazy-val": "^1.0.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/readdirp": {"version": "3.6.0", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/regenerator-runtime": {"version": "0.14.1", "dev": true, "license": "MIT"}, "node_modules/require-directory": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/resolve": {"version": "1.22.8", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/responselike": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"lowercase-keys": "^1.0.0"}}, "node_modules/reusify": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/roarr": {"version": "2.15.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"boolean": "^3.0.1", "detect-node": "^2.0.4", "globalthis": "^1.0.1", "json-stringify-safe": "^5.0.1", "semver-compare": "^1.0.0", "sprintf-js": "^1.1.2"}, "engines": {"node": ">=8.0"}}, "node_modules/rollup": {"version": "2.79.1", "dev": true, "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=10.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.8.1", "devOptional": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/sanitize-filename": {"version": "1.6.3", "dev": true, "license": "WTFPL OR ISC", "dependencies": {"truncate-utf8-bytes": "^1.0.0"}}, "node_modules/sass": {"version": "1.78.0", "dev": true, "license": "MIT", "dependencies": {"chokidar": ">=3.0.0 <4.0.0", "immutable": "^4.0.0", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/sax": {"version": "1.4.1", "license": "ISC"}, "node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/semver-compare": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/serialize-error": {"version": "7.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"type-fest": "^0.13.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/serialize-error/node_modules/type-fest": {"version": "0.13.1", "dev": true, "license": "(MIT OR CC0-1.0)", "optional": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.1", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/simple-update-notifier": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"semver": "~7.0.0"}, "engines": {"node": ">=8.10.0"}}, "node_modules/simple-update-notifier/node_modules/semver": {"version": "7.0.0", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/spawn-command": {"version": "0.0.2", "dev": true}, "node_modules/sprintf-js": {"version": "1.1.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/stat-mode": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/string-width": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/sumchecker": {"version": "3.0.1", "dev": true, "license": "Apache-2.0", "dependencies": {"debug": "^4.1.0"}, "engines": {"node": ">= 8.0"}}, "node_modules/supports-color": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tar": {"version": "6.2.1", "dev": true, "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/temp-file": {"version": "3.4.0", "dev": true, "license": "MIT", "dependencies": {"async-exit-hook": "^2.0.1", "fs-extra": "^10.0.0"}}, "node_modules/temp-file/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/temp-file/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/temp-file/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/throttle-debounce": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/tmp": {"version": "0.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/tmp-promise": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"tmp": "^0.2.0"}}, "node_modules/to-fast-properties": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/to-readable-stream": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/tree-kill": {"version": "1.2.2", "dev": true, "license": "MIT", "bin": {"tree-kill": "cli.js"}}, "node_modules/truncate-utf8-bytes": {"version": "1.0.2", "dev": true, "license": "WTFPL", "dependencies": {"utf8-byte-length": "^1.0.1"}}, "node_modules/tslib": {"version": "2.7.0", "devOptional": true, "license": "0BSD"}, "node_modules/tunnel": {"version": "0.0.6", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.6.11 <=0.7.0 || >=0.7.3"}}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typed-emitter": {"version": "2.1.0", "license": "MIT", "optionalDependencies": {"rxjs": "*"}}, "node_modules/universalify": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-parse-lax": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"prepend-http": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/utf8-byte-length": {"version": "1.0.5", "dev": true, "license": "(WTFPL OR MIT)"}, "node_modules/util-deprecate": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/uuid": {"version": "9.0.1", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/vite": {"version": "3.2.10", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.15.9", "postcss": "^8.4.18", "resolve": "^1.22.1", "rollup": "^2.79.1"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "peerDependencies": {"@types/node": ">= 14", "less": "*", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/vite-plugin-electron": {"version": "0.10.4", "dev": true, "license": "MIT"}, "node_modules/vue": {"version": "3.5.4", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.4", "@vue/compiler-sfc": "3.5.4", "@vue/runtime-dom": "3.5.4", "@vue/server-renderer": "3.5.4", "@vue/shared": "3.5.4"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vue-eslint-parser": {"version": "9.4.3", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": ">=6.0.0"}}, "node_modules/vue-eslint-parser/node_modules/semver": {"version": "7.6.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/vue-router": {"version": "4.4.4", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.4"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/vue3-video-play": {"version": "1.3.2", "license": "ISC", "dependencies": {"hls.js": "^1.0.10", "throttle-debounce": "^3.0.1", "vue": "^3.2.2"}}, "node_modules/wait-on": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"axios": "^0.25.0", "joi": "^17.6.0", "lodash": "^4.17.21", "minimist": "^1.2.5", "rxjs": "^7.5.4"}, "bin": {"wait-on": "bin/wait-on"}, "engines": {"node": ">=10.0.0"}}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/word-wrap": {"version": "1.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/ws": {"version": "7.5.10", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xml-name-validator": {"version": "4.0.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12"}}, "node_modules/xmlbuilder": {"version": "15.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8.0"}}, "node_modules/y18n": {"version": "5.0.8", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/yargs": {"version": "17.7.2", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yauzl": {"version": "2.10.0", "dev": true, "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}, "dependencies": {"@babel/helper-string-parser": {"version": "7.24.8"}, "@babel/helper-validator-identifier": {"version": "7.24.7"}, "@babel/parser": {"version": "7.25.6", "requires": {"@babel/types": "^7.25.6"}}, "@babel/runtime": {"version": "7.26.9", "resolved": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.26.9.tgz", "integrity": "sha512-aA63XwOkcl4xxQa3HjPMqOP6LiK0ZDv3mUPYEFXkpHbaFjtGggE1A61FjFzJnB+p7/oy2gA8E+rcBNl/zC1tMg==", "dev": true, "requires": {"regenerator-runtime": "^0.14.0"}}, "@babel/types": {"version": "7.25.6", "requires": {"@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "to-fast-properties": "^2.0.0"}}, "@ctrl/tinycolor": {"version": "3.6.1"}, "@cycjimmy/awesome-js-funcs": {"version": "1.1.1", "dev": true}, "@cycjimmy/sass-lib": {"version": "0.1.0", "dev": true}, "@develar/schema-utils": {"version": "2.6.5", "dev": true, "requires": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}}, "@electron/get": {"version": "1.14.1", "dev": true, "requires": {"debug": "^4.1.1", "env-paths": "^2.2.0", "fs-extra": "^8.1.0", "global-agent": "^3.0.0", "global-tunnel-ng": "^2.7.1", "got": "^9.6.0", "progress": "^2.0.3", "semver": "^6.2.0", "sumchecker": "^3.0.1"}}, "@electron/universal": {"version": "1.2.1", "dev": true, "requires": {"@malept/cross-spawn-promise": "^1.1.0", "asar": "^3.1.0", "debug": "^4.3.1", "dir-compare": "^2.4.0", "fs-extra": "^9.0.1", "minimatch": "^3.0.4", "plist": "^3.0.4"}, "dependencies": {"fs-extra": {"version": "9.1.0", "dev": true, "requires": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "jsonfile": {"version": "6.1.0", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "universalify": {"version": "2.0.1", "dev": true}}}, "@element-plus/icons-vue": {"version": "2.3.1", "requires": {}}, "@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "requires": {"eslint-visitor-keys": "^3.3.0"}}, "@eslint-community/regexpp": {"version": "4.11.0", "dev": true}, "@eslint/eslintrc": {"version": "2.1.4", "dev": true, "requires": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}}, "@eslint/js": {"version": "8.57.0", "dev": true}, "@floating-ui/core": {"version": "1.6.8", "requires": {"@floating-ui/utils": "^0.2.7"}}, "@floating-ui/dom": {"version": "1.6.10", "requires": {"@floating-ui/core": "^1.6.0", "@floating-ui/utils": "^0.2.7"}}, "@floating-ui/utils": {"version": "0.2.7"}, "@hapi/hoek": {"version": "9.3.0", "dev": true}, "@hapi/topo": {"version": "5.1.0", "dev": true, "requires": {"@hapi/hoek": "^9.0.0"}}, "@humanwhocodes/config-array": {"version": "0.11.14", "dev": true, "requires": {"@humanwhocodes/object-schema": "^2.0.2", "debug": "^4.3.1", "minimatch": "^3.0.5"}}, "@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true}, "@humanwhocodes/object-schema": {"version": "2.0.3", "dev": true}, "@jridgewell/sourcemap-codec": {"version": "1.5.0"}, "@malept/cross-spawn-promise": {"version": "1.1.1", "dev": true, "requires": {"cross-spawn": "^7.0.1"}}, "@malept/flatpak-bundler": {"version": "0.4.0", "dev": true, "requires": {"debug": "^4.1.1", "fs-extra": "^9.0.0", "lodash": "^4.17.15", "tmp-promise": "^3.0.2"}, "dependencies": {"fs-extra": {"version": "9.1.0", "dev": true, "requires": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "jsonfile": {"version": "6.1.0", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "universalify": {"version": "2.0.1", "dev": true}}}, "@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "dev": true}, "@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@popperjs/core": {"version": "npm:@sxzz/popperjs-es@2.11.7"}, "@rushstack/eslint-patch": {"version": "1.10.4", "dev": true}, "@sideway/address": {"version": "4.1.5", "dev": true, "requires": {"@hapi/hoek": "^9.0.0"}}, "@sideway/formula": {"version": "3.0.1", "dev": true}, "@sideway/pinpoint": {"version": "2.0.0", "dev": true}, "@sindresorhus/is": {"version": "0.14.0", "dev": true}, "@szmarczak/http-timer": {"version": "1.1.2", "dev": true, "requires": {"defer-to-connect": "^1.0.1"}}, "@tootallnate/once": {"version": "2.0.0", "dev": true}, "@types/debug": {"version": "4.1.12", "dev": true, "requires": {"@types/ms": "*"}}, "@types/fs-extra": {"version": "9.0.13", "dev": true, "requires": {"@types/node": "*"}}, "@types/glob": {"version": "7.2.0", "dev": true, "optional": true, "requires": {"@types/minimatch": "*", "@types/node": "*"}}, "@types/lodash": {"version": "4.17.7"}, "@types/lodash-es": {"version": "4.17.12", "requires": {"@types/lodash": "*"}}, "@types/minimatch": {"version": "5.1.2", "dev": true, "optional": true}, "@types/ms": {"version": "0.7.34", "dev": true}, "@types/node": {"version": "16.18.108", "dev": true}, "@types/semver": {"version": "7.5.8"}, "@types/web-bluetooth": {"version": "0.0.16"}, "@types/yargs": {"version": "17.0.33", "dev": true, "requires": {"@types/yargs-parser": "*"}}, "@types/yargs-parser": {"version": "21.0.3", "dev": true}, "@types/yauzl": {"version": "2.10.3", "dev": true, "optional": true, "requires": {"@types/node": "*"}}, "@ungap/structured-clone": {"version": "1.2.0", "dev": true}, "@vitejs/plugin-vue": {"version": "3.2.0", "dev": true, "requires": {}}, "@vue/compiler-core": {"version": "3.5.4", "requires": {"@babel/parser": "^7.25.3", "@vue/shared": "3.5.4", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.0"}}, "@vue/compiler-dom": {"version": "3.5.4", "requires": {"@vue/compiler-core": "3.5.4", "@vue/shared": "3.5.4"}}, "@vue/compiler-sfc": {"version": "3.5.4", "requires": {"@babel/parser": "^7.25.3", "@vue/compiler-core": "3.5.4", "@vue/compiler-dom": "3.5.4", "@vue/compiler-ssr": "3.5.4", "@vue/shared": "3.5.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.11", "postcss": "^8.4.44", "source-map-js": "^1.2.0"}}, "@vue/compiler-ssr": {"version": "3.5.4", "requires": {"@vue/compiler-dom": "3.5.4", "@vue/shared": "3.5.4"}}, "@vue/devtools-api": {"version": "6.6.4"}, "@vue/eslint-config-prettier": {"version": "7.1.0", "dev": true, "requires": {"eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0"}}, "@vue/reactivity": {"version": "3.5.4", "requires": {"@vue/shared": "3.5.4"}}, "@vue/runtime-core": {"version": "3.5.4", "requires": {"@vue/reactivity": "3.5.4", "@vue/shared": "3.5.4"}}, "@vue/runtime-dom": {"version": "3.5.4", "requires": {"@vue/reactivity": "3.5.4", "@vue/runtime-core": "3.5.4", "@vue/shared": "3.5.4", "csstype": "^3.1.3"}}, "@vue/server-renderer": {"version": "3.5.4", "requires": {"@vue/compiler-ssr": "3.5.4", "@vue/shared": "3.5.4"}}, "@vue/shared": {"version": "3.5.4"}, "@vueuse/core": {"version": "9.13.0", "requires": {"@types/web-bluetooth": "^0.0.16", "@vueuse/metadata": "9.13.0", "@vueuse/shared": "9.13.0", "vue-demi": "*"}, "dependencies": {"vue-demi": {"version": "0.14.10", "requires": {}}}}, "@vueuse/electron": {"version": "9.13.0", "dev": true, "requires": {"@vueuse/shared": "9.13.0", "vue-demi": "*"}, "dependencies": {"vue-demi": {"version": "0.14.10", "dev": true, "requires": {}}}}, "@vueuse/metadata": {"version": "9.13.0"}, "@vueuse/shared": {"version": "9.13.0", "requires": {"vue-demi": "*"}, "dependencies": {"vue-demi": {"version": "0.14.10", "requires": {}}}}, "@xmldom/xmldom": {"version": "0.8.10", "dev": true}, "7zip-bin": {"version": "5.1.1", "dev": true}, "acorn": {"version": "8.12.1", "dev": true}, "acorn-jsx": {"version": "5.3.2", "dev": true, "requires": {}}, "agent-base": {"version": "6.0.2", "dev": true, "requires": {"debug": "4"}}, "ajv": {"version": "6.12.6", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "3.5.2", "dev": true, "requires": {}}, "ansi-regex": {"version": "5.0.1", "dev": true}, "ansi-styles": {"version": "4.3.0", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "anymatch": {"version": "3.1.3", "dev": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "app-builder-bin": {"version": "4.0.0", "dev": true}, "app-builder-lib": {"version": "23.6.0", "dev": true, "requires": {"@develar/schema-utils": "~2.6.5", "@electron/universal": "1.2.1", "@malept/flatpak-bundler": "^0.4.0", "7zip-bin": "~5.1.1", "async-exit-hook": "^2.0.1", "bluebird-lst": "^1.0.9", "builder-util": "23.6.0", "builder-util-runtime": "9.1.1", "chromium-pickle-js": "^0.2.0", "debug": "^4.3.4", "ejs": "^3.1.7", "electron-osx-sign": "^0.6.0", "electron-publish": "23.6.0", "form-data": "^4.0.0", "fs-extra": "^10.1.0", "hosted-git-info": "^4.1.0", "is-ci": "^3.0.0", "isbinaryfile": "^4.0.10", "js-yaml": "^4.1.0", "lazy-val": "^1.0.5", "minimatch": "^3.1.2", "read-config-file": "6.2.0", "sanitize-filename": "^1.6.3", "semver": "^7.3.7", "tar": "^6.1.11", "temp-file": "^3.4.0"}, "dependencies": {"fs-extra": {"version": "10.1.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "jsonfile": {"version": "6.1.0", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "semver": {"version": "7.6.3", "dev": true}, "universalify": {"version": "2.0.1", "dev": true}}}, "argparse": {"version": "2.0.1"}, "asar": {"version": "3.2.0", "dev": true, "requires": {"@types/glob": "^7.1.1", "chromium-pickle-js": "^0.2.0", "commander": "^5.0.0", "glob": "^7.1.6", "minimatch": "^3.0.4"}}, "async": {"version": "3.2.6", "dev": true}, "async-exit-hook": {"version": "2.0.1", "dev": true}, "async-validator": {"version": "4.2.5"}, "asynckit": {"version": "0.4.0", "dev": true}, "at-least-node": {"version": "1.0.0", "dev": true}, "axios": {"version": "0.25.0", "requires": {"follow-redirects": "^1.14.7"}}, "balanced-match": {"version": "1.0.2", "dev": true}, "base64-js": {"version": "1.5.1", "dev": true}, "big.js": {"version": "6.2.2", "resolved": "https://registry.npmmirror.com/big.js/-/big.js-6.2.2.tgz", "integrity": "sha512-y/ie+Faknx7sZA5MfGA2xKlu0GDv8RWrXGsmlteyJQ2lvoKv9GBK/fpRMc2qlSoBAgNxrixICFCBefIq8WCQpQ=="}, "binary-extensions": {"version": "2.3.0", "dev": true}, "bluebird": {"version": "3.7.2", "dev": true}, "bluebird-lst": {"version": "1.0.9", "dev": true, "requires": {"bluebird": "^3.5.5"}}, "boolbase": {"version": "1.0.0", "dev": true}, "boolean": {"version": "3.2.0", "dev": true, "optional": true}, "brace-expansion": {"version": "1.1.11", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.3", "dev": true, "requires": {"fill-range": "^7.1.1"}}, "buffer-alloc": {"version": "1.2.0", "dev": true, "requires": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "buffer-alloc-unsafe": {"version": "1.1.0", "dev": true}, "buffer-crc32": {"version": "0.2.13", "dev": true}, "buffer-equal": {"version": "1.0.0", "dev": true}, "buffer-fill": {"version": "1.0.0", "dev": true}, "buffer-from": {"version": "1.1.2", "dev": true}, "builder-util": {"version": "23.6.0", "dev": true, "requires": {"@types/debug": "^4.1.6", "@types/fs-extra": "^9.0.11", "7zip-bin": "~5.1.1", "app-builder-bin": "4.0.0", "bluebird-lst": "^1.0.9", "builder-util-runtime": "9.1.1", "chalk": "^4.1.1", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "fs-extra": "^10.0.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-ci": "^3.0.0", "js-yaml": "^4.1.0", "source-map-support": "^0.5.19", "stat-mode": "^1.0.0", "temp-file": "^3.4.0"}, "dependencies": {"fs-extra": {"version": "10.1.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "jsonfile": {"version": "6.1.0", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "universalify": {"version": "2.0.1", "dev": true}}}, "builder-util-runtime": {"version": "9.1.1", "requires": {"debug": "^4.3.4", "sax": "^1.2.4"}}, "cacheable-request": {"version": "6.1.0", "dev": true, "requires": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^3.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^4.1.0", "responselike": "^1.0.2"}, "dependencies": {"json-buffer": {"version": "3.0.0", "dev": true}, "keyv": {"version": "3.1.0", "dev": true, "requires": {"json-buffer": "3.0.0"}}, "lowercase-keys": {"version": "2.0.0", "dev": true}}}, "callsites": {"version": "3.1.0", "dev": true}, "chalk": {"version": "4.1.2", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "dependencies": {"supports-color": {"version": "7.2.0", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "chokidar": {"version": "3.6.0", "dev": true, "requires": {"anymatch": "~3.1.2", "braces": "~3.0.2", "fsevents": "~2.3.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "dependencies": {"glob-parent": {"version": "5.1.2", "dev": true, "requires": {"is-glob": "^4.0.1"}}}}, "chownr": {"version": "2.0.0", "dev": true}, "chromium-pickle-js": {"version": "0.2.0", "dev": true}, "ci-info": {"version": "3.9.0", "dev": true}, "cliui": {"version": "8.0.1", "dev": true, "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}}, "clone-response": {"version": "1.0.3", "dev": true, "requires": {"mimic-response": "^1.0.0"}}, "color-convert": {"version": "2.0.1", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "dev": true}, "colors": {"version": "1.0.3", "dev": true}, "combined-stream": {"version": "1.0.8", "dev": true, "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "5.1.0"}, "compare-version": {"version": "0.1.2", "dev": true}, "concat-map": {"version": "0.0.1", "dev": true}, "concurrently": {"version": "7.6.0", "dev": true, "requires": {"chalk": "^4.1.0", "date-fns": "^2.29.1", "lodash": "^4.17.21", "rxjs": "^7.0.0", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0", "tree-kill": "^1.2.2", "yargs": "^17.3.1"}}, "config-chain": {"version": "1.1.13", "dev": true, "optional": true, "requires": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "cross-env": {"version": "7.0.3", "dev": true, "requires": {"cross-spawn": "^7.0.1"}}, "cross-spawn": {"version": "7.0.3", "dev": true, "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "cssesc": {"version": "3.0.0", "dev": true}, "csstype": {"version": "3.1.3"}, "date-fns": {"version": "2.30.0", "dev": true, "requires": {"@babel/runtime": "^7.21.0"}}, "dayjs": {"version": "1.11.13"}, "debug": {"version": "4.3.7", "requires": {"ms": "^2.1.3"}}, "decompress-response": {"version": "3.3.0", "dev": true, "requires": {"mimic-response": "^1.0.0"}}, "deep-is": {"version": "0.1.4", "dev": true}, "defer-to-connect": {"version": "1.1.3", "dev": true}, "define-data-property": {"version": "1.1.4", "dev": true, "optional": true, "requires": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}}, "define-properties": {"version": "1.2.1", "dev": true, "optional": true, "requires": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}, "delayed-stream": {"version": "1.0.0", "dev": true}, "detect-node": {"version": "2.1.0", "dev": true, "optional": true}, "dir-compare": {"version": "2.4.0", "dev": true, "requires": {"buffer-equal": "1.0.0", "colors": "1.0.3", "commander": "2.9.0", "minimatch": "3.0.4"}, "dependencies": {"commander": {"version": "2.9.0", "dev": true, "requires": {"graceful-readlink": ">= 1.0.0"}}, "minimatch": {"version": "3.0.4", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}}}, "dmg-builder": {"version": "23.6.0", "dev": true, "requires": {"app-builder-lib": "23.6.0", "builder-util": "23.6.0", "builder-util-runtime": "9.1.1", "dmg-license": "^1.0.11", "fs-extra": "^10.0.0", "iconv-lite": "^0.6.2", "js-yaml": "^4.1.0"}, "dependencies": {"fs-extra": {"version": "10.1.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "jsonfile": {"version": "6.1.0", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "universalify": {"version": "2.0.1", "dev": true}}}, "doctrine": {"version": "3.0.0", "dev": true, "requires": {"esutils": "^2.0.2"}}, "dotenv": {"version": "9.0.2", "dev": true}, "dotenv-expand": {"version": "5.1.0", "dev": true}, "duplexer3": {"version": "0.1.5", "dev": true}, "ejs": {"version": "3.1.10", "dev": true, "requires": {"jake": "^10.8.5"}}, "electron": {"version": "20.3.12", "resolved": "https://registry.npmmirror.com/electron/-/electron-20.3.12.tgz", "integrity": "sha512-CuCZKhwdSXaUNIoEuPVZ25YH18zmox3wEM8Acwcy9tlpD8Jiuq3ji3RZ98eVWJalrpqbdE9LtTD/sLC86GIkLg==", "dev": true, "requires": {"@electron/get": "^1.14.1", "@types/node": "^16.11.26", "extract-zip": "^2.0.1"}}, "electron-builder": {"version": "23.6.0", "dev": true, "requires": {"@types/yargs": "^17.0.1", "app-builder-lib": "23.6.0", "builder-util": "23.6.0", "builder-util-runtime": "9.1.1", "chalk": "^4.1.1", "dmg-builder": "23.6.0", "fs-extra": "^10.0.0", "is-ci": "^3.0.0", "lazy-val": "^1.0.5", "read-config-file": "6.2.0", "simple-update-notifier": "^1.0.7", "yargs": "^17.5.1"}, "dependencies": {"fs-extra": {"version": "10.1.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "jsonfile": {"version": "6.1.0", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "universalify": {"version": "2.0.1", "dev": true}}}, "electron-log": {"version": "4.4.8"}, "electron-osx-sign": {"version": "0.6.0", "dev": true, "requires": {"bluebird": "^3.5.0", "compare-version": "^0.1.2", "debug": "^2.6.8", "isbinaryfile": "^3.0.2", "minimist": "^1.2.0", "plist": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "dev": true, "requires": {"ms": "2.0.0"}}, "isbinaryfile": {"version": "3.0.3", "dev": true, "requires": {"buffer-alloc": "^1.2.0"}}, "ms": {"version": "2.0.0", "dev": true}}}, "electron-publish": {"version": "23.6.0", "dev": true, "requires": {"@types/fs-extra": "^9.0.11", "builder-util": "23.6.0", "builder-util-runtime": "9.1.1", "chalk": "^4.1.1", "fs-extra": "^10.0.0", "lazy-val": "^1.0.5", "mime": "^2.5.2"}, "dependencies": {"fs-extra": {"version": "10.1.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "jsonfile": {"version": "6.1.0", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "universalify": {"version": "2.0.1", "dev": true}}}, "electron-updater": {"version": "5.3.0", "requires": {"@types/semver": "^7.3.6", "builder-util-runtime": "9.1.1", "fs-extra": "^10.0.0", "js-yaml": "^4.1.0", "lazy-val": "^1.0.5", "lodash.escaperegexp": "^4.1.2", "lodash.isequal": "^4.5.0", "semver": "^7.3.5", "typed-emitter": "^2.1.0"}, "dependencies": {"fs-extra": {"version": "10.1.0", "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "jsonfile": {"version": "6.1.0", "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "semver": {"version": "7.6.3"}, "universalify": {"version": "2.0.1"}}}, "element-plus": {"version": "2.8.2", "requires": {"@ctrl/tinycolor": "^3.4.1", "@element-plus/icons-vue": "^2.3.1", "@floating-ui/dom": "^1.0.1", "@popperjs/core": "npm:@sxzz/popperjs-es@^2.11.7", "@types/lodash": "^4.14.182", "@types/lodash-es": "^4.17.6", "@vueuse/core": "^9.1.0", "async-validator": "^4.2.5", "dayjs": "^1.11.3", "escape-html": "^1.0.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "memoize-one": "^6.0.0", "normalize-wheel-es": "^1.2.0"}}, "emoji-regex": {"version": "8.0.0", "dev": true}, "encodeurl": {"version": "1.0.2", "dev": true, "optional": true}, "end-of-stream": {"version": "1.4.4", "dev": true, "requires": {"once": "^1.4.0"}}, "entities": {"version": "4.5.0"}, "env-paths": {"version": "2.2.1", "dev": true}, "es-define-property": {"version": "1.0.0", "dev": true, "optional": true, "requires": {"get-intrinsic": "^1.2.4"}}, "es-errors": {"version": "1.3.0", "dev": true, "optional": true}, "es6-error": {"version": "4.1.1", "dev": true, "optional": true}, "esbuild": {"version": "0.15.18", "dev": true, "requires": {"@esbuild/android-arm": "0.15.18", "@esbuild/linux-loong64": "0.15.18", "esbuild-android-64": "0.15.18", "esbuild-android-arm64": "0.15.18", "esbuild-darwin-64": "0.15.18", "esbuild-darwin-arm64": "0.15.18", "esbuild-freebsd-64": "0.15.18", "esbuild-freebsd-arm64": "0.15.18", "esbuild-linux-32": "0.15.18", "esbuild-linux-64": "0.15.18", "esbuild-linux-arm": "0.15.18", "esbuild-linux-arm64": "0.15.18", "esbuild-linux-mips64le": "0.15.18", "esbuild-linux-ppc64le": "0.15.18", "esbuild-linux-riscv64": "0.15.18", "esbuild-linux-s390x": "0.15.18", "esbuild-netbsd-64": "0.15.18", "esbuild-openbsd-64": "0.15.18", "esbuild-sunos-64": "0.15.18", "esbuild-windows-32": "0.15.18", "esbuild-windows-64": "0.15.18", "esbuild-windows-arm64": "0.15.18"}}, "esbuild-windows-64": {"version": "0.15.18", "dev": true, "optional": true}, "escalade": {"version": "3.2.0", "dev": true}, "escape-html": {"version": "1.0.3"}, "escape-string-regexp": {"version": "4.0.0", "dev": true}, "eslint": {"version": "8.57.0", "dev": true, "requires": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.0", "@humanwhocodes/config-array": "^0.11.14", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}}, "eslint-config-prettier": {"version": "8.10.0", "dev": true, "requires": {}}, "eslint-plugin-prettier": {"version": "4.2.1", "dev": true, "requires": {"prettier-linter-helpers": "^1.0.0"}}, "eslint-plugin-vue": {"version": "9.28.0", "dev": true, "requires": {"@eslint-community/eslint-utils": "^4.4.0", "globals": "^13.24.0", "natural-compare": "^1.4.0", "nth-check": "^2.1.1", "postcss-selector-parser": "^6.0.15", "semver": "^7.6.3", "vue-eslint-parser": "^9.4.3", "xml-name-validator": "^4.0.0"}, "dependencies": {"semver": {"version": "7.6.3", "dev": true}}}, "eslint-scope": {"version": "7.2.2", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}}, "eslint-visitor-keys": {"version": "3.4.3", "dev": true}, "espree": {"version": "9.6.1", "dev": true, "requires": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}}, "esquery": {"version": "1.6.0", "dev": true, "requires": {"estraverse": "^5.1.0"}}, "esrecurse": {"version": "4.3.0", "dev": true, "requires": {"estraverse": "^5.2.0"}}, "estraverse": {"version": "5.3.0", "dev": true}, "estree-walker": {"version": "2.0.2"}, "esutils": {"version": "2.0.3", "dev": true}, "extract-zip": {"version": "2.0.1", "dev": true, "requires": {"@types/yauzl": "^2.9.1", "debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}}, "fast-deep-equal": {"version": "3.1.3", "dev": true}, "fast-diff": {"version": "1.3.0", "dev": true}, "fast-json-stable-stringify": {"version": "2.1.0", "dev": true}, "fast-levenshtein": {"version": "2.0.6", "dev": true}, "fastq": {"version": "1.17.1", "dev": true, "requires": {"reusify": "^1.0.4"}}, "fd-slicer": {"version": "1.1.0", "dev": true, "requires": {"pend": "~1.2.0"}}, "file-entry-cache": {"version": "6.0.1", "dev": true, "requires": {"flat-cache": "^3.0.4"}}, "filelist": {"version": "1.0.4", "dev": true, "requires": {"minimatch": "^5.0.1"}, "dependencies": {"brace-expansion": {"version": "2.0.1", "dev": true, "requires": {"balanced-match": "^1.0.0"}}, "minimatch": {"version": "5.1.6", "dev": true, "requires": {"brace-expansion": "^2.0.1"}}}}, "fill-range": {"version": "7.1.1", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "find-up": {"version": "5.0.0", "dev": true, "requires": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}}, "flat-cache": {"version": "3.2.0", "dev": true, "requires": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}}, "flatted": {"version": "3.3.1", "dev": true}, "follow-redirects": {"version": "1.15.9"}, "form-data": {"version": "4.0.0", "dev": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "fs-extra": {"version": "8.1.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs-minipass": {"version": "2.1.0", "dev": true, "requires": {"minipass": "^3.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "dev": true, "requires": {"yallist": "^4.0.0"}}}}, "fs.realpath": {"version": "1.0.0", "dev": true}, "function-bind": {"version": "1.1.2", "dev": true}, "get-caller-file": {"version": "2.0.5", "dev": true}, "get-intrinsic": {"version": "1.2.4", "dev": true, "optional": true, "requires": {"es-errors": "^1.3.0", "function-bind": "^1.1.2", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "hasown": "^2.0.0"}}, "get-stream": {"version": "5.2.0", "dev": true, "requires": {"pump": "^3.0.0"}}, "glob": {"version": "7.2.3", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "6.0.2", "dev": true, "requires": {"is-glob": "^4.0.3"}}, "global-agent": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"boolean": "^3.0.1", "es6-error": "^4.1.1", "matcher": "^3.0.0", "roarr": "^2.15.3", "semver": "^7.3.2", "serialize-error": "^7.0.1"}, "dependencies": {"semver": {"version": "7.6.3", "dev": true, "optional": true}}}, "global-tunnel-ng": {"version": "2.7.1", "dev": true, "optional": true, "requires": {"encodeurl": "^1.0.2", "lodash": "^4.17.10", "npm-conf": "^1.1.3", "tunnel": "^0.0.6"}}, "globals": {"version": "13.24.0", "dev": true, "requires": {"type-fest": "^0.20.2"}}, "globalthis": {"version": "1.0.4", "dev": true, "optional": true, "requires": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}}, "gopd": {"version": "1.0.1", "dev": true, "optional": true, "requires": {"get-intrinsic": "^1.1.3"}}, "got": {"version": "9.6.0", "dev": true, "requires": {"@sindresorhus/is": "^0.14.0", "@szmarczak/http-timer": "^1.1.2", "cacheable-request": "^6.0.0", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^4.1.0", "lowercase-keys": "^1.0.1", "mimic-response": "^1.0.1", "p-cancelable": "^1.0.0", "to-readable-stream": "^1.0.0", "url-parse-lax": "^3.0.0"}, "dependencies": {"get-stream": {"version": "4.1.0", "dev": true, "requires": {"pump": "^3.0.0"}}}}, "graceful-fs": {"version": "4.2.11"}, "graceful-readlink": {"version": "1.0.1", "dev": true}, "graphemer": {"version": "1.4.0", "dev": true}, "has-flag": {"version": "4.0.0", "dev": true}, "has-property-descriptors": {"version": "1.0.2", "dev": true, "optional": true, "requires": {"es-define-property": "^1.0.0"}}, "has-proto": {"version": "1.0.3", "dev": true, "optional": true}, "has-symbols": {"version": "1.0.3", "dev": true, "optional": true}, "hasown": {"version": "2.0.2", "dev": true, "requires": {"function-bind": "^1.1.2"}}, "hls.js": {"version": "1.5.15"}, "hosted-git-info": {"version": "4.1.0", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "http-cache-semantics": {"version": "4.1.1", "dev": true}, "http-proxy-agent": {"version": "5.0.0", "dev": true, "requires": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}}, "https-proxy-agent": {"version": "5.0.1", "dev": true, "requires": {"agent-base": "6", "debug": "4"}}, "iconv-lite": {"version": "0.6.3", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}, "ignore": {"version": "5.3.2", "dev": true}, "immutable": {"version": "4.3.7", "dev": true}, "import-fresh": {"version": "3.3.0", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "imurmurhash": {"version": "0.1.4", "dev": true}, "inflight": {"version": "1.0.6", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "dev": true}, "ini": {"version": "1.3.8", "dev": true, "optional": true}, "is-binary-path": {"version": "2.1.0", "dev": true, "requires": {"binary-extensions": "^2.0.0"}}, "is-ci": {"version": "3.0.1", "dev": true, "requires": {"ci-info": "^3.2.0"}}, "is-core-module": {"version": "2.15.1", "dev": true, "requires": {"hasown": "^2.0.2"}}, "is-extglob": {"version": "2.1.1", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "dev": true}, "is-glob": {"version": "4.0.3", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "7.0.0", "dev": true}, "is-path-inside": {"version": "3.0.3", "dev": true}, "isbinaryfile": {"version": "4.0.10", "dev": true}, "isexe": {"version": "2.0.0", "dev": true}, "jake": {"version": "10.9.2", "dev": true, "requires": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}}, "joi": {"version": "17.13.3", "dev": true, "requires": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "js-yaml": {"version": "4.1.0", "requires": {"argparse": "^2.0.1"}}, "jsmpeg-player": {"version": "3.0.3", "dev": true, "requires": {"@cycjimmy/awesome-js-funcs": "^1.0.1", "@cycjimmy/sass-lib": "^0.1.0"}}, "json-buffer": {"version": "3.0.1", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "dev": true}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true}, "json-stringify-safe": {"version": "5.0.1", "dev": true, "optional": true}, "json5": {"version": "2.2.3", "dev": true}, "jsonfile": {"version": "4.0.0", "dev": true, "requires": {"graceful-fs": "^4.1.6"}}, "keyv": {"version": "4.5.4", "dev": true, "requires": {"json-buffer": "3.0.1"}}, "lazy-val": {"version": "1.0.5"}, "levn": {"version": "0.4.1", "dev": true, "requires": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}}, "locate-path": {"version": "6.0.0", "dev": true, "requires": {"p-locate": "^5.0.0"}}, "lodash": {"version": "4.17.21"}, "lodash-es": {"version": "4.17.21"}, "lodash-unified": {"version": "1.0.3", "requires": {}}, "lodash.escaperegexp": {"version": "4.1.2"}, "lodash.isequal": {"version": "4.5.0"}, "lodash.merge": {"version": "4.6.2", "dev": true}, "lowercase-keys": {"version": "1.0.1", "dev": true}, "lru-cache": {"version": "6.0.0", "dev": true, "requires": {"yallist": "^4.0.0"}}, "magic-string": {"version": "0.30.11", "requires": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "matcher": {"version": "3.0.0", "dev": true, "optional": true, "requires": {"escape-string-regexp": "^4.0.0"}}, "memoize-one": {"version": "6.0.0"}, "mime": {"version": "2.6.0", "dev": true}, "mime-db": {"version": "1.52.0", "dev": true}, "mime-types": {"version": "2.1.35", "dev": true, "requires": {"mime-db": "1.52.0"}}, "mimic-response": {"version": "1.0.1", "dev": true}, "minimatch": {"version": "3.1.2", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.8", "dev": true}, "minipass": {"version": "5.0.0", "dev": true}, "minizlib": {"version": "2.1.2", "dev": true, "requires": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "dev": true, "requires": {"yallist": "^4.0.0"}}}}, "mitt": {"version": "3.0.1"}, "mkdirp": {"version": "1.0.4", "dev": true}, "mockjs": {"version": "1.1.0", "requires": {"commander": "*"}}, "ms": {"version": "2.1.3"}, "nanoid": {"version": "3.3.7"}, "natural-compare": {"version": "1.4.0", "dev": true}, "node-rtsp-stream": {"version": "0.0.9", "requires": {"ws": "^7.0.0"}}, "normalize-path": {"version": "3.0.0", "dev": true}, "normalize-url": {"version": "4.5.1", "dev": true}, "normalize-wheel-es": {"version": "1.2.0"}, "npm-conf": {"version": "1.1.3", "dev": true, "optional": true, "requires": {"config-chain": "^1.1.11", "pify": "^3.0.0"}}, "nprogress": {"version": "0.2.0"}, "nth-check": {"version": "2.1.1", "dev": true, "requires": {"boolbase": "^1.0.0"}}, "object-keys": {"version": "1.1.1", "dev": true, "optional": true}, "once": {"version": "1.4.0", "dev": true, "requires": {"wrappy": "1"}}, "optionator": {"version": "0.9.4", "dev": true, "requires": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}}, "p-cancelable": {"version": "1.1.0", "dev": true}, "p-limit": {"version": "3.1.0", "dev": true, "requires": {"yocto-queue": "^0.1.0"}}, "p-locate": {"version": "5.0.0", "dev": true, "requires": {"p-limit": "^3.0.2"}}, "parent-module": {"version": "1.0.1", "dev": true, "requires": {"callsites": "^3.0.0"}}, "path-exists": {"version": "4.0.0", "dev": true}, "path-is-absolute": {"version": "1.0.1", "dev": true}, "path-key": {"version": "3.1.1", "dev": true}, "path-parse": {"version": "1.0.7", "dev": true}, "pend": {"version": "1.2.0", "dev": true}, "picocolors": {"version": "1.1.0"}, "picomatch": {"version": "2.3.1", "dev": true}, "pify": {"version": "3.0.0", "dev": true, "optional": true}, "pinia": {"version": "2.2.2", "requires": {"@vue/devtools-api": "^6.6.3", "vue-demi": "^0.14.10"}, "dependencies": {"vue-demi": {"version": "0.14.10", "requires": {}}}}, "pinia-plugin-persistedstate": {"version": "2.4.0", "requires": {}}, "plist": {"version": "3.1.0", "dev": true, "requires": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}}, "postcss": {"version": "8.4.45", "requires": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}}, "postcss-selector-parser": {"version": "6.1.2", "dev": true, "requires": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}}, "prelude-ls": {"version": "1.2.1", "dev": true}, "prepend-http": {"version": "2.0.0", "dev": true}, "prettier": {"version": "2.8.8", "dev": true}, "prettier-linter-helpers": {"version": "1.0.0", "dev": true, "requires": {"fast-diff": "^1.1.2"}}, "progress": {"version": "2.0.3", "dev": true}, "proto-list": {"version": "1.2.4", "dev": true, "optional": true}, "pump": {"version": "3.0.1", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "punycode": {"version": "2.3.1", "dev": true}, "queue-microtask": {"version": "1.2.3", "dev": true}, "read-config-file": {"version": "6.2.0", "dev": true, "requires": {"dotenv": "^9.0.2", "dotenv-expand": "^5.1.0", "js-yaml": "^4.1.0", "json5": "^2.2.0", "lazy-val": "^1.0.4"}}, "readdirp": {"version": "3.6.0", "dev": true, "requires": {"picomatch": "^2.2.1"}}, "regenerator-runtime": {"version": "0.14.1", "dev": true}, "require-directory": {"version": "2.1.1", "dev": true}, "resolve": {"version": "1.22.8", "dev": true, "requires": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-from": {"version": "4.0.0", "dev": true}, "responselike": {"version": "1.0.2", "dev": true, "requires": {"lowercase-keys": "^1.0.0"}}, "reusify": {"version": "1.0.4", "dev": true}, "rimraf": {"version": "3.0.2", "dev": true, "requires": {"glob": "^7.1.3"}}, "roarr": {"version": "2.15.4", "dev": true, "optional": true, "requires": {"boolean": "^3.0.1", "detect-node": "^2.0.4", "globalthis": "^1.0.1", "json-stringify-safe": "^5.0.1", "semver-compare": "^1.0.0", "sprintf-js": "^1.1.2"}}, "rollup": {"version": "2.79.1", "dev": true, "requires": {"fsevents": "~2.3.2"}}, "run-parallel": {"version": "1.2.0", "dev": true, "requires": {"queue-microtask": "^1.2.2"}}, "rxjs": {"version": "7.8.1", "devOptional": true, "requires": {"tslib": "^2.1.0"}}, "safer-buffer": {"version": "2.1.2", "dev": true}, "sanitize-filename": {"version": "1.6.3", "dev": true, "requires": {"truncate-utf8-bytes": "^1.0.0"}}, "sass": {"version": "1.78.0", "dev": true, "requires": {"chokidar": ">=3.0.0 <4.0.0", "immutable": "^4.0.0", "source-map-js": ">=0.6.2 <2.0.0"}}, "sax": {"version": "1.4.1"}, "semver": {"version": "6.3.1", "dev": true}, "semver-compare": {"version": "1.0.0", "dev": true, "optional": true}, "serialize-error": {"version": "7.0.1", "dev": true, "optional": true, "requires": {"type-fest": "^0.13.1"}, "dependencies": {"type-fest": {"version": "0.13.1", "dev": true, "optional": true}}}, "shebang-command": {"version": "2.0.0", "dev": true, "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "dev": true}, "shell-quote": {"version": "1.8.1", "dev": true}, "simple-update-notifier": {"version": "1.1.0", "dev": true, "requires": {"semver": "~7.0.0"}, "dependencies": {"semver": {"version": "7.0.0", "dev": true}}}, "source-map": {"version": "0.6.1", "dev": true}, "source-map-js": {"version": "1.2.1"}, "source-map-support": {"version": "0.5.21", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "spawn-command": {"version": "0.0.2", "dev": true}, "sprintf-js": {"version": "1.1.3", "dev": true, "optional": true}, "stat-mode": {"version": "1.0.0", "dev": true}, "string-width": {"version": "4.2.3", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}, "strip-json-comments": {"version": "3.1.1", "dev": true}, "sumchecker": {"version": "3.0.1", "dev": true, "requires": {"debug": "^4.1.0"}}, "supports-color": {"version": "8.1.1", "dev": true, "requires": {"has-flag": "^4.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "dev": true}, "tar": {"version": "6.2.1", "dev": true, "requires": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}}, "temp-file": {"version": "3.4.0", "dev": true, "requires": {"async-exit-hook": "^2.0.1", "fs-extra": "^10.0.0"}, "dependencies": {"fs-extra": {"version": "10.1.0", "dev": true, "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}}, "jsonfile": {"version": "6.1.0", "dev": true, "requires": {"graceful-fs": "^4.1.6", "universalify": "^2.0.0"}}, "universalify": {"version": "2.0.1", "dev": true}}}, "text-table": {"version": "0.2.0", "dev": true}, "throttle-debounce": {"version": "3.0.1"}, "tmp": {"version": "0.2.3", "dev": true}, "tmp-promise": {"version": "3.0.3", "dev": true, "requires": {"tmp": "^0.2.0"}}, "to-fast-properties": {"version": "2.0.0"}, "to-readable-stream": {"version": "1.0.0", "dev": true}, "to-regex-range": {"version": "5.0.1", "dev": true, "requires": {"is-number": "^7.0.0"}}, "tree-kill": {"version": "1.2.2", "dev": true}, "truncate-utf8-bytes": {"version": "1.0.2", "dev": true, "requires": {"utf8-byte-length": "^1.0.1"}}, "tslib": {"version": "2.7.0", "devOptional": true}, "tunnel": {"version": "0.0.6", "dev": true, "optional": true}, "type-check": {"version": "0.4.0", "dev": true, "requires": {"prelude-ls": "^1.2.1"}}, "type-fest": {"version": "0.20.2", "dev": true}, "typed-emitter": {"version": "2.1.0", "requires": {"rxjs": "*"}}, "universalify": {"version": "0.1.2", "dev": true}, "uri-js": {"version": "4.4.1", "dev": true, "requires": {"punycode": "^2.1.0"}}, "url-parse-lax": {"version": "3.0.0", "dev": true, "requires": {"prepend-http": "^2.0.0"}}, "utf8-byte-length": {"version": "1.0.5", "dev": true}, "util-deprecate": {"version": "1.0.2", "dev": true}, "uuid": {"version": "9.0.1"}, "vite": {"version": "3.2.10", "dev": true, "requires": {"esbuild": "^0.15.9", "fsevents": "~2.3.2", "postcss": "^8.4.18", "resolve": "^1.22.1", "rollup": "^2.79.1"}}, "vite-plugin-electron": {"version": "0.10.4", "dev": true}, "vue": {"version": "3.5.4", "requires": {"@vue/compiler-dom": "3.5.4", "@vue/compiler-sfc": "3.5.4", "@vue/runtime-dom": "3.5.4", "@vue/server-renderer": "3.5.4", "@vue/shared": "3.5.4"}}, "vue-eslint-parser": {"version": "9.4.3", "dev": true, "requires": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "dependencies": {"semver": {"version": "7.6.3", "dev": true}}}, "vue-router": {"version": "4.4.4", "requires": {"@vue/devtools-api": "^6.6.4"}}, "vue3-video-play": {"version": "1.3.2", "requires": {"hls.js": "^1.0.10", "throttle-debounce": "^3.0.1", "vue": "^3.2.2"}}, "wait-on": {"version": "6.0.1", "dev": true, "requires": {"axios": "^0.25.0", "joi": "^17.6.0", "lodash": "^4.17.21", "minimist": "^1.2.5", "rxjs": "^7.5.4"}}, "which": {"version": "2.0.2", "dev": true, "requires": {"isexe": "^2.0.0"}}, "word-wrap": {"version": "1.2.5", "dev": true}, "wrap-ansi": {"version": "7.0.0", "dev": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "wrappy": {"version": "1.0.2", "dev": true}, "ws": {"version": "7.5.10", "requires": {}}, "xml-name-validator": {"version": "4.0.0", "dev": true}, "xmlbuilder": {"version": "15.1.1", "dev": true}, "y18n": {"version": "5.0.8", "dev": true}, "yallist": {"version": "4.0.0", "dev": true}, "yargs": {"version": "17.7.2", "dev": true, "requires": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}}, "yargs-parser": {"version": "21.1.1", "dev": true}, "yauzl": {"version": "2.10.0", "dev": true, "requires": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "yocto-queue": {"version": "0.1.0", "dev": true}}}