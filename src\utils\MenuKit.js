import { useMenu } from '@/stores/menu';
import { useUser } from '@/stores/user';
import { addDynamicRoutes } from '@/utils/router';
import { activeRouteTab } from './tabKit';

/**
 * 加载菜单
 */
export const loadMenusAndRoutes = (obj) => {
  const menu = useMenu();
  const user = useUser();

  if (!menu.state.menuLoaded) {
    let menus;
    if (user.role === 1) {
      menus = collectorMenus;
    } else {
      menus = adminMenus;
    }

    const page_work_statistics = user.page_work_statistics?.split(',').map((item) => parseInt(item, 10)) || [];
    if (!page_work_statistics.includes(user.role)) {
      // 去除掉menus中的本班统计信息菜单，要注意menus是多层结构
      menus = menus.map((menu) => {
        if (menu.children) {
          menu.children = menu.children.filter((child) => child.id !== '104');
        }
        return menu;
      });
    }

    console.info('menus', menus);
    // 添加动态路由
    addDynamicRoutes(menus);

    // 设置菜单
    menu.state.menuList = menus;
    // 设置页面
    setPages(menus);
    // 设置权限
    setPermissions(menus);

    // 设置菜单加载状态
    menu.state.menuLoaded = true;

    // 激活路由页签
    if (obj.path) {
      activeRouteTab({
        path: obj.path,
        query: obj.query
      });
    } else {
      activeRouteTab({
        name: obj.name,
        params: obj.params
      });
    }
  }
};

/**
 * 设置页面
 * @param {*} menus
 */
export const setPages = (menus) => {
  for (const idx in menus) {
    if (menus[idx].type == 'page') {
      const menu = useMenu();
      menu.state.pageList.push(menus[idx]);

      if (menus[idx].cached && menus[idx].cached === 1) {
        menu.state.includeKeepAlives.push(menus[idx].name);
      } else {
        menu.state.excludeKeepAlives.push(menus[idx].name);
      }
    }

    if (menus[idx].children && menus[idx].children.length > 0) {
      setPages(menus[idx].children);
    }
  }
};

/**
 * 设置权限
 * @param {*} menus
 */
export const setPermissions = (menus) => {
  for (const idx in menus) {
    if (menus[idx].permissions) {
      const menu = useMenu();
      menu.state.permissionList.push(...menus[idx].permissions);
    }

    if (menus[idx].children && menus[idx].children.length > 0) {
      setPermissions(menus[idx].children);
    }
  }
};

// 收费员菜单
const collectorMenus = [
  {
    id: '1',
    name: 'Watch',
    title: '岗亭值守',
    icon: 'DataBoard',
    type: 'menu',
    children: [
      {
        id: '101',
        name: 'Watch',
        title: '岗亭值守',
        path: '/watch/watch',
        type: 'page',
        visible: 1,
        cached: 1,
        component: '/src/views/watch/Watch.vue'
      },
      {
        id: '102',
        name: 'ShiftQuery',
        title: '交接班查询',
        path: '/watch/shiftQuery',
        type: 'page',
        visible: 1,
        cached: 1,
        component: '/src/views/watch/ShiftQuery.vue'
      },
      {
        id: '103',
        name: 'ShiftDetail',
        title: '班次统计信息',
        path: '/watch/shiftDetail',
        type: 'page',
        visible: 0,
        cached: 1,
        component: '/src/views/watch/ShiftDetail.vue'
      },
      {
        id: '104',
        name: 'CurrentShiftDetail',
        title: '本班统计信息',
        path: '/watch/currentShiftDetail',
        type: 'page',
        visible: 1,
        cached: 1,
        component: '/src/views/watch/CurrentShiftDetail.vue'
      },
      {
        id: '105',
        name: 'InterceptRecord',
        title: '拦截记录',
        path: '/watch/interceptRecord',
        type: 'page',
        visible: 1,
        component: '/src/views/watch/InterceptRecord.vue'
      },
      {
        id: '106',
        name: 'MockCar',
        title: '模拟车辆',
        path: '/watch/MockCar',
        type: 'page',
        visible: 1,
        cached: 1,
        component: '/src/views/watch/MockCar.vue'
      }
    ]
  },
  {
    id: '2',
    name: 'StandAccount',
    title: '台账查询',
    icon: 'Tickets',
    type: 'menu',
    children: [
      {
        id: '201',
        name: 'PassInRecord',
        title: '入场记录',
        path: '/stand_account/passInRecord',
        type: 'page',
        visible: 1,
        component: '/src/views/stand_account/PassInRecord.vue'
      },
      {
        id: '202',
        name: 'PassOutRecord',
        title: '出场记录',
        path: '/stand_account/passOutRecord',
        type: 'page',
        visible: 1,
        component: '/src/views/stand_account/PassOutRecord.vue'
      },
      {
        id: '203',
        name: 'ParkOrder',
        title: '停车订单',
        path: '/stand_account/parkOrder',
        type: 'page',
        visible: 1,
        component: '/src/views/stand_account/ParkOrder.vue'
      }
    ]
  },
  {
    id: '3',
    name: 'Car',
    title: '车辆查询',
    icon: 'Van',
    type: 'menu',
    children: [
      {
        id: '301',
        name: 'ParkInCar',
        title: '在场车辆',
        path: '/car/parkInCar',
        type: 'page',
        visible: 1,
        component: '/src/views/car/ParkInCar.vue'
      },
      {
        id: '302',
        name: 'NoPlateCar',
        title: '无牌车辆',
        path: '/car/noPlateCar',
        type: 'page',
        visible: 1,
        component: '/src/views/car/NoPlateCar.vue'
      },
      {
        id: '303',
        name: 'LongStopCar',
        title: '滞留车辆',
        path: '/car/longStopCar',
        type: 'page',
        visible: 1,
        component: '/src/views/car/LongStopCar.vue'
      },
      {
        id: '304',
        name: 'CarInfo',
        title: '一户多车',
        path: '/car/carInfo',
        type: 'page',
        visible: 1,
        component: '/src/views/car/CarInfo.vue'
      }
    ]
  },
  {
    id: '4',
    name: 'Park',
    title: '车场信息',
    icon: 'Place',
    type: 'menu',
    children: [
      {
        id: '401',
        name: 'FeeModel',
        title: '计费模型',
        path: '/park/feeModel',
        type: 'page',
        visible: 1,
        component: '/src/views/park/FeeModel.vue'
      },
      {
        id: '402',
        name: 'FeeModelDetail',
        title: '计费模型详情',
        path: '/park/feeModelDetail',
        type: 'page',
        visible: 0,
        component: '/src/views/park/FeeModelDetail.vue'
      },
      {
        id: '403',
        name: 'ParkInfo',
        title: '停车场信息',
        path: '/park/parkInfo',
        type: 'page',
        visible: 1,
        component: '/src/views/park/ParkInfo.vue'
      },
      {
        id: '404',
        name: 'BlackList',
        title: '黑名单列表',
        path: '/park/blackList',
        type: 'page',
        visible: 1,
        component: '/src/views/park/BlackList.vue'
      },
      {
        id: '405',
        name: 'WhiteList',
        title: '白名单列表',
        path: '/park/whiteList',
        type: 'page',
        visible: 1,
        component: '/src/views/park/WhiteList.vue'
      },
      {
        id: '406',
        name: 'RentCar',
        title: '长租车',
        path: '/park/rentCar',
        type: 'page',
        visible: 1,
        component: '/src/views/park/RentCar.vue'
      },
      {
        id: '407',
        name: 'InterceptRule',
        title: '区域防控规则',
        path: '/park/interceptRule',
        type: 'page',
        visible: 1,
        component: '/src/views/park/InterceptRule.vue'
      }
    ]
  },
  {
    id: '5',
    name: 'Device',
    title: '设备查询',
    icon: 'Cpu',
    type: 'menu',
    children: [
      {
        id: '501',
        name: 'Device',
        title: '设备通信',
        path: '/device/device',
        type: 'page',
        visible: 1,
        component: '/src/views/device/Device.vue'
      },
      {
        id: '502',
        name: 'DeviceState',
        title: '设备状态',
        path: '/device/deviceState',
        type: 'page',
        visible: 1,
        component: '/src/views/device/DeviceState.vue'
      },
      {
        id: '503',
        name: 'Terminal',
        title: '终端查询',
        path: '/device/terminal',
        type: 'page',
        visible: 1,
        component: '/src/views/device/Terminal.vue'
      },
      {
        id: '504',
        name: 'DeviceDetail',
        title: '设备详情',
        path: '/device/deviceDetail',
        type: 'page',
        visible: 0,
        component: '/src/views/device/DeviceDetail.vue'
      }
    ]
  },
  {
    id: '6',
    name: 'Log',
    title: '日志',
    icon: 'Notebook',
    type: 'menu',
    children: [
      {
        id: '601',
        name: 'SysLog',
        title: '系统日志',
        path: '/log/sysLog',
        type: 'page',
        visible: 1,
        component: '/src/views/log/SysLog.vue'
      }
      // {
      //   id: '602',
      //   name: 'OperateLog',
      //   title: '操作记录',
      //   path: '/log/operateLog',
      //   type: 'page',
      //   visible: 1,
      //   component: '/src/views/log/OperateLog.vue'
      // }
    ]
  },
  {
    id: '7',
    name: 'Sync',
    title: '数据同步',
    icon: 'Link',
    type: 'menu',
    children: [
      {
        id: '701',
        name: 'UpData',
        title: '上行数据',
        path: '/sync/upData',
        type: 'page',
        visible: 1,
        component: '/src/views/sync/UpData.vue'
      }
      // {
      //   id: '702',
      //   name: 'DownData',
      //   title: '下行数据',
      //   path: '/sync/downData',
      //   type: 'page',
      //   visible: 1,
      //   component: '/src/views/sync/DownData.vue'
      // }
    ]
  },
  // {
  //   id: '8',
  //   name: 'Monitor',
  //   title: '识别相机',
  //   icon: 'Monitor',
  //   type: 'menu',
  //   children: [
  //     {
  //       id: '801',
  //       name: 'MonitorChannel',
  //       title: '识别相机',
  //       path: '/monitor/monitorChannel',
  //       type: 'page',
  //       visible: 1,
  //       component: '/src/views/monitor/MonitorChannel.vue'
  //     }
  //   ]
  // },
  {
    id: '9',
    name: 'LaneMonitor',
    title: '通道监控',
    icon: 'VideoCamera',
    type: 'menu',
    children: [
      // {
      //   id: '901',
      //   name: 'RealtimeVideo',
      //   title: '实时视频',
      //   path: '/lane_monitor/realtimeVideo',
      //   type: 'page',
      //   visible: 1,
      //   component: '/src/views/lane_monitor/realtimeVideo.vue'
      // },
      {
        id: '902',
        name: 'DataQuery',
        title: '数据查询',
        path: '/lane_monitor/dataQuery',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/dataQuery.vue'
      },
      {
        id: '903',
        name: 'RuleConfig',
        title: '规则配置',
        path: '/lane_monitor/ruleConfig',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/ruleConfig.vue'
      },
      {
        id: '905',
        name: 'ArrearsOfFees',
        title: '欠逃费事件',
        path: '/lane_monitor/arrearsOfFees',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/arrearsOfFees.vue'
      },
      {
        id: '906',
        name: 'WarningReport',
        title: '预警上报事件',
        path: '/lane_monitor/warningReport',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/warningReport.vue'
      },
      {
        id: '904',
        name: 'AdvancedConfig',
        title: '高级设置',
        path: '/lane_monitor/advancedConfig',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/advancedConfig.vue'
      }
    ]
  },
  {
    id: '10',
    name: 'Setting',
    title: '岗亭设置',
    icon: 'Monitor',
    type: 'menu',
    children: [
      {
        id: '1001',
        name: 'CustomizeConfig',
        title: '自定义配置信息',
        path: '/setting/CustomizeConfig',
        type: 'page',
        visible: 1,
        component: '/src/views/setting/CustomizeConfig.vue'
      }
    ]
  },
  {
    id: '11',
    name: 'OndutyMenu',
    title: '云端值守',
    icon: 'Monitor',
    type: 'menu',
    children: [
      {
        id: '1101',
        name: 'Onduty',
        title: '云端值守',
        path: '/OndutyMenu/Onduty',
        type: 'page',
        visible: 1,
        component: '/src/views/Onduty/Onduty.vue'
      }
    ]
  }
];

// 管理员菜单
const adminMenus = [
  {
    id: '1',
    name: 'Watch',
    title: '岗亭值守',
    icon: 'DataBoard',
    type: 'menu',
    children: [
      {
        id: '102',
        name: 'ShiftQuery',
        title: '交接班查询',
        path: '/watch/shiftQuery',
        type: 'page',
        visible: 1,
        cached: 1,
        component: '/src/views/watch/ShiftQuery.vue'
      },
      {
        id: '103',
        name: 'ShiftDetail',
        title: '班次统计信息',
        path: '/watch/shiftDetail',
        type: 'page',
        visible: 0,
        cached: 1,
        component: '/src/views/watch/ShiftDetail.vue'
      },
      {
        id: '105',
        name: 'InterceptRecord',
        title: '拦截记录',
        path: '/watch/interceptRecord',
        type: 'page',
        visible: 1,
        component: '/src/views/watch/InterceptRecord.vue'
      }
    ]
  },
  {
    id: '2',
    name: 'StandAccount',
    title: '台账查询',
    icon: 'Tickets',
    type: 'menu',
    children: [
      {
        id: '201',
        name: 'PassInRecord',
        title: '入场记录',
        path: '/stand_account/passInRecord',
        type: 'page',
        visible: 1,
        component: '/src/views/stand_account/PassInRecord.vue'
      },
      {
        id: '202',
        name: 'PassOutRecord',
        title: '出场记录',
        path: '/stand_account/passOutRecord',
        type: 'page',
        visible: 1,
        component: '/src/views/stand_account/PassOutRecord.vue'
      },
      {
        id: '203',
        name: 'ParkOrder',
        title: '停车订单',
        path: '/stand_account/parkOrder',
        type: 'page',
        visible: 1,
        component: '/src/views/stand_account/ParkOrder.vue'
      }
    ]
  },
  {
    id: '3',
    name: 'Car',
    title: '车辆查询',
    icon: 'Van',
    type: 'menu',
    children: [
      {
        id: '301',
        name: 'ParkInCar',
        title: '在场车辆',
        path: '/car/parkInCar',
        type: 'page',
        visible: 1,
        component: '/src/views/car/ParkInCar.vue'
      },
      {
        id: '302',
        name: 'NoPlateCar',
        title: '无牌车辆',
        path: '/car/noPlateCar',
        type: 'page',
        visible: 1,
        component: '/src/views/car/NoPlateCar.vue'
      },
      {
        id: '303',
        name: 'LongStopCar',
        title: '滞留车辆',
        path: '/car/longStopCar',
        type: 'page',
        visible: 1,
        component: '/src/views/car/LongStopCar.vue'
      },
      {
        id: '304',
        name: 'CarInfo',
        title: '一户多车',
        path: '/car/carInfo',
        type: 'page',
        visible: 1,
        component: '/src/views/car/CarInfo.vue'
      }
    ]
  },
  {
    id: '4',
    name: 'Park',
    title: '车场信息',
    icon: 'Place',
    type: 'menu',
    children: [
      {
        id: '401',
        name: 'FeeModel',
        title: '计费模型',
        path: '/park/feeModel',
        type: 'page',
        visible: 1,
        component: '/src/views/park/FeeModel.vue'
      },
      {
        id: '402',
        name: 'FeeModelDetail',
        title: '计费模型详情',
        path: '/park/feeModelDetail',
        type: 'page',
        visible: 0,
        component: '/src/views/park/FeeModelDetail.vue'
      },
      {
        id: '403',
        name: 'ParkInfo',
        title: '停车场信息',
        path: '/park/parkInfo',
        type: 'page',
        visible: 1,
        component: '/src/views/park/ParkInfo.vue'
      },
      {
        id: '404',
        name: 'BlackList',
        title: '黑名单列表',
        path: '/park/blackList',
        type: 'page',
        visible: 1,
        component: '/src/views/park/BlackList.vue'
      },
      {
        id: '405',
        name: 'WhiteList',
        title: '白名单列表',
        path: '/park/whiteList',
        type: 'page',
        visible: 1,
        component: '/src/views/park/WhiteList.vue'
      },
      {
        id: '406',
        name: 'RentCar',
        title: '长租车',
        path: '/park/rentCar',
        type: 'page',
        visible: 1,
        component: '/src/views/park/RentCar.vue'
      },
      {
        id: '407',
        name: 'InterceptRule',
        title: '区域防控规则',
        path: '/park/interceptRule',
        type: 'page',
        visible: 1,
        component: '/src/views/park/InterceptRule.vue'
      }
    ]
  },
  {
    id: '5',
    name: 'Device',
    title: '设备查询',
    icon: 'Cpu',
    type: 'menu',
    children: [
      {
        id: '501',
        name: 'Device',
        title: '设备通信',
        path: '/device/device',
        type: 'page',
        visible: 1,
        component: '/src/views/device/Device.vue'
      },
      {
        id: '502',
        name: 'DeviceState',
        title: '设备状态',
        path: '/device/deviceState',
        type: 'page',
        visible: 1,
        component: '/src/views/device/DeviceState.vue'
      },
      {
        id: '503',
        name: 'Terminal',
        title: '终端查询',
        path: '/device/terminal',
        type: 'page',
        visible: 1,
        component: '/src/views/device/Terminal.vue'
      },
      {
        id: '504',
        name: 'DeviceDetail',
        title: '设备详情',
        path: '/device/deviceDetail',
        type: 'page',
        visible: 0,
        component: '/src/views/device/DeviceDetail.vue'
      }
    ]
  },
  {
    id: '6',
    name: 'Log',
    title: '日志',
    icon: 'Notebook',
    type: 'menu',
    children: [
      {
        id: '601',
        name: 'SysLog',
        title: '系统日志',
        path: '/log/sysLog',
        type: 'page',
        visible: 1,
        component: '/src/views/log/SysLog.vue'
      }
      // {
      //   id: '602',
      //   name: 'OperateLog',
      //   title: '操作记录',
      //   path: '/log/operateLog',
      //   type: 'page',
      //   visible: 1,
      //   component: '/src/views/log/OperateLog.vue'
      // }
    ]
  },
  {
    id: '7',
    name: 'Sync',
    title: '数据同步',
    icon: 'Link',
    type: 'menu',
    children: [
      {
        id: '701',
        name: 'UpData',
        title: '上行数据',
        path: '/sync/upData',
        type: 'page',
        visible: 1,
        component: '/src/views/sync/UpData.vue'
      }
      // {
      //   id: '702',
      //   name: 'DownData',
      //   title: '下行数据',
      //   path: '/sync/downData',
      //   type: 'page',
      //   visible: 1,
      //   component: '/src/views/sync/DownData.vue'
      // }
    ]
  },
  // {
  //   id: '8',
  //   name: 'Monitor',
  //   title: '识别相机',
  //   icon: 'Monitor',
  //   type: 'menu',
  //   children: [
  //     {
  //       id: '801',
  //       name: 'MonitorChannel',
  //       title: '识别相机',
  //       path: '/monitor/monitorChannel',
  //       type: 'page',
  //       visible: 1,
  //       component: '/src/views/monitor/MonitorChannel.vue'
  //     }
  //   ]
  // },
  {
    id: '9',
    name: 'LaneMonitor',
    title: '通道监控',
    icon: 'VideoCamera',
    type: 'menu',
    children: [
      // {
      //   id: '901',
      //   name: 'RealtimeVideo',
      //   title: '实时视频',
      //   path: '/lane_monitor/realtimeVideo',
      //   type: 'page',
      //   visible: 1,
      //   component: '/src/views/lane_monitor/realtimeVideo.vue'
      // },
      {
        id: '902',
        name: 'DataQuery',
        title: '数据查询',
        path: '/lane_monitor/dataQuery',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/dataQuery.vue'
      },
      {
        id: '903',
        name: 'RuleConfig',
        title: '规则配置',
        path: '/lane_monitor/ruleConfig',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/ruleConfig.vue'
      },
      {
        id: '905',
        name: 'ArrearsOfFees',
        title: '欠逃费事件',
        path: '/lane_monitor/arrearsOfFees',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/arrearsOfFees.vue'
      },
      {
        id: '906',
        name: 'WarningReport',
        title: '预警上报事件',
        path: '/lane_monitor/warningReport',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/warningReport.vue'
      },
      {
        id: '904',
        name: 'AdvancedConfig',
        title: '高级设置',
        path: '/lane_monitor/advancedConfig',
        type: 'page',
        visible: 1,
        component: '/src/views/lane_monitor/advancedConfig.vue'
      }
    ]
  },
  {
    id: '10',
    name: 'Setting',
    title: '岗亭设置',
    icon: 'Monitor',
    type: 'menu',
    children: [
      {
        id: '1001',
        name: 'CustomizeConfig',
        title: '自定义配置信息',
        path: '/setting/CustomizeConfig',
        type: 'page',
        visible: 1,
        component: '/src/views/setting/CustomizeConfig.vue'
      }
    ]
  },
  {
    id: '11',
    name: 'OndutyMenu',
    title: '云端值守',
    icon: 'Monitor',
    type: 'menu',
    children: [
      {
        id: '1101',
        name: 'Onduty',
        title: '云端值守',
        path: '/OndutyMenu/Onduty',
        type: 'page',
        visible: 1,
        component: '/src/views/Onduty/Onduty.vue'
      }
    ]
  }
];
