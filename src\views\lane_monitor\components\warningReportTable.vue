<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-02-27 17:34:02
 * @LastEditTime: 2024-03-01 10:56:15
 * @LastEditors: 达万安 段世煜
 * @Description: 事件table组件
 * @FilePath: \parking-client-ui\src\views\lane_monitor\components\eventTable.vue
-->
<template>
  <el-card class="table" shadow="never">
    <div>
      <el-space>
        <ui style="display: flex; margin-bottom: 10px">
          <li style="margin-left: 8px" v-for="(item, i) in types" :key="i">{{ item.label }}：{{ item.num }}起{{ i < 5
              ? ', ' : '' }}</li>
        </ui>
      </el-space>
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange" ref="tableRef">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <!-- <el-table-column type="index" label="序号" align="center" width="60" /> -->
        <el-table-column prop="plate_no" label="车牌号" align="center" min-width="100" />
        <el-table-column prop="event_no" label="事件编号" align="center" min-width="100" />
        <el-table-column prop="event_type" label="事件类型" align="center" min-width="100" />
        <el-table-column prop="recover_status" label="事件是否恢复" align="center" min-width="100" />
        <el-table-column prop="event_time" label="事件发起时间" align="center" min-width="100" />
        <el-table-column prop="duration" label="事件持续时长" align="center" min-width="100" />
        <el-table-column prop="gateway_name" label="通道名称" align="center" min-width="100" />
        <el-table-column prop="gateway_type" label="通道类型" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.gateway_type == 1">出口</span>
            <span v-if="scope.row.gateway_type == 2">入口</span>
            <span v-if="scope.row.gateway_type == 3">出入混合</span>
          </template>
        </el-table-column>
        <el-table-column label="事件抓拍图" align="center" min-width="100">
          <template v-slot="scope">
            <el-button link type="primary" @click="handelViewPic(scope.row)">查看图片</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="event_video" label="事件短视频" align="center" min-width="100">
          <template v-slot="scope">
            <el-button link type="primary" @click="handelViewPic(scope.row, 'video')">查看视频</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background v-model:current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        v-model:page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper"
        :total="data.queryParams.total" class="table-pagination" @size-change="handleSizeOrCurrentChange"
        @current-change="handleSizeOrCurrentChange" />
    </div>
  </el-card>
  <!-- 图片预览组件 -->
  <el-image-viewer v-if="showImagePreview" hide-on-click-modal @close="() => (showImagePreview = false)"
    :url-list="imgPreviewList" />
</template>

<script name="DeviceTable" setup>
import ArrearsOfFeesApi from '@/api/arrears-of-fees/arrearsOfFees';
import { exportImageFile } from '@/api/lane_monitor/laneMonitorApi';
import { useSetting } from '@/stores/setting';
import { useIpcRenderer } from '@vueuse/electron';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import { storeToRefs } from 'pinia';
import { nextTick, onMounted, reactive, ref, watch } from 'vue';
// 渲染进程回调 监听文件是否处理成功
const ipcRenderer = useIpcRenderer();
ipcRenderer.on('open-export-file-fail', (event, data) => {
  ElMessage.error(data);
});
// 查询数据
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    total: 0,
    camera_id: ''
  }
});
// 表格数据
const tableData = ref([]);
// 表格实体
const tableRef = ref();
// 表格加载状态
const loading = ref(false);
// camera_id
const setting = useSetting();
const { lane_monitor } = storeToRefs(setting);
const topData = ref({
  ryzl: 0,
  clzl: 0,
  ryjj: 0,
  fjdczl: 0,
  clyd: 0,
  dzwgw: 0
});
onMounted(() => {
  // 数据初始化
  nextTick(() => {
    initData(lane_monitor.value);
  });
});

// 监听lane_monitor变化，数据初始化
watch(lane_monitor, (val) => {
  initData(val);
});

/**
 * @description: 初始化数据
 * @param {*} val camera_id
 */
const initData = (val) => {
  // 设置data.queryParams.camera_id的值为val
  data.queryParams.camera_id = val;
  // 调用getList函数，传入data.queryParams参数
  getList(data.queryParams);
};

/**
 * @description: 获取表格数据
 * @param {*} params 表格入参
 * @param {*} init 是否初始化
 */
const types = ref([
  {
    label: '人员滞留',
    value: 80,
    num: 0
  },
  {
    label: '车辆滞留',
    value: 81,
    num: 0
  },
  {
    label: '人员聚集',
    value: 96,
    num: 0
  },
  {
    label: '非机动车滞留',
    value: 112,
    num: 0
  },
  {
    label: '道闸未归位',
    value: 129,
    num: 0
  },
  {
    label: '车辆拥堵',
    value: 144,
    num: 0
  }
]);
const getCount = (queryParams) => {
  // 获取事件列表
  for (let i = 0; i < types.value.length; i++) {
    ArrearsOfFeesApi.countEventsRelation({
      eventTypeId: types.value[i].value,
      startTime: queryParams.start_time,
      endTime: queryParams.end_time
    }).then((res) => {
      types.value[i].num = res.data;
    });
  }
};
const getList = async (params, init = false) => {
  // 初始化
  if (init) {
    // 设置页面和每页条数
    data.queryParams.page = 1;
    data.queryParams.limit = 30;
    // 清空已选择行
    selectedRows.value = [];
  }
  // 加载中
  loading.value = true;
  // 设置查询参数
  data.queryParams = { ...data.queryParams, ...params };
  try {
    getCount(data.queryParams);
    const { data: resData, message, success } = await ArrearsOfFeesApi.pagingEventsRelation(data.queryParams);
    if (success) {
      // 设置表格数据
      tableData.value = resData.rows;
      // 设置总条数
      data.queryParams.total = parseInt(resData.total);
    } else {
      // 弹出错误信息
      ElMessage({
        message: message,
        type: 'error'
      });
    }
  } catch (error) {
    // 弹出错误信息
    ElMessage({
      message: error,
      type: 'error'
    });
  } finally {
    // 加载完成
    loading.value = false;
  }
};

// 图片预览展示标识
const showImagePreview = ref(false);
// 图片预览列表
const imgPreviewList = ref([]);
/**
 * @description: 图片预览
 * @param {*} val 源数据
 */
const handelViewPic = (val,type) => {
  if (type) {
    ElMessage.warning(`设备暂不支持视频`);
    return;
  }
  if (!val.event_data) {
    ElMessage.warning(`当前${val.event_level}暂无图片`);
    return;
  }
  let imageUrl = val.event_data;
  imgPreviewList.value = [imageUrl];
  showImagePreview.value = true;
};
/**
 * @description: 分页
 */
const handleSizeOrCurrentChange = () => {
  getList(data.queryParams);
  selectedRows.value = [];
};
// 多选选择行
const selectedRows = ref([]);
/**
 * @description: 选择行
 * @param {*} val 当前选择值数组
 */
const handleSelectionChange = (val) => {
  selectedRows.value = val;
};
// 导出加载状态
const exportLoading = ref();
/**
 * @description: 打开loading
 */
const openLoading = () => {
  exportLoading.value = ElLoading.service({
    lock: true,
    text: '正在导出，请稍后',
    background: 'rgba(0, 0, 0, 0.7)'
  });
};
/**
 * @description: 导出图片
 */
const exportImage = async () => {
  if (selectedRows.value.length) {
    openLoading();
    const ids = selectedRows.value.map((item) => {
      return item.id;
    });
    try {
      const { data: resData, success, message } = await exportImageFile({ camera_id: data.queryParams.camera_id, ids: ids.join(',') });
      if (success) {
        // 清空已选择行
        selectedRows.value = [];
        tableRef.value.clearSelection();
        // 处理导出数据
        dealFile(resData);
      } else {
        ElMessage({
          message: message,
          type: 'error'
        });
      }
    } finally {
      exportLoading.value.close();
    }
  } else {
    ElMessage({
      message: '请选择需要下载的图片',
      type: 'warning'
    });
  }
};
/**
 * @description: 导出列表
 */

const exportList = async () => {
  ArrearsOfFeesApi.exportEventsRelation({
    event_type_id: data.queryParams.event_type_id,
    start_time: data.queryParams.start_time,
    end_time: data.queryParams.end_time,
    plate_no: data.queryParams.plate_no,
    gateway_id: data.queryParams.gateway_id
  });
};
/**
 * @description: 处理导出数据 在文件系统中打开
 * @param {*} url 文件绝对路径
 */
const dealFile = (url) => {
  if (!url) {
    ElMessage.error('未获取到下载文件');
    return;
  }
  ElMessageBox.confirm('文件已导出至本地，是否需要在文件夹中打开文件？', '提示', {
    confirmButtonText: '在文件夹中显示',
    cancelButtonText: '取消',
    type: 'success'
  }).then(() => {
    const urlUse = url.replaceAll('/', '\\');
    ipcRenderer.send('open-export-file', urlUse);
  });
};

defineExpose({
  getList,
  exportImage,
  exportList
});
</script>
<style lang="scss" scoped>
ul,
li {
  list-style: none;
}
</style>
