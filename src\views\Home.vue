<template>
  <el-container class="page-container">
    <el-header height="48px">
      <frame-header />
    </el-header>
    <el-container>
      <el-aside width="86px">
        <frame-menu />
      </el-aside>
      <el-main>
        <frame-tab />
        <frame-content />
      </el-main>
    </el-container>
    <el-footer height="36px">
      <frame-footer />
    </el-footer>
  </el-container>
</template>

<script name="Home" setup>
import { useUser } from '@/stores/user';
import { onMounted, reactive } from 'vue';
import router from '@/router/index';
import FrameHeader from './frame/FrameHeader.vue';
import FrameMenu from './frame/FrameMenu.vue';
import FrameTab from './frame/FrameTab.vue';
import FrameContent from './frame/FrameContent.vue';
import FrameFooter from './frame/FrameFooter.vue';
import { loadMenusAndRoutes } from '@/utils/menuKit';
import { activeRouteTab } from '@/utils/tabKit';

onMounted(() => {
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }

  // 初始化-加载菜单
  activeRouteTab({
    path: user.role === 1 ? '/watch/watch' : '/watch/shiftQuery'
  });
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background-color: #f2f2f2;
}

.el-header {
  padding: 0px;
}

.el-main {
  padding: 0px;
}

.el-aside {
  padding: 0px;
  background-color: #00474f;
}

.el-footer {
  padding: 0;
}
</style>
