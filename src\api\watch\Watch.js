/*
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-27 19:36:25
 * @FilePath: \new-wanda-park\parking-client-ui\src\api\watch\Watch.js
 * @Description: {}
 */
import $ from '@/utils/axios';

// 查询通道数据
export const querySentryGateway = (data) => {
  return $({
    url: '/sentry/watch/querySentryGateway',
    method: 'post',
    data
  });
};

// 切换出入口
export const changeInOut = (params) => {
  return $({
    url: '/sentry/operate/convertGateway',
    method: 'get',
    params
  });
};

// 查询最近一条入场记录
export const queryLastParkInRecord = (data) => {
  return $({
    url: '/sentry/watch/queryLastParkInRecord',
    method: 'post',
    data
  });
};

// 按入场时间生成当前需缴费订单
export const getParkOrder = (data) => {
  return $({
    url: '/sentry/watch/generateParkOrder',
    method: 'post',
    data
  });
};

// 重新选择计费方式
export const refreshOrder = (data) => {
  return $({
    url: '/sentry/watch/refreshOrder',
    method: 'post',
    data
  });
};

// 切换费率
export const switchCarType = (data) => {
  return $({
    url: '/sentry/watch/switchCarType',
    method: 'post',
    data
  });
};

// 修正入场记录
export const updateCarInRecord = (data) => {
  return $({
    url: '/sentry/watch/updateCarInRecord',
    method: 'post',
    data
  });
};

// 修正入场记录   无牌车
export const modifyPlateNo = (data) => {
  return $({
    url: '/sentry/watch/modifyPlateNo',
    method: 'post',
    data
  });
};

// 生成无牌车号码
export const generateCarNoPlateNo = (data) => {
  return $({
    url: '/sentry/car/generateCarNoPlateNo',
    method: 'post',
    data
  });
};

// 取消订单中所有的冻结的优免券
export const cancelFreeCoupon = (data) => {
  return $({
    url: '/sentry/watch/cancelFreeCoupon',
    method: 'post',
    data
  });
};

// 查询入场记录
export const queryParkInRecord = (data) => {
  return $({
    url: '/sentry/watch/queryParkInRecord',
    method: 'post',
    data
  });
};

// 查询出场记录
export const queryParkOutRecord = (data) => {
  return $({
    url: '/sentry/watch/queryLastParkOutRecord',
    method: 'post',
    data
  });
};

// 查询车牌可用优惠券
export const queryAvailableCoupons = (data) => {
  return $({
    url: '/sentry/watch/queryAvailableCoupons',
    method: 'post',
    data
  });
};

// 使用优惠券
export const useCoupon = (data) => {
  return $({
    url: '/sentry/watch/useCoupon',
    method: 'post',
    data
  });
};

// 收费放行
export const chargeAndPass = (data) => {
  return $({
    url: '/sentry/watch/chargeAndPass',
    method: 'post',
    data
  });
};

// 特殊放行
export const specialPass = (data) => {
  return $({
    url: '/sentry/watch/specialPass',
    method: 'post',
    data
  });
};

// 手动抓拍
export const manualCapture = (data) => {
  return $({
    url: '/sentry/manualCapture',
    method: 'post',
    data
  });
};

// 取消放行
export const cancelPass = (data) => {
  return $({
    url: '/sentry/cancelPass',
    method: 'post',
    data
  });
};

// 抬杆测试
export const liftRoll = (data) => {
  return $({
    url: '/sentry/liftRoll',
    method: 'post',
    data
  });
};

// 落杆测试
export const downRoll = (data) => {
  return $({
    url: '/sentry/operate/closeGate' + `?gatewayId=${data.gateway_id}`,
    method: 'get'
  });
};

// 车队模式
export const fleetMode = (data) => {
  return $({
    url: '/sentry/fleetMode',
    method: 'post',
    data
  });
};

// 关闭车队
export const fleetModeClose = (data) => {
  return $({
    url: '/sentry/fleetModeClose',
    method: 'post',
    data
  });
};

// 一体机测试
export const testOneMachine = (data) => {
  return $({
    url: '/test/testOneMachine',
    method: 'post',
    data
  });
};

// 手动匹配-最近入场记录
export const getTopNCarInRecordInPark = (data) => {
  return $({
    url: '/sentry/watch/getTopNCarInRecordInPark',
    method: 'post',
    data
  });
};

// 允许入场(入口)
export const manualPassIn = (data) => {
  return $({
    url: '/sentry/watch/manualPassIn',
    method: 'post',
    data
  });
};

//手动补录
export const manualRecordCarInRecord = (data) => {
  return $({
    url: '/sentry/watch/manualRecordCarInRecord',
    method: 'post',
    data
  });
};

//统计车场应收和实收、空闲车位
export const statParkStatus = (shiftRecordId) => {
  return $({
    url: '/sentry/watch/statParkStatus?shiftRecordId=' + shiftRecordId,
    method: 'post'
  });
};

//校准空闲车位
export const adjustParkSpace = (spaceAdjust) => {
  return $({
    url: '/sentry/watch/adjustParkSpace?spaceAdjust=' + spaceAdjust,
    method: 'post'
  });
};

//查询排队车辆
export const queryCarOutQueueRecords = () => {
  return $({
    url: '/sentry/watch/queryCarOutQueueRecords',
    method: 'post'
  })
};
// //获取剩余车位
// export const hasCarData = () => {
//   return $({
//     url: '/sentry/watch/getParkSpaceIdle',
//     method: 'get'
//   });
// };
//获取总车位
export const allCarData = () => {
  return $({
    url: '/sentry/park/getParkInfo',
    method: 'get'
  });
};
