<template>
  <div class="container">
    <down-data-search @form-search="searchDownData" @reset="resetParamsAndData" />
    <down-data-table ref="table" />  
  </div>
</template>

<script setup name="DownData">
import DownDataSearch from './down_data/DownDataSearch.vue';
import DownDataTable from './down_data/DownDataTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchDownData = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
<style lang="scss" scoped></style>
