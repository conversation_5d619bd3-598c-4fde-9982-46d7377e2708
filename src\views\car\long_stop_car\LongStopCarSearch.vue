<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="订单开始时间"
        end-placeholder="订单结束时间"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.gateway_id" placeholder="进入通道" clearable>
        <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.stop_car_type" placeholder="停车类型" multiple clearable>
        <el-option v-for="item in stopCarType" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.day_cnt" placeholder="滞留天数" /></form-search-item>
  </FormSearch>
</template>

<script name="LongStopCarSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onMounted } from 'vue';
import parkService from '@/service/park/ParkService';
import { useUser } from '@/stores/user';
import { getDefaultDateRange } from '@/utils/common';

const user = useUser();

const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    plate_no: undefined,
    stop_car_type: [],
    gateway_id: undefined,
    start_time: undefined,
    end_time: undefined,
    day_cnt: undefined
  },
  dateRange: []
});
const types = ref([]);
const stopCarType = ref([]);

onMounted(() => {
  // 数据初始化
  initSelects();
  form.dateRange = getDefaultDateRange(7);
  handleDataSearch();
});

const initSelects = () => {
  const param = [{ enum_key: 'stopCarType', enum_value: 'EnumStopCarType' }];
  commonService.findEnums('park', param).then((response) => {
    stopCarType.value = response.data.stopCarType;
  });

  const gatewayParam = { region_id: user.park_region_id };
  parkService.listAllGateway(gatewayParam).then((response) => {
    types.value = response;
  });
};

const handleDataSearch = () => {
  if (null !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }

  const param = {
    plate_no: form.queryParams.plate_no,
    stop_car_type: form.queryParams.stop_car_type,
    gateway_id: form.queryParams.gateway_id,
    start_time: form.queryParams.start_time,
    end_time: form.queryParams.end_time,
    day_cnt: form.queryParams.day_cnt === undefined ? 0 : form.queryParams.day_cnt
  };
  const query = Object.assign(param, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    plate_no: undefined,
    stop_car_type: [],
    gateway_id: undefined,
    start_time: undefined,
    end_time: undefined,
    day_cnt: undefined
  };
  const param = {
    plate_no: form.queryParams.plate_no,
    stop_car_type: form.queryParams.stop_car_type,
    gateway_id: form.queryParams.gateway_id,
    start_time: form.queryParams.start_time,
    end_time: form.queryParams.end_time,
    day_cnt: form.queryParams.day_cnt === undefined ? 0 : form.queryParams.day_cnt
  };

  emits('form-reset', param);
};
</script>
<style lang="scss" scoped></style>
