<template>
  <div class="frame-header">
    <div style="display: flex; align-items: center">
      <div class="logo-area">
        <img src="@/assets/logo.png" width="42" />
        <span class="title">{{ user.park_name }}</span>
        <span style="color: #fff">（Build: {{ version }}）</span>
      </div>
      <!-- 车道识别相机选择器  #开始 -->
      <div class="camera-select" v-if="showCameraSelect">
        <el-select v-model="selectedCamera" placeholder="选择车道识别相机" style="width: 150px" @change="handleCameraChange" size="small">
          <el-option v-for="item in cameraOptions" :key="item.video_id" :label="item.video_name" :value="item.video_id" />
        </el-select>
      </div>
      <!-- 车道识别相机选择器  #结束 -->
    </div>
    <div style="line-height: 48px; font-size: 14px; padding: 0px 8px; color: #fff; user-select: none" v-if="user.role === 1">
      本次上班时间：{{ user.on_time }}
      <!-- （<span>应收金额：{{ user.total_money }} 元</span>； <span>实收金额：{{ user.payed_money }} 元</span>） -->
    </div>
    <div class="info-area">
      <div class="onduty-info">
        <div class="onduty" v-if="useOndutyStore.isEscrow == 2 || useOndutyStore.isEscrow == 3">
          <div>托管状态: <span style="color: #95f202">云端托管中</span></div>
        </div>
      </div>
      <span class="header-btn" @click="handleExecuteShift" v-if="user.role === 1">交班</span>
      <span class="header-btn" @click="activeRouteTab({ path: '/watch/currentShiftDetail' })" v-if="showCurrentShiftDetail">本班统计</span>
      <el-dropdown>
        <div class="user">
          <el-icon style="color: #fff; font-size: 20px; position: relative; top: 5%">
            <User />
          </el-icon>
          <span style="position: relative; top: -5%">&ensp;{{ user.name }}</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="changePasswd" :icon="Lock">&ensp;修改密码</el-dropdown-item>
            <el-dropdown-item @click="logout" :icon="SwitchButton">&ensp;退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
  <!-- 修改密码 -->
  <el-dialog title="修改密码" v-model="changePasswdDialogVisible" :close-on-click-modal="false" width="30%">
    <el-form ref="loginForm" :model="data.form" label-width="120px" :rules="data.rules">
      <el-form-item label="原密码" prop="old_passwd">
        <el-input v-model="data.form.old_passwd" type="password" />
      </el-form-item>
      <el-form-item label="新密码" prop="new_passwd">
        <el-input v-model="data.form.new_passwd" type="password" />
      </el-form-item>
      <el-form-item label="重复新密码" prop="repeat_passwd">
        <el-input v-model="data.form.repeat_passwd" type="password" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="changePasswdDialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitChangePasswd(loginForm)">确 定</el-button>
    </template>
  </el-dialog>

  <!-- 交接班 -->
  <el-dialog v-model="executeShift.dialogVisible" title="交接班" width="400px" :show-close="false" :close-on-click-modal="false">
    <el-form ref="executeShiftFormRef" :model="executeShift.form" :rules="executeShift.rules" label-position="top">
      <el-form-item prop="curr_username" label="当班人账号">
        <el-input v-model="executeShift.form.curr_username" placeholder="请输入" style="width: 100%" readonly />
      </el-form-item>
      <el-form-item prop="curr_password" label="当班人密码">
        <el-input v-model="executeShift.form.curr_password" placeholder="请输入" type="password" style="width: 100%" />
      </el-form-item>
      <el-form-item prop="next_username" label="接班人账号">
        <el-select v-model="executeShift.form.next_username" placeholder="请选择" style="width: 100%" clearable>
          <el-option v-for="item in nextUsernameList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="next_password" label="接班人密码">
        <el-input v-model="executeShift.form.next_password" placeholder="请输入" type="password" style="width: 100%" />
      </el-form-item>
    </el-form>
    <div style="margin-top: 30px; text-align: center">
      <el-space>
        <el-button @click="cancelExecuteShift(executeShiftFormRef)">取 消</el-button>
        <el-button type="primary" @click="confirmExecuteShift(executeShiftFormRef)">确 定</el-button>
      </el-space>
    </div>
  </el-dialog>
</template>

<script name="FrameHeader" setup>
import { getCameraOptions } from '@/api/lane_monitor/laneMonitorApi';
import project from '@/project.json';
import router from '@/router';
import homeService from '@/service/home/<USER>';
import loginService from '@/service/login/LoginService';
import { useMenu } from '@/stores/menu';
import { useSetting } from '@/stores/setting';
import { useTab } from '@/stores/tab';
import { useUser } from '@/stores/user';
import { activeRouteTab } from '@/utils/tabKit';
import { Lock, SwitchButton } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import ondutyStore from '@/stores/onduty.js'; //引入值班信息仓库
const useOndutyStore = ondutyStore(); //使用仓库

// 系统版本信息
const version = project.version;

const user = useUser();
const setting = useSetting();
const menu = useMenu();
const tab = useTab();

const showCurrentShiftDetail = ref(false);
const page_work_statistics = user.page_work_statistics?.split(',').map((item) => parseInt(item, 10)) || [];
showCurrentShiftDetail.value = page_work_statistics.includes(user.role);

const loginForm = ref();
const data = reactive({
  form: {
    user_id: undefined,
    old_passwd: undefined,
    new_passwd: undefined,
    repeat_passwd: undefined
  },
  rules: {
    old_passwd: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
    new_passwd: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
    repeat_passwd: [{ required: true, message: '请重复输入新密码', trigger: 'blur' }]
  }
});
const changePasswdDialogVisible = ref(false);

const interval = ref();
onMounted(() => {
  // if (user.role === 1) {
  //   interval.value = setInterval(() => {
  //     statParkStatus();
  //   }, 1000 * 60 * 5);
  // }
  // fetchCameraOptions();
});

onUnmounted(() => {
  if (interval.value) {
    clearInterval(interval.value);
  }
});

// 是否展示车道识别相机选择器
const showCameraSelect = ref(false);
// 当前选择的车道识别相机
const selectedCamera = ref(setting.lane_monitor);
// 车道识别相机列表
const cameraOptions = ref([]);
/**
 * @description 选择车道识别相机
 */
const handleCameraChange = (val) => {
  setting.setSelected(val);
};
/**
 * @description 获取车道识别相机列表
 */
const fetchCameraOptions = async () => {
  const { data } = await getCameraOptions();
  cameraOptions.value = data;
  if (!selectedCamera.value && data.length) {
    selectedCamera.value = data[0];
    setting.setSelected(data[0].video_id);
  }
};
/**
 * @description 监听路由变化 决定是否展示车道识别相机选择器
 */
watch(
  () => router.currentRoute.value,
  (newValue) => {
    if (newValue.path.indexOf('lane_monitor') === -1) {
      showCameraSelect.value = false;
    } else {
      showCameraSelect.value = true;
    }
  },
  { immediate: true }
);

// 退出登录
const logout = () => {
  loginService.logout().then((response) => {
    ElMessage({
      message: '退出登录成功',
      type: 'success'
    });

    user.removeUser();
    menu.unloadMenu();
    tab.unloadTab();

    router.push('/login');
  });
};

// 修改密码
const changePasswd = () => {
  data.form = {
    user_id: undefined,
    old_passwd: undefined,
    new_passwd: undefined,
    repeat_passwd: undefined
  };
  changePasswdDialogVisible.value = true;
};

// 提交修改密码
const submitChangePasswd = (form) => {
  form.validate().then(() => {
    data.form.user_id = user.user_id;

    loginService.changePasswd(data.form).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: '密码修改成功，正在跳转登录...',
          type: 'success'
        });
        changePasswdDialogVisible.value = false;

        user.removeToken();
        router.push('/login');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

// 交接班
const executeShiftFormRef = ref();
const executeShift = reactive({
  dialogVisible: false,
  form: {
    shift_id: undefined,
    park_code: undefined,
    terminal_code: undefined,
    terminal_auth_code: undefined,
    sentry_id: undefined,
    curr_username: undefined,
    curr_password: undefined,
    next_username: undefined,
    next_password: undefined
  },
  rules: {
    curr_password: [{ required: true, message: '请输入当班人密码', trigger: 'blur' }],
    next_username: [{ required: true, message: '请选择接班人账号', trigger: 'blur' }],
    next_password: [{ required: true, message: '请输入接班人密码', trigger: 'blur' }]
  }
});

const nextUsernameList = ref([]);
// 打开交接班对话框
const handleExecuteShift = () => {
  executeShift.form = {
    shift_id: user.shift_handover_record_id,
    park_code: setting.park_code,
    terminal_code: setting.terminal_code,
    terminal_auth_code: setting.terminal_auth_code,
    sentry_id: setting.sentry_id,
    curr_username: user.username,
    curr_password: undefined,
    next_username: undefined,
    next_password: undefined
  };
  homeService.getShift({ page: 1, limit: 99 }).then((res) => {
    if (res.success) {
      nextUsernameList.value = res.data.rows.map((item) => {
        return {
          value: item.username,
          label: item.name
        };
      });
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
  executeShift.dialogVisible = true;
};
// 交接班-确定
const confirmExecuteShift = (formRef) => {
  formRef.validate().then(() => {
    homeService.executeShift(executeShift.form).then((res) => {
      if (res.success) {
        ElMessage({
          message: '交班成功',
          type: 'success'
        });

        const data = res.data;

        user.user_id = data.user_detail.user_id;
        user.name = data.user_detail.name;
        user.username = data.user_detail.username;
        user.mobile = data.user_detail.mobile;
        user.state = data.user_detail.state;
        user.token = data.token;
        user.shift_handover_record_id = data.shift_handover_record_id;

        //检查班次信息;
        const checkShiftParam = {
          park_id: user.park_id,
          park_region_id: user.park_region_id,
          current_user_id: data.user_detail.user_id,
          sentry_id: setting.sentry_id
        };
        loginService.checkShift(checkShiftParam).then((res) => {
          if (res.success) {
            const data = res.data;
            user.on_time = data.on_time;
          }
        });

        executeShift.dialogVisible = false;
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
};
// 交接班-取消
const cancelExecuteShift = (formRef) => {
  if (!formRef) return;
  formRef.resetFields();

  executeShift.dialogVisible = false;
};

// 刷新应收和实收、空闲车位
// const statParkStatus = () => {
//   const shift_record_id = user.shift_handover_record_id;
//   watchService.statParkStatus(shift_record_id).then((res) => {
//     if (res.success) {
//       user.total_money = res.data.total_money;
//       user.payed_money = res.data.payed_money;
//       user.free_space = res.data.free_space;
//       user.space_adjust = res.data.space_adjust;
//     } else {
//       ElMessage({
//         dangerouslyUseHTMLString: true,
//         message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
//         type: 'error'
//       });
//     }
//   });
// };
</script>

<style lang="scss" scoped>
.frame-header {
  width: 100%;
  height: 48px;
  background: #00474f;
  box-shadow: 0 2px 8px #f0f1f2;
  display: flex;
  justify-content: space-between;
  user-select: none;

  .logo-area {
    height: 48px;
    text-align: center;
    background: #00474f;
    margin-left: 25px;

    img {
      position: relative;
      top: 15%;
    }

    .title {
      color: rgba(255, 255, 255, 0.95);
      font-size: 18px;
      font-weight: 600;
      margin-left: 20px;
    }
  }

  .info-area {
    height: 48px;

    img {
      position: relative;
      top: 10%;
    }

    .user {
      line-height: 52px;
      color: rgba(255, 255, 255, 0.95);
      margin-right: 10px;
      padding: 0 10px;
    }
  }
}

.header-btn {
  display: inline-block;
  color: rgba(255, 255, 255, 0.95);
  line-height: 48px;
  font-size: 14px;
  padding: 0px 8px;
  cursor: pointer;
}

.header-btn:hover {
  color: rgba(255, 255, 255, 0.85);
}

.el-dropdown {
  height: 48px;
}

.el-tooltip__trigger:hover {
  background: #006d75;
}

.el-dropdown-menu {
  padding: 3px 6px;
}

.label {
  color: rgba(255, 255, 255, 0.8);
}
.onduty {
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.onduty-info {
  display: inline-block;
  margin-right: 20px;
}
</style>
