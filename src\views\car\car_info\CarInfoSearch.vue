<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
  </FormSearch>
</template>

<script name="CarInfoSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive } from 'vue';
import { ElMessage } from 'element-plus';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    plate_no: ''
  }
});

const handleDataSearch = () => {
  if (form.queryParams.plate_no == '') {
    ElMessage({
      message: '请输入车牌号',
      type: 'error'
    });
  } else {
    const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
  }
};
const handleAllReset = () => {
  form.queryParams = {
    plate_no: null
  };
  emits('form-reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
