import * as watch from '@/api/watch/Watch';

/**
 * @description 岗亭值守
 * <AUTHOR>
 * @date 2022/10/18
 */
export default {
  /**
   * 查询通道数据
   */
  querySentryGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.querySentryGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 切换出入口
   */
  changeInOut(params) {
    return new Promise((resolve, reject) => {
      try {
        watch.changeInOut(params).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询最近一条入场记录
   */
  queryLastParkInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.queryLastParkInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 按入场时间生成当前需缴费订单
   */
  getParkOrder(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.getParkOrder(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 重新选择计费方式
   */
  refreshOrder(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.refreshOrder(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 切换费率
   */
  switchCarType(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.switchCarType(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修正入场记录
   */
  updateCarInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.updateCarInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 车修改车牌号
   */
  modifyPlateNo(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.modifyPlateNo(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 生成无牌车号码
   */
  generateCarNoPlateNo(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.generateCarNoPlateNo(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 取消订单中所有的冻结的优免券
   */
  cancelFreeCoupon(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.cancelFreeCoupon(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询入场记录
   */
  queryParkInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.queryParkInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询出场记录
   */
  queryParkOutRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.queryParkOutRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询车牌可用优惠券
   */
  queryAvailableCoupons(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.queryAvailableCoupons(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 使用优惠券
   */
  useCoupon(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.useCoupon(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 收费放行
   */
  chargeAndPass(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.chargeAndPass(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 特殊放行
   */
  specialPass(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.specialPass(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 手动抓拍
   */
  manualCapture(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.manualCapture(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 取消放行
   */
  cancelPass(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.cancelPass(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 抬杆测试
   */
  liftRoll(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.liftRoll(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 车队模式
   */
  fleetMode(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.fleetMode(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 关闭车队
   */
  fleetModeClose(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.fleetModeClose(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 一体机测试
   */
  testOneMachine(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.testOneMachine(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 手动匹配-最近入场记录
   */
  getTopNCarInRecordInPark(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.getTopNCarInRecordInPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 允许入场(入口)
   */
  manualPassIn(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.manualPassIn(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 手动补录
   * @param {*} data
   * @returns
   */
  manualRecordCarInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.manualRecordCarInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 统计车场的应收和实收、空闲车位
   * @param {*} data
   * @returns
   */
  statParkStatus(shiftRecordId) {
    return new Promise((resolve, reject) => {
      try {
        watch.statParkStatus(shiftRecordId).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 校准车位
   * @param {车位偏离变量} spaceAdjust
   */
  adjustParkSpace(spaceAdjust) {
    return new Promise((resolve, reject) => {
      try {
        watch.adjustParkSpace(spaceAdjust).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
 * 校准车位
 * @param {车位偏离变量} spaceAdjust
 */
  queryCarOutQueueRecords(spaceAdjust) {
    return new Promise((resolve, reject) => {
      try {
        watch.queryCarOutQueueRecords(spaceAdjust).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
    }
};
