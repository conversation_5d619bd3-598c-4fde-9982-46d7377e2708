/* 基本样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none !important;
}

html,
body,
#app {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, segoe ui, Roboto, helvetica neue, <PERSON>l, noto sans, sans-serif, apple color emoji, segoe ui emoji,
    segoe ui symbol, noto color emoji;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;
  background-color: var(--ba-bg-color);
  font-size: 14px;
  overflow: hidden;
  position: relative;
}

/*
 * 表格
 */
.table {
  margin-top: 10px;
  margin-bottom: 10px;
}

.table-pagination {
  margin-top: 8px;
  float: right;
  padding-bottom: 10px;
}

/*
 * 操作按钮两端对齐
 */
.opers {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
}

/* 
 * 滚动条
 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-thumb {
  border-radius: 2px;
  background-color: #afafaf;
}

::-webkit-scrollbar-track {
  background-color: #fafafa;
}
