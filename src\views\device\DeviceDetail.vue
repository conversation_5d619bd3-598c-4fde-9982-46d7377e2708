<template>
  <div class="container">
    <el-card shadow="hover">
      <el-form :model="data.deviceDetail" label-width="170px">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="车场">
              {{ data.deviceDetail.park_name }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备名称">
              {{ data.deviceDetail.dev_device_name }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备序列号">
              {{ data.deviceDetail.sn }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="设备类型"> {{ data.deviceDetail.type_desc }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备型号">{{ data.deviceDetail.model }} </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备厂家"> {{ data.deviceDetail.dev_factory_name }} </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="区域">
              {{ data.deviceDetail.park_region_name }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="通道">
              {{ data.deviceDetail.gateway_name }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备IP">
              {{ data.deviceDetail.ip }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="控制端口">
              {{ data.deviceDetail.port }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备状态">
              {{ data.deviceDetail.state_desc }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="在线状态">
              {{ data.deviceDetail.online_desc }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="最后一次心跳时间">
              {{ data.deviceDetail.last_heart_time }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script name="DeviceDetail" setup>
import deviceService from '@/service/device/DeviceService';
import { reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRoute } from 'vue-router';

const route = useRoute();
const data = reactive({
  deviceDetail: {}
});

onMounted(() => {
  const id = route.query.id;
  getDeviceStateDetail(id);
});

const getDeviceStateDetail = (id) => {
  const parms = { gateway_device_id: id };
  deviceService.getDeviceStateDetail(parms).then((response) => {
    if (response.success === true) {
      data.deviceDetail = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
</script>

<style lang="scss" scoped></style>
