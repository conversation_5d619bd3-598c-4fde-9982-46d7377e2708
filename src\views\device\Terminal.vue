<template>
  <div class="container">
    <terminal-search @form-search="searchTerminal" @form-reset="resetParamsAndData" />
    <terminal-table ref="table" />  
  </div>
</template>

<script name="Terminal" setup>
import TerminalSearch from './terminal/TerminalSearch.vue';
import TerminalTable from './terminal/TerminalTable.vue';
import { ref } from 'vue';

const table = ref(null);

const searchTerminal = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
<style lang="scss" scoped></style>
