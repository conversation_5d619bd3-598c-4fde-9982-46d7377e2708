<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="car_in_biz_no" label="入场业务流水号" align="center" />
        <el-table-column prop="park_name" label="车场名" align="center" />
        <el-table-column prop="park_region_name" label="区域名称" align="center" />
        <el-table-column prop="gateway_name" label="通道名称" align="center" />
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="car_type_desc" label="车辆类型" align="center" />
        <el-table-column prop="car_color" label="车辆颜色" align="center" />\
        <el-table-column prop="out_state_desc" label="出场状态" align="center" />
        <el-table-column label="入场图片" align="center" min-width="100">
          <template v-slot="scope">
            <el-image
              v-if="scope.row.car_photo_url !== '' && scope.row.car_photo_url !== null && scope.row.car_photo_url !== undefined"
              style="width: 100px; height: 100px"
              :src="scope.row.car_photo_url"
              :fit="fit"
              @click="showImage(scope.row.car_photo_url)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="in_type_desc" label="入场类型" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog v-model="imageDialogVisible" title="入场图片">
      <img w-full :src="data.dialogImageUrl" style="max-width: 100%; max-height: 100%" alt="Preview Image" />
    </el-dialog>
  </el-card>
</template>

<script name="LongStopCarTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import carService from '@/service/car/CarService';
import { getDefaultDateRange } from '@/utils/common';

const imageDialogVisible = ref(false);
const tableData = ref([]);
const total = ref(0);
const loading = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    start_time: '',
    end_time: '',
    day_cnt: 0
  },
  dateRange: [],
  dialogImageUrl: ''
});

const getList = (params) => {
  loading.value = true;
  data.queryParams.start_time = params.start_time;
  data.queryParams.end_time = params.end_time;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  carService.pageCarInRecordNotOut(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const showImage = (val) => {
  data.dialogImageUrl = val;
  imageDialogVisible.value = true;
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
