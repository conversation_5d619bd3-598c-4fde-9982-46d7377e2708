<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleDetail(scope.row.id)"> 详情 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" align="center" min-width="140" />
        <el-table-column prop="name" label="计费模型名称" align="center" min-width="140" />
        <el-table-column prop="valid_start_time" label="开始时间" align="center" min-width="160" />
        <el-table-column prop="valid_end_time" label="结束时间" align="center" min-width="100" />
        <el-table-column prop="car_type_desc" label="适合车型" align="center" min-width="100" />
        <el-table-column prop="created_at" label="创建时间" align="center" min-width="100" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="FeeModelTable" setup>
import { reactive, ref, onMounted } from 'vue';
import parkService from '@/service/park/ParkService';
import { activeRouteTab } from '@/utils/tabKit';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {
    name: ''
  }
});

onMounted(() => {
  // 数据初始化
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  parkService.pageFeeModel(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleDetail = (id) => {
  activeRouteTab({
    path: '/park/feeModelDetail',
    query: {
      id: id
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
