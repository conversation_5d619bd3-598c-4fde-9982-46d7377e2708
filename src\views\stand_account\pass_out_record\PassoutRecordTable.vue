<template>
    <el-card class="table" shadow="never">
        <div ref="table">
            <el-table :data="tableData" border v-loading="loading">
                <el-table-column type="selection" style="text-align: center" width="40" />
                <el-table-column prop="id" label="ID" align="center" />
                <el-table-column prop="car_out_biz_no" label="出场记录流水号" align="center" min-width="140" />
                <el-table-column prop="park_name" label="车场名" align="center" min-width="160" />
                <el-table-column prop="park_region_name" label="区域名" align="center" min-width="100" />
                <el-table-column prop="gateway_name" label="通道名" align="center" min-width="140" />
                <el-table-column prop="plate_no" label="车牌号" align="center" min-width="100" />
                <el-table-column label="入场图片" align="center" min-width="100">
                    <template v-slot="scope">
                        <el-image
                                v-if="scope.row.car_photo_url !== '' && scope.row.car_in_photo_url !== null && scope.row.car_in_photo_url !== undefined"
                                style="width: 100px; height: 100px"
                                :src="scope.row.car_in_photo_url"
                                :fit="fit"
                                @click="showImage(scope.row.car_in_photo_url)"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="出场图片" align="center" min-width="100">
                    <template v-slot="scope">
                        <el-image
                                v-if="scope.row.car_photo_url !== '' && scope.row.car_photo_url !== null && scope.row.car_photo_url !== undefined"
                                style="width: 100px; height: 100px"
                                :src="scope.row.car_photo_url"
                                :fit="fit"
                                @click="showImage(scope.row.car_photo_url)"
                        />
                    </template>
                </el-table-column>
                <el-table-column prop="out_time" label="出场时间" align="center" min-width="160" />
                <el-table-column prop="car_type_desc" label="车辆类型" align="center" min-width="100" />
                <el-table-column prop="stop_car_type_desc" label="停车类型" align="center" min-width="100" />
                <el-table-column prop="car_color" label="颜色" align="center" min-width="100" />
                <el-table-column prop="out_type_desc" label="出场状态" align="center" min-width="100" />
                <el-table-column prop="out_reason_desc" label="出场原因" align="center" min-width="100" />
                <el-table-column prop="in_type_desc" label="入场类型" align="center" min-width="100" />
                <el-table-column prop="car_in_record_id" label="入场记录ID" align="center" min-width="150" />
                <el-table-column prop="duration" label="停留时长" align="center" min-width="100" />
                <el-table-column prop="out_order_no" label="出场的最后一个订单号" align="center" min-width="250" />
                <el-table-column prop="car_in_biz_no" label="入场流水号" align="center" min-width="150" />
                <el-table-column prop="has_in_record" label="是否有入场记录" align="center" min-width="200">
                    <template v-slot="scope">
                        <span v-if="scope.row.has_in_record == 1">有</span>
                        <span v-if="scope.row.has_in_record == 0">无</span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    :current-page="data.queryParams.page"
                    :page-sizes="[10, 30, 50, 100]"
                    :page-size="data.queryParams.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    class="table-pagination"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
            />
        </div>

        <el-dialog v-model="imageDialogVisible" title="出场图片">
            <img w-full :src="data.dialogImageUrl" style="max-width: 100%; max-height: 100%" alt="Preview Image" />
        </el-dialog>
    </el-card>
</template>

<script name="PassoutRecordTable" setup>
    import { reactive, ref, onMounted } from 'vue';
    import { ElMessage } from 'element-plus';
    import stantAccountService from '@/service/stand_account/StantAccountService';

    const loading = ref(false);
    const tableData = ref([]);
    const total = ref(0);
    const imageDialogVisible = ref(false);
    const data = reactive({
        queryParams: {
            page: 1,
            limit: 30,
            start_time: '',
            end_time: ''
        },
        dateRange: [],
        dialogImageUrl: ''
    });

    const getList = (params) => {
        loading.value = true;
        data.queryParams.start_time = params.start_time;
        data.queryParams.end_time = params.end_time;
        params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
        params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
        data.queryParams = params;
        stantAccountService.pageParkOutRecord(params).then((response) => {
            if (response.success === true) {
                tableData.value = response.data.rows;
                total.value = parseInt(response.data.total);
                loading.value = false;
            } else {
                ElMessage({
                    message: response.detail_message != '' ? response.detail_message : response.message,
                    type: 'error'
                });
                loading.value = false;
            }
        });
    };

    const handleSizeChange = (val) => {
        data.queryParams.limit = val;
        getList(data.queryParams);
    };
    const handleCurrentChange = (val) => {
        data.queryParams.page = val;
        getList(data.queryParams);
    };
    const showImage = (val) => {
        data.dialogImageUrl = val;
        imageDialogVisible.value = true;
    };
    defineExpose({
        getList
    });
</script>
<style lang="scss" scoped></style>
