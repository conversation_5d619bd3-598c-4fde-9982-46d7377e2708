import { defineStore } from 'pinia'
export default defineStore('onduty', {
    actions: {},
    state() {
        return {
            isEscrow: 2,
            endDialogVisible: false,
            cloudData: '',
            phoneDataOne: "",
            phoneDataTwo: "",
            phoneDataThree: "",
            duty_phone: ''
        }
    },
    getters: {},
    persist: {
        path: ['isEscrow', "endDialogVisible", "cloudData", "duty_phone"]
    }
})