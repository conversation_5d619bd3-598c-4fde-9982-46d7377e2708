<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="logger_name" label="日志记录器" align="center" min-width="80" />
        <el-table-column prop="level" label="级别" align="center" min-width="50" />
        <el-table-column prop="thread_name" label="线程名" align="center" min-width="50" />
        <el-table-column prop="created_at" label="创建时间" align="center" min-width="60" />
        <el-table-column prop="formatted_message" label="消息" align="center" min-width="210" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="SysLogTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import logService from '@/service/log/LogService';
import { getDefaultDateTimeRange } from '@/utils/common';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    start_time: '',
    end_time: ''
  },
  dateRange: []
});
onMounted(() => {
  data.dateRange = getDefaultDateTimeRange(1);
  getList(data.queryParams);
});
console.log(data.dateRange,'data.dateRange')
console.log(data.queryParams.start_time,'data.queryParams.start_time')
const getList = (params) => {
  loading.value = true;
  params.start_time === '' ? (params.start_time = data.dateRange[0]) : (params.start_time = data.queryParams.start_time);
  params.end_time === '' ? (params.end_time = data.dateRange[1]) : (params.end_time = data.queryParams.end_time);
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  logService.pageSysLog(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
