<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="名称" /></form-search-item>
  </FormSearch>
</template>

<script name="FeeModelSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    name: ''
  }
});

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams);
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    name: ''
  };
  emits('form-reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
